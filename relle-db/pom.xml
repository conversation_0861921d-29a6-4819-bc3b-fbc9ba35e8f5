<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>relle-db</artifactId>
    <packaging>jar</packaging>
    <version>0.1</version>
    <name>Relle DB Module</name>
    <description>Database module for Relle Application</description>

    <parent>
        <groupId>com.relle</groupId>
        <artifactId>relle-parent</artifactId>
        <version>0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <dependencies>
        <!-- Module-specific dependencies -->
        <!-- JPA and Hibernate dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <!--Mysql database driver-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sun.mail</groupId>
                    <artifactId>javax.mail</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Explicitly add these dependencies to resolve "used undeclared dependencies" warning -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        
        <!-- Lombok for reducing boilerplate code -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <classifier>exec</classifier>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
