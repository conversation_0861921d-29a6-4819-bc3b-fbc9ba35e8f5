package com.relle.mbg;

import org.mybatis.generator.api.CommentGenerator;
import org.mybatis.generator.api.IntrospectedColumn;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.dom.java.CompilationUnit;
import org.mybatis.generator.api.dom.java.Field;
import org.mybatis.generator.api.dom.java.FullyQualifiedJavaType;
import org.mybatis.generator.api.dom.java.InnerClass;
import org.mybatis.generator.api.dom.java.InnerEnum;
import org.mybatis.generator.api.dom.java.Method;
import org.mybatis.generator.api.dom.java.Parameter;
import org.mybatis.generator.api.dom.java.TopLevelClass;
import org.mybatis.generator.api.dom.xml.XmlElement;
import org.mybatis.generator.internal.util.StringUtility;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Properties;

public class MyCommentGenerator implements CommentGenerator, Serializable {

    private Properties properties = new Properties();
    /**
     * 抑制日期 默认false：不抑制
     */
    private boolean suppressDate = false;
    /**
     * 抑制注释 默认false：不抑制
     */
    private boolean suppressAllComments = false;

    /**
     * 显示数据库comments 默认false：不显示
     */
    private boolean addRemarkComments = false;
    /**
     * 日期格式
     */
    private DateTimeFormatter dateFormat;
    private DateTimeFormatter datetimeFormat;

    public MyCommentGenerator() {
        super();
        this.dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        this.datetimeFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 读取配置文件
     *
     * @param properties
     */
    @Override
    public void addConfigurationProperties(Properties properties) {
        this.properties.putAll(properties);
        this.suppressDate = StringUtility.isTrue(properties.getProperty("suppressDate"));
        this.suppressAllComments = StringUtility.isTrue(properties.getProperty("suppressAllComments"));
        this.addRemarkComments = StringUtility.isTrue(properties.getProperty("addRemarkComments"));
        String dateFormatString = properties.getProperty("dateFormat");
        if (StringUtility.stringHasValue(dateFormatString)) {
            this.dateFormat = DateTimeFormatter.ofPattern(dateFormatString);
        }

    }

    /**
     * 创建的数据表对应的类添加的注释
     *
     * /**
     * * @author: create by NiceHang
     * * @version: v1.0.1
     * * @date:2019/11/27 10:16
     * *
     *
     * @param topLevelClass
     * @param introspectedTable
     */
    @Override
    public void addModelClassComment(TopLevelClass topLevelClass, IntrospectedTable introspectedTable) {
        topLevelClass.addImportedType("com.fasterxml.jackson.annotation.JsonFormat");
        if (!this.suppressAllComments) {
            topLevelClass.addJavaDocLine("/**");
            topLevelClass.addJavaDocLine(" * <AUTHOR> by sj");
            topLevelClass.addJavaDocLine(" * @version v1.0.1 ");
            topLevelClass.addJavaDocLine(" * @Database table name is " + introspectedTable.getFullyQualifiedTable());
            topLevelClass.addJavaDocLine(" * @create " + datetimeFormat.format(LocalDateTime.now()));
            topLevelClass.addJavaDocLine(" */");
        }
    }

    /**
     * <p>
     * 生成xx.java文件（model）属性的注释
     * </p>
     *
     * @param field
     * @param introspectedTable
     * @param introspectedColumn
     */
    @Override
    public void addFieldComment(Field field, IntrospectedTable introspectedTable,
            IntrospectedColumn introspectedColumn) {
        if (!this.suppressAllComments) {
            // 注释开始的地方
            field.addJavaDocLine("/**");
            String remarks = introspectedColumn.getRemarks();
            // 开启注释，并且数据库中comment有值
            if (this.addRemarkComments && StringUtility.stringHasValue(remarks)) {
                // 通过换行符分割 System.getProperty("line.separator")：换行符 ，屏蔽了 Windows和Linux的区别
                String[] remarkLines = remarks.split(System.getProperty("line.separator"));
                int length = remarkLines.length;
                // 如果有多行，就换行显示
                for (int i = 0; i < length; i++) {
                    String remarkLine = remarkLines[i];
                    field.addJavaDocLine(" * " + remarkLine);
                }
            }
            // 注释结束
            field.addJavaDocLine(" */");
            if (introspectedColumn.isJDBCDateColumn()) {
                field.addAnnotation("@JsonFormat(pattern = \"yyyy-MM-dd\", timezone = \"GMT+8\")");
            }

            if (introspectedColumn.isJDBCTimeColumn()) {
                field.addAnnotation("@JsonFormat(pattern = \"HH:mm\", timezone = \"GMT+8\")");
            }
            if (introspectedColumn.getFullyQualifiedJavaType().equals(FullyQualifiedJavaType.getDateInstance()) &&
                    ("DATETIME".equalsIgnoreCase(introspectedColumn.getJdbcTypeName())
                            || "TIMESTAMP".equalsIgnoreCase(introspectedColumn.getJdbcTypeName()))) {
                field.addAnnotation("@JsonFormat(pattern = \"yyyy-MM-dd HH:mm:ss\", timezone = \"GMT+8\")");
            }

        }
    }

    /**
     * xxxMapper接口和xxxExample类方法注解
     *
     * @param method
     * @param introspectedTable
     */
    @Override
    public void addGeneralMethodComment(Method method, IntrospectedTable introspectedTable) {
        if (!this.suppressAllComments) {
            method.addJavaDocLine("/**");
            // TODO 注释掉搜索名
            // method.addJavaDocLine(" * " + method.getName());
            List<Parameter> parameters = method.getParameters();
            // TODO 入参
            parameters.forEach(parameter -> method.addJavaDocLine(" * @param " + parameter.getName()));
            // TODO 出参如果有返回类型，添加@return
            String returnType = "void";
            if (!returnType.equals(method.getReturnType())) {
                method.addJavaDocLine(" * @return ");
            }
            method.addJavaDocLine(" */");
        }

    }

    /**
     * 数据库对应实体类的Getter方法注解
     *
     * @param method
     * @param introspectedTable
     * @param introspectedColumn
     */
    @Override
    public void addGetterComment(Method method, IntrospectedTable introspectedTable,
            IntrospectedColumn introspectedColumn) {

    }

    /**
     * 数据库对应实体类的Setter方法注解
     *
     * @param method
     * @param introspectedTable
     * @param introspectedColumn
     */
    @Override
    public void addSetterComment(Method method, IntrospectedTable introspectedTable,
            IntrospectedColumn introspectedColumn) {

    }

    /**
     * 生成xxMapper.XML文件的注释
     *
     * @param xmlElement
     */
    @Override
    public void addComment(XmlElement xmlElement) {
    }

    @Override
    public void addJavaFileComment(CompilationUnit compilationUnit) {
    }

    @Override
    public void addRootComment(XmlElement rootElement) {
    }

    @Override
    public void addFieldComment(Field field, IntrospectedTable introspectedTable) {

    }

    @Override
    public void addEnumComment(InnerEnum innerEnum, IntrospectedTable introspectedTable) {

    }

    @Override
    public void addClassComment(InnerClass innerClass, IntrospectedTable introspectedTable) {

    }

    @Override
    public void addClassComment(InnerClass innerClass, IntrospectedTable introspectedTable, boolean markAsDoNotDelete) {

    }
}