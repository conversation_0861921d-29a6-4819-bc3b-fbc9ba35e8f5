package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;

public class AppCouponGive implements Serializable {
    /**
     * 优惠券赠送记录自增id
     */
    private Long id;

    /**
     */
    private Long couponNo;

    /**
     * 优惠券号
     */
    private String couponId;

    /**
     * 优惠券名称
     */
    private String couponReceiveId;

    /**
     */
    private String couponName;

    /**
     * 优惠券类型 
     */
    private String giveUserId;

    /**
     */
    private String giveUserUnionid;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime giveTime;

    /**
     * 优惠券发放总数，如果是0，则是无限量
     */
    private String receiveUserUnionid;

    /**
     */
    private String receiveUserId;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receiveTime;

    /**
     * 赠送状态，1赠送中 2已领取 4已退回
     */
    private Byte giveStatus;

    /**
     * 创建人：unionid
     */
    private String createBy;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新人：unionid
     */
    private String updateBy;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除，0未删除，1已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(Long couponNo) {
        this.couponNo = couponNo;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponReceiveId() {
        return couponReceiveId;
    }

    public void setCouponReceiveId(String couponReceiveId) {
        this.couponReceiveId = couponReceiveId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getGiveUserId() {
        return giveUserId;
    }

    public void setGiveUserId(String giveUserId) {
        this.giveUserId = giveUserId;
    }

    public String getGiveUserUnionid() {
        return giveUserUnionid;
    }

    public void setGiveUserUnionid(String giveUserUnionid) {
        this.giveUserUnionid = giveUserUnionid;
    }

    public LocalDateTime getGiveTime() {
        return giveTime;
    }

    public void setGiveTime(LocalDateTime giveTime) {
        this.giveTime = giveTime;
    }

    public String getReceiveUserUnionid() {
        return receiveUserUnionid;
    }

    public void setReceiveUserUnionid(String receiveUserUnionid) {
        this.receiveUserUnionid = receiveUserUnionid;
    }

    public String getReceiveUserId() {
        return receiveUserId;
    }

    public void setReceiveUserId(String receiveUserId) {
        this.receiveUserId = receiveUserId;
    }

    public LocalDateTime getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(LocalDateTime receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Byte getGiveStatus() {
        return giveStatus;
    }

    public void setGiveStatus(Byte giveStatus) {
        this.giveStatus = giveStatus;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", couponNo=").append(couponNo);
        sb.append(", couponId=").append(couponId);
        sb.append(", couponReceiveId=").append(couponReceiveId);
        sb.append(", couponName=").append(couponName);
        sb.append(", giveUserId=").append(giveUserId);
        sb.append(", giveUserUnionid=").append(giveUserUnionid);
        sb.append(", giveTime=").append(giveTime);
        sb.append(", receiveUserUnionid=").append(receiveUserUnionid);
        sb.append(", receiveUserId=").append(receiveUserId);
        sb.append(", receiveTime=").append(receiveTime);
        sb.append(", giveStatus=").append(giveStatus);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}