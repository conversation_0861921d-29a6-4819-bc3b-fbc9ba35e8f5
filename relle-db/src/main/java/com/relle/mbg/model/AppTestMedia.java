package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_test_media
 * @create 2023-12-07 17:21:51
 */
public class AppTestMedia implements Serializable {
    /**
     * 媒体内容自增id
     */
    private Long id;

    /**
     * 媒体文件名（不带后缀名）
     */
    private String mediaName;

    /**
     * 媒体文件类型
     */
    private String mediaType;

    /**
     * 文件大小
     */
    private Long mediaSize;

    /**
     * 文件存储路径
     */
    private String mediaSrc;

    /**
     */
    private String thumbnailSrc;

    /**
     * 已删除，正常，已隐藏
     */
    private Byte mediaStatus;

    /**
     * 文件校验，避免重复存储。
     */
    private String md5Sum;

    /**
     * 显示名称，相同文件可显示名称不同
     */
    private String mediaShowname;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String createBy;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String updateBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除，默认为0未删除，1已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMediaName() {
        return mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public String getMediaType() {
        return mediaType;
    }

    public void setMediaType(String mediaType) {
        this.mediaType = mediaType;
    }

    public Long getMediaSize() {
        return mediaSize;
    }

    public void setMediaSize(Long mediaSize) {
        this.mediaSize = mediaSize;
    }

    public String getMediaSrc() {
        return mediaSrc;
    }

    public void setMediaSrc(String mediaSrc) {
        this.mediaSrc = mediaSrc;
    }

    public String getThumbnailSrc() {
        return thumbnailSrc;
    }

    public void setThumbnailSrc(String thumbnailSrc) {
        this.thumbnailSrc = thumbnailSrc;
    }

    public Byte getMediaStatus() {
        return mediaStatus;
    }

    public void setMediaStatus(Byte mediaStatus) {
        this.mediaStatus = mediaStatus;
    }

    public String getMd5Sum() {
        return md5Sum;
    }

    public void setMd5Sum(String md5Sum) {
        this.md5Sum = md5Sum;
    }

    public String getMediaShowname() {
        return mediaShowname;
    }

    public void setMediaShowname(String mediaShowname) {
        this.mediaShowname = mediaShowname;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", mediaName=").append(mediaName);
        sb.append(", mediaType=").append(mediaType);
        sb.append(", mediaSize=").append(mediaSize);
        sb.append(", mediaSrc=").append(mediaSrc);
        sb.append(", thumbnailSrc=").append(thumbnailSrc);
        sb.append(", mediaStatus=").append(mediaStatus);
        sb.append(", md5Sum=").append(md5Sum);
        sb.append(", mediaShowname=").append(mediaShowname);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}