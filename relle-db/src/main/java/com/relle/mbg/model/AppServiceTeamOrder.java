package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AppServiceTeamOrder implements Serializable {
    /**
     */
    private Long id;

    /**
     */
    private String teamNo;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 用户小程序唯一id
     */
    private String unionid;

    /**
     * 门店编号，如SH0001
     */
    private String sotreId;

    /**
     * 订单金额->子订单汇总金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单原价->子订单原价格汇总
     */
    private BigDecimal originPrice;

    /**
     * 减免金额->子订单减免金额汇总
     */
    private BigDecimal reductionAmount;

    /**
     * 订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；-8:部分已退款；-16:部分已取消；
     */
    private Short orderStatus;

    /**
     * 订单生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String createBy;

    /**
     * 数据更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 更新者unionid-可为用户、店员、店长、系统等
     */
    private String updateBy;

    /**
     * 逻辑删除，默认为0未删除，1已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTeamNo() {
        return teamNo;
    }

    public void setTeamNo(String teamNo) {
        this.teamNo = teamNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getSotreId() {
        return sotreId;
    }

    public void setSotreId(String sotreId) {
        this.sotreId = sotreId;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(BigDecimal originPrice) {
        this.originPrice = originPrice;
    }

    public BigDecimal getReductionAmount() {
        return reductionAmount;
    }

    public void setReductionAmount(BigDecimal reductionAmount) {
        this.reductionAmount = reductionAmount;
    }

    public Short getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Short orderStatus) {
        this.orderStatus = orderStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", teamNo=").append(teamNo);
        sb.append(", orderId=").append(orderId);
        sb.append(", unionid=").append(unionid);
        sb.append(", sotreId=").append(sotreId);
        sb.append(", orderAmount=").append(orderAmount);
        sb.append(", originPrice=").append(originPrice);
        sb.append(", reductionAmount=").append(reductionAmount);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}