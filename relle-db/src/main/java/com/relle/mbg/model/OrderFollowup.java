package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * Order Followup Entity
 * Database table: order_followup
 */
public class OrderFollowup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    private Long id;

    /**
     * Order ID
     */
    private String orderId;

    /**
     * Service operation ID
     */
    private String serviceOperationId;

    /**
     * Followup interviewer
     */
    private String followupInterviewer;

    /**
     * Followup date
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date followupDate;

    /**
     * Rating by stars 1
     */
    private BigDecimal rateByStars1;

    /**
     * Rating by stars 2
     */
    private BigDecimal rateByStars2;

    /**
     * Rating by stars 3
     */
    private BigDecimal rateByStars3;

    /**
     * Rating by stars 4
     */
    private BigDecimal rateByStars4;

    /**
     * Rating by stars 5
     */
    private BigDecimal rateByStars5;

    /**
     * Followup remark
     */
    private String followupRemark;

    /**
     * Creator
     */
    private String createBy;

    /**
     * Create time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createOn;

    /**
     * Updater
     */
    private String updateBy;

    /**
     * Update time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateOn;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getServiceOperationId() {
        return serviceOperationId;
    }

    public void setServiceOperationId(String serviceOperationId) {
        this.serviceOperationId = serviceOperationId;
    }

    public String getFollowupInterviewer() {
        return followupInterviewer;
    }

    public void setFollowupInterviewer(String followupInterviewer) {
        this.followupInterviewer = followupInterviewer;
    }

    public Date getFollowupDate() {
        return followupDate;
    }

    public void setFollowupDate(Date followupDate) {
        this.followupDate = followupDate;
    }

    public BigDecimal getRateByStars1() {
        return rateByStars1;
    }

    public void setRateByStars1(BigDecimal rateByStars1) {
        this.rateByStars1 = rateByStars1;
    }

    public BigDecimal getRateByStars2() {
        return rateByStars2;
    }

    public void setRateByStars2(BigDecimal rateByStars2) {
        this.rateByStars2 = rateByStars2;
    }

    public BigDecimal getRateByStars3() {
        return rateByStars3;
    }

    public void setRateByStars3(BigDecimal rateByStars3) {
        this.rateByStars3 = rateByStars3;
    }

    public BigDecimal getRateByStars4() {
        return rateByStars4;
    }

    public void setRateByStars4(BigDecimal rateByStars4) {
        this.rateByStars4 = rateByStars4;
    }

    public BigDecimal getRateByStars5() {
        return rateByStars5;
    }

    public void setRateByStars5(BigDecimal rateByStars5) {
        this.rateByStars5 = rateByStars5;
    }

    public String getFollowupRemark() {
        return followupRemark;
    }

    public void setFollowupRemark(String followupRemark) {
        this.followupRemark = followupRemark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateOn() {
        return createOn;
    }

    public void setCreateOn(LocalDateTime createOn) {
        this.createOn = createOn;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateOn() {
        return updateOn;
    }

    public void setUpdateOn(LocalDateTime updateOn) {
        this.updateOn = updateOn;
    }

    @Override
    public String toString() {
        return "OrderFollowup{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", serviceOperationId='" + serviceOperationId + '\'' +
                ", followupInterviewer='" + followupInterviewer + '\'' +
                ", followupDate=" + followupDate +
                ", rateByStars1=" + rateByStars1 +
                ", rateByStars2=" + rateByStars2 +
                ", rateByStars3=" + rateByStars3 +
                ", rateByStars4=" + rateByStars4 +
                ", rateByStars5=" + rateByStars5 +
                ", followupRemark='" + followupRemark + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createOn=" + createOn +
                ", updateBy='" + updateBy + '\'' +
                ", updateOn=" + updateOn +
                '}';
    }
}
