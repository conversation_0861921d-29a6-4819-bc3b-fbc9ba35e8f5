package com.relle.mbg.model;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;

public class AppAvailableTime implements Serializable {
    /**
     */
    private Long id;

    /**
     * 可预约日期
     */
    private LocalDate availableDate;

    /**
     * 可预约时间
     */
    private LocalTime availableTime;

    /**
     * 状态0不可用，1可用
     */
    private Byte status;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getAvailableDate() {
        return availableDate;
    }

    public void setAvailableDate(LocalDate availableDate) {
        this.availableDate = availableDate;
    }

    public LocalTime getAvailableTime() {
        return availableTime;
    }

    public void setAvailableTime(LocalTime availableTime) {
        this.availableTime = availableTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", availableDate=").append(availableDate);
        sb.append(", availableTime=").append(availableTime);
        sb.append(", status=").append(status);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}