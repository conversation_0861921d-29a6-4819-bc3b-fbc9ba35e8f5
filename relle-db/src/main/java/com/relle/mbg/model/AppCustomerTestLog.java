package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_customer_test_log
 * @create 2023-12-07 17:21:51
 */
public class AppCustomerTestLog implements Serializable {
    /**
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerUnionid;

    /**
     * 客户编号
     */
    private String customerId;

    /**
     */
    private String testOperatorId;

    /**
     * 此检测的服务项目id
     */
    private String serviceOperationRecordId;

    /**
     * 此检测的类型，如皮肤检测、身体检测、水分检测等
     */
    private Byte testType;

    /**
     * 检测阶段：服务前、服务后、服务中,便于后续对接
     */
    private Byte testPhase;

    /**
     * 服务日期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime testStarttime;

    /**
     * 服务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime testEndtime;

    /**
     */
    private String reportSrc;

    /**
     */
    private String reportRemark;

    /**
     */
    private Byte reportSend;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reportSendTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String createBy;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String updateBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 检测状态
     */
    private Byte testStatus;

    /**
     * 逻辑删除，0未删除，1已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerUnionid() {
        return customerUnionid;
    }

    public void setCustomerUnionid(String customerUnionid) {
        this.customerUnionid = customerUnionid;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getTestOperatorId() {
        return testOperatorId;
    }

    public void setTestOperatorId(String testOperatorId) {
        this.testOperatorId = testOperatorId;
    }

    public String getServiceOperationRecordId() {
        return serviceOperationRecordId;
    }

    public void setServiceOperationRecordId(String serviceOperationRecordId) {
        this.serviceOperationRecordId = serviceOperationRecordId;
    }

    public Byte getTestType() {
        return testType;
    }

    public void setTestType(Byte testType) {
        this.testType = testType;
    }

    public Byte getTestPhase() {
        return testPhase;
    }

    public void setTestPhase(Byte testPhase) {
        this.testPhase = testPhase;
    }

    public LocalDateTime getTestStarttime() {
        return testStarttime;
    }

    public void setTestStarttime(LocalDateTime testStarttime) {
        this.testStarttime = testStarttime;
    }

    public LocalDateTime getTestEndtime() {
        return testEndtime;
    }

    public void setTestEndtime(LocalDateTime testEndtime) {
        this.testEndtime = testEndtime;
    }

    public String getReportSrc() {
        return reportSrc;
    }

    public void setReportSrc(String reportSrc) {
        this.reportSrc = reportSrc;
    }

    public String getReportRemark() {
        return reportRemark;
    }

    public void setReportRemark(String reportRemark) {
        this.reportRemark = reportRemark;
    }

    public Byte getReportSend() {
        return reportSend;
    }

    public void setReportSend(Byte reportSend) {
        this.reportSend = reportSend;
    }

    public LocalDateTime getReportSendTime() {
        return reportSendTime;
    }

    public void setReportSendTime(LocalDateTime reportSendTime) {
        this.reportSendTime = reportSendTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(Byte testStatus) {
        this.testStatus = testStatus;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", customerUnionid=").append(customerUnionid);
        sb.append(", customerId=").append(customerId);
        sb.append(", testOperatorId=").append(testOperatorId);
        sb.append(", serviceOperationRecordId=").append(serviceOperationRecordId);
        sb.append(", testType=").append(testType);
        sb.append(", testPhase=").append(testPhase);
        sb.append(", testStarttime=").append(testStarttime);
        sb.append(", testEndtime=").append(testEndtime);
        sb.append(", reportSrc=").append(reportSrc);
        sb.append(", reportRemark=").append(reportRemark);
        sb.append(", reportSend=").append(reportSend);
        sb.append(", reportSendTime=").append(reportSendTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", testStatus=").append(testStatus);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}