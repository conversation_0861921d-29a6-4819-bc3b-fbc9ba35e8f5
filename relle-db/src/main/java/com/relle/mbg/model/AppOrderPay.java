package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class AppOrderPay implements Serializable {
    /**
     * 订单成功支付表自增id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     */
    private String paySystemOrderid;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payEndTime;

    /**
     * 支付渠道
     */
    private String payChanelId;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     */
    private String payParamsTimestamp;

    /**
     */
    private String payParamsPackage;

    /**
     */
    private String payParamsPaysign;

    /**
     */
    private String payPramsAppid;

    /**
     */
    private String payParamsSigntype;

    /**
     */
    private String payPraramsNoncestr;

    /**
     * 支付状态，0待支付，1已支付
     */
    private Byte payStatus;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String createBy;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String updateBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getPaySystemOrderid() {
        return paySystemOrderid;
    }

    public void setPaySystemOrderid(String paySystemOrderid) {
        this.paySystemOrderid = paySystemOrderid;
    }

    public LocalDateTime getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(LocalDateTime payEndTime) {
        this.payEndTime = payEndTime;
    }

    public String getPayChanelId() {
        return payChanelId;
    }

    public void setPayChanelId(String payChanelId) {
        this.payChanelId = payChanelId;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getPayParamsTimestamp() {
        return payParamsTimestamp;
    }

    public void setPayParamsTimestamp(String payParamsTimestamp) {
        this.payParamsTimestamp = payParamsTimestamp;
    }

    public String getPayParamsPackage() {
        return payParamsPackage;
    }

    public void setPayParamsPackage(String payParamsPackage) {
        this.payParamsPackage = payParamsPackage;
    }

    public String getPayParamsPaysign() {
        return payParamsPaysign;
    }

    public void setPayParamsPaysign(String payParamsPaysign) {
        this.payParamsPaysign = payParamsPaysign;
    }

    public String getPayPramsAppid() {
        return payPramsAppid;
    }

    public void setPayPramsAppid(String payPramsAppid) {
        this.payPramsAppid = payPramsAppid;
    }

    public String getPayParamsSigntype() {
        return payParamsSigntype;
    }

    public void setPayParamsSigntype(String payParamsSigntype) {
        this.payParamsSigntype = payParamsSigntype;
    }

    public String getPayPraramsNoncestr() {
        return payPraramsNoncestr;
    }

    public void setPayPraramsNoncestr(String payPraramsNoncestr) {
        this.payPraramsNoncestr = payPraramsNoncestr;
    }

    public Byte getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderSource=").append(orderSource);
        sb.append(", paySystemOrderid=").append(paySystemOrderid);
        sb.append(", payEndTime=").append(payEndTime);
        sb.append(", payChanelId=").append(payChanelId);
        sb.append(", payTime=").append(payTime);
        sb.append(", payAmount=").append(payAmount);
        sb.append(", payParamsTimestamp=").append(payParamsTimestamp);
        sb.append(", payParamsPackage=").append(payParamsPackage);
        sb.append(", payParamsPaysign=").append(payParamsPaysign);
        sb.append(", payPramsAppid=").append(payPramsAppid);
        sb.append(", payParamsSigntype=").append(payParamsSigntype);
        sb.append(", payPraramsNoncestr=").append(payPraramsNoncestr);
        sb.append(", payStatus=").append(payStatus);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}