package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_coupon_checkout
 * @create 2023-12-07 17:21:51
 */
public class AppCouponCheckout implements Serializable {
    /**
     * 优惠券领用记录自增id
     */
    private Long id;

    /**
     */
    private String storeIds;

    /**
     * 领用优惠券编号
     */
    private String couponId;

    /**
     */
    private String couponReceiveId;

    /**
     * 领用人unionid
     */
    private String receiveCustomerUnionid;

    /**
     * 领用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receiveTime;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponValidStarttime;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponValidEndtime;

    /**
     * 优惠券状态，默认1未使用；2已使用；4已过期；64已下架；
     */
    private Byte couponStatus;

    /**
     * 优惠券消费的订单编号
     */
    private String useCouponOrderid;

    /**
     * 优惠券消费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime useCouponTime;

    /**
     * 卡券来源，1用户领取；2系统发放；4用户购买(代金券) 8其他人赠送
     */
    private Integer couponSource;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String createBy;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String updateBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(String storeIds) {
        this.storeIds = storeIds;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponReceiveId() {
        return couponReceiveId;
    }

    public void setCouponReceiveId(String couponReceiveId) {
        this.couponReceiveId = couponReceiveId;
    }

    public String getReceiveCustomerUnionid() {
        return receiveCustomerUnionid;
    }

    public void setReceiveCustomerUnionid(String receiveCustomerUnionid) {
        this.receiveCustomerUnionid = receiveCustomerUnionid;
    }

    public LocalDateTime getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(LocalDateTime receiveTime) {
        this.receiveTime = receiveTime;
    }

    public LocalDateTime getCouponValidStarttime() {
        return couponValidStarttime;
    }

    public void setCouponValidStarttime(LocalDateTime couponValidStarttime) {
        this.couponValidStarttime = couponValidStarttime;
    }

    public LocalDateTime getCouponValidEndtime() {
        return couponValidEndtime;
    }

    public void setCouponValidEndtime(LocalDateTime couponValidEndtime) {
        this.couponValidEndtime = couponValidEndtime;
    }

    public Byte getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(Byte couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getUseCouponOrderid() {
        return useCouponOrderid;
    }

    public void setUseCouponOrderid(String useCouponOrderid) {
        this.useCouponOrderid = useCouponOrderid;
    }

    public LocalDateTime getUseCouponTime() {
        return useCouponTime;
    }

    public void setUseCouponTime(LocalDateTime useCouponTime) {
        this.useCouponTime = useCouponTime;
    }

    public Integer getCouponSource() {
        return couponSource;
    }

    public void setCouponSource(Integer couponSource) {
        this.couponSource = couponSource;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", storeIds=").append(storeIds);
        sb.append(", couponId=").append(couponId);
        sb.append(", couponReceiveId=").append(couponReceiveId);
        sb.append(", receiveCustomerUnionid=").append(receiveCustomerUnionid);
        sb.append(", receiveTime=").append(receiveTime);
        sb.append(", couponValidStarttime=").append(couponValidStarttime);
        sb.append(", couponValidEndtime=").append(couponValidEndtime);
        sb.append(", couponStatus=").append(couponStatus);
        sb.append(", useCouponOrderid=").append(useCouponOrderid);
        sb.append(", useCouponTime=").append(useCouponTime);
        sb.append(", couponSource=").append(couponSource);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}