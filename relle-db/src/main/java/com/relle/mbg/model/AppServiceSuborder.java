package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_suborder
 * @create 2023-12-07 17:21:51
 */
public class AppServiceSuborder implements Serializable {
    /**
     * 子订单自增id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 子订单编号
     */
    private String suborderId;

    /**
     * 用户小程序唯一id
     */
    private String unionid;

    /**
     * 门店编号，如SH0001
     */
    private String storeId;

    /**
     */
    private String roomId;

    /**
     * 产品编号，对应产品表自增id（app_service_item表）
     */
    private String serviceItemId;

    /**
     * 预约服务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime bookTime;

    /**
     */
    private BigDecimal suborderOriginPrice;

    /**
     */
    private BigDecimal suborderReductionAmount;

    /**
     * 子订单金额
     */
    private BigDecimal suborderAmount;

    /**
     * 子订单状态-无符号int，状态可以为负数，表明异常状态；1：待支付，2: 待服务，4: 服务中，8: 已完成。 -1：售后中； -2: 已退款； -4: 已取消；
     */
    private Short suborderStatus;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String createBy;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String updateBy;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除，默认为0未删除，1已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getSuborderId() {
        return suborderId;
    }

    public void setSuborderId(String suborderId) {
        this.suborderId = suborderId;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(String serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public LocalDateTime getBookTime() {
        return bookTime;
    }

    public void setBookTime(LocalDateTime bookTime) {
        this.bookTime = bookTime;
    }

    public BigDecimal getSuborderOriginPrice() {
        return suborderOriginPrice;
    }

    public void setSuborderOriginPrice(BigDecimal suborderOriginPrice) {
        this.suborderOriginPrice = suborderOriginPrice;
    }

    public BigDecimal getSuborderReductionAmount() {
        return suborderReductionAmount;
    }

    public void setSuborderReductionAmount(BigDecimal suborderReductionAmount) {
        this.suborderReductionAmount = suborderReductionAmount;
    }

    public BigDecimal getSuborderAmount() {
        return suborderAmount;
    }

    public void setSuborderAmount(BigDecimal suborderAmount) {
        this.suborderAmount = suborderAmount;
    }

    public Short getSuborderStatus() {
        return suborderStatus;
    }

    public void setSuborderStatus(Short suborderStatus) {
        this.suborderStatus = suborderStatus;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", suborderId=").append(suborderId);
        sb.append(", unionid=").append(unionid);
        sb.append(", storeId=").append(storeId);
        sb.append(", roomId=").append(roomId);
        sb.append(", serviceItemId=").append(serviceItemId);
        sb.append(", bookTime=").append(bookTime);
        sb.append(", suborderOriginPrice=").append(suborderOriginPrice);
        sb.append(", suborderReductionAmount=").append(suborderReductionAmount);
        sb.append(", suborderAmount=").append(suborderAmount);
        sb.append(", suborderStatus=").append(suborderStatus);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}