package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_coupon
 * @create 2023-12-07 17:21:51
 */
public class AppCoupon implements Serializable {
    /**
     * 优惠券自增id
     */
    private Long id;

    /**
     * 优惠券号
     */
    private String couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 卡券分类，1代金券；2抵扣券；3特殊券
     */
    private Byte couponCategory;

    /**
     * 优惠券类型 
     */
    private String couponType;

    /**
     */
    private String couponCoverImg;

    /**
     * 优惠券发放总数，如果是0，则是无限量
     */
    private Integer couponIssueTotal;

    /**
     * 最低可使用订单金额
     */
    private BigDecimal couponMinCharge;

    /**
     * 优惠金额
     */
    private BigDecimal couponValueAmount;

    /**
     * 开始有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponValidDatetimeBegin;

    /**
     * 结束有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponValidDatetimeEnd;

    /**
     * ？？？叠加，0完全不可叠加；1普通券，可叠加通用券；2通用券
     */
    private Byte canSuperpose;

    /**
     * 优惠券领用限制，0不限制，默认是1，限领一张
     */
    private Byte couponReceiveLimit;

    /**
     * 优惠券领用有效期天数，0,不限制（与优惠券有效期一致），大于1的即为天数
     */
    private Integer couponReveiveValidday;

    /**
     * 优惠券状态，1则是正常可用；2则是过期; 64则是下架。预留几个状态
     */
    private Byte couponStatus;

    /**
     * 创建人：unionid
     */
    private String createBy;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新人：unionid
     */
    private String updateBy;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除，0未删除，1已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public Byte getCouponCategory() {
        return couponCategory;
    }

    public void setCouponCategory(Byte couponCategory) {
        this.couponCategory = couponCategory;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getCouponCoverImg() {
        return couponCoverImg;
    }

    public void setCouponCoverImg(String couponCoverImg) {
        this.couponCoverImg = couponCoverImg;
    }

    public Integer getCouponIssueTotal() {
        return couponIssueTotal;
    }

    public void setCouponIssueTotal(Integer couponIssueTotal) {
        this.couponIssueTotal = couponIssueTotal;
    }

    public BigDecimal getCouponMinCharge() {
        return couponMinCharge;
    }

    public void setCouponMinCharge(BigDecimal couponMinCharge) {
        this.couponMinCharge = couponMinCharge;
    }

    public BigDecimal getCouponValueAmount() {
        return couponValueAmount;
    }

    public void setCouponValueAmount(BigDecimal couponValueAmount) {
        this.couponValueAmount = couponValueAmount;
    }

    public LocalDateTime getCouponValidDatetimeBegin() {
        return couponValidDatetimeBegin;
    }

    public void setCouponValidDatetimeBegin(LocalDateTime couponValidDatetimeBegin) {
        this.couponValidDatetimeBegin = couponValidDatetimeBegin;
    }

    public LocalDateTime getCouponValidDatetimeEnd() {
        return couponValidDatetimeEnd;
    }

    public void setCouponValidDatetimeEnd(LocalDateTime couponValidDatetimeEnd) {
        this.couponValidDatetimeEnd = couponValidDatetimeEnd;
    }

    public Byte getCanSuperpose() {
        return canSuperpose;
    }

    public void setCanSuperpose(Byte canSuperpose) {
        this.canSuperpose = canSuperpose;
    }

    public Byte getCouponReceiveLimit() {
        return couponReceiveLimit;
    }

    public void setCouponReceiveLimit(Byte couponReceiveLimit) {
        this.couponReceiveLimit = couponReceiveLimit;
    }

    public Integer getCouponReveiveValidday() {
        return couponReveiveValidday;
    }

    public void setCouponReveiveValidday(Integer couponReveiveValidday) {
        this.couponReveiveValidday = couponReveiveValidday;
    }

    public Byte getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(Byte couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", couponId=").append(couponId);
        sb.append(", couponName=").append(couponName);
        sb.append(", couponCategory=").append(couponCategory);
        sb.append(", couponType=").append(couponType);
        sb.append(", couponCoverImg=").append(couponCoverImg);
        sb.append(", couponIssueTotal=").append(couponIssueTotal);
        sb.append(", couponMinCharge=").append(couponMinCharge);
        sb.append(", couponValueAmount=").append(couponValueAmount);
        sb.append(", couponValidDatetimeBegin=").append(couponValidDatetimeBegin);
        sb.append(", couponValidDatetimeEnd=").append(couponValidDatetimeEnd);
        sb.append(", canSuperpose=").append(canSuperpose);
        sb.append(", couponReceiveLimit=").append(couponReceiveLimit);
        sb.append(", couponReveiveValidday=").append(couponReveiveValidday);
        sb.append(", couponStatus=").append(couponStatus);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}