package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_test_media_relation
 * @create 2023-12-07 17:21:51
 */
public class AppTestMediaRelation implements Serializable {
    /**
     * 用户检测上传媒体自增id
     */
    private Long id;

    /**
     * 检测记录编号，关联检测记录
     */
    private Long testNo;

    /**
     * 图片id
     */
    private Long mediaNo;

    /**
     * 媒体展示顺序
     */
    private Byte mediaShowSort;

    /**
     * 媒体分类
     */
    private Byte mediaCategory;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String createBy;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     */
    private String updateBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTestNo() {
        return testNo;
    }

    public void setTestNo(Long testNo) {
        this.testNo = testNo;
    }

    public Long getMediaNo() {
        return mediaNo;
    }

    public void setMediaNo(Long mediaNo) {
        this.mediaNo = mediaNo;
    }

    public Byte getMediaShowSort() {
        return mediaShowSort;
    }

    public void setMediaShowSort(Byte mediaShowSort) {
        this.mediaShowSort = mediaShowSort;
    }

    public Byte getMediaCategory() {
        return mediaCategory;
    }

    public void setMediaCategory(Byte mediaCategory) {
        this.mediaCategory = mediaCategory;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpDateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String upDateBy) {
        this.updateBy = upDateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", testNo=").append(testNo);
        sb.append(", mediaNo=").append(mediaNo);
        sb.append(", mediaShowSort=").append(mediaShowSort);
        sb.append(", mediaCategory=").append(mediaCategory);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}