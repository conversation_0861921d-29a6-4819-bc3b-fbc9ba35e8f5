package com.relle.mbg.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;

public class AppServiceOperationRecord implements Serializable {
    /**
     * 服务操作记录表自增id
     */
    private Long id;

    /**
     * 服务操作记录id
     */
    private String serviceOperationId;

    /**
     */
    private String unionid;

    /**
     * 子订单id
     */
    private String suborderId;

    /**
     * 服务所在门店
     */
    private String storeId;

    /**
     * 操作员id unionid
     */
    private String serviceOperatorId;

    /**
     * 服务开始时间 ：：这两个需要修改，关联操作时段表
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime serviceStarttime;

    /**
     * 服务结束时间 ：：这两个需要修改，关联操作时段表
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime serviceEndtime;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime nursingStarttime;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime nursingEndtime;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 设备ID
     */
    private String machineId;

    /**
     * 服务操作状态
     */
    private Byte serviceOperationStatus;

    /**
     */
    private Byte customerFeedback;

    /**
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime customerFeedbackTime;

    /**
     * 系统
     */
    private String createBy;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 系统
     */
    private String updateBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除，0未删除，1已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getServiceOperationId() {
        return serviceOperationId;
    }

    public void setServiceOperationId(String serviceOperationId) {
        this.serviceOperationId = serviceOperationId;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getSuborderId() {
        return suborderId;
    }

    public void setSuborderId(String suborderId) {
        this.suborderId = suborderId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getServiceOperatorId() {
        return serviceOperatorId;
    }

    public void setServiceOperatorId(String serviceOperatorId) {
        this.serviceOperatorId = serviceOperatorId;
    }

    public LocalDateTime getServiceStarttime() {
        return serviceStarttime;
    }

    public void setServiceStarttime(LocalDateTime serviceStarttime) {
        this.serviceStarttime = serviceStarttime;
    }

    public LocalDateTime getServiceEndtime() {
        return serviceEndtime;
    }

    public void setServiceEndtime(LocalDateTime serviceEndtime) {
        this.serviceEndtime = serviceEndtime;
    }

    public LocalDateTime getNursingStarttime() {
        return nursingStarttime;
    }

    public void setNursingStarttime(LocalDateTime nursingStarttime) {
        this.nursingStarttime = nursingStarttime;
    }

    public LocalDateTime getNursingEndtime() {
        return nursingEndtime;
    }

    public void setNursingEndtime(LocalDateTime nursingEndtime) {
        this.nursingEndtime = nursingEndtime;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getMachineId() {
        return machineId;
    }

    public void setMachineId(String machineId) {
        this.machineId = machineId;
    }

    public Byte getServiceOperationStatus() {
        return serviceOperationStatus;
    }

    public void setServiceOperationStatus(Byte serviceOperationStatus) {
        this.serviceOperationStatus = serviceOperationStatus;
    }

    public Byte getCustomerFeedback() {
        return customerFeedback;
    }

    public void setCustomerFeedback(Byte customerFeedback) {
        this.customerFeedback = customerFeedback;
    }

    public LocalDateTime getCustomerFeedbackTime() {
        return customerFeedbackTime;
    }

    public void setCustomerFeedbackTime(LocalDateTime customerFeedbackTime) {
        this.customerFeedbackTime = customerFeedbackTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    /**
     * @return 
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", serviceOperationId=").append(serviceOperationId);
        sb.append(", unionid=").append(unionid);
        sb.append(", suborderId=").append(suborderId);
        sb.append(", storeId=").append(storeId);
        sb.append(", serviceOperatorId=").append(serviceOperatorId);
        sb.append(", serviceStarttime=").append(serviceStarttime);
        sb.append(", serviceEndtime=").append(serviceEndtime);
        sb.append(", nursingStarttime=").append(nursingStarttime);
        sb.append(", nursingEndtime=").append(nursingEndtime);
        sb.append(", roomId=").append(roomId);
        sb.append(", machineId=").append(machineId);
        sb.append(", serviceOperationStatus=").append(serviceOperationStatus);
        sb.append(", customerFeedback=").append(customerFeedback);
        sb.append(", customerFeedbackTime=").append(customerFeedbackTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}