package com.relle.mbg.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AppServiceSuborderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * @return 
     */
    public AppServiceSuborderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderByClause
     * @return 
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * @return 
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     * @return 
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return 
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @return 
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     * @return 
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return 
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return 
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return 
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * @return 
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdIsNull() {
            addCriterion("suborder_id is null");
            return (Criteria) this;
        }

        public Criteria andSuborderIdIsNotNull() {
            addCriterion("suborder_id is not null");
            return (Criteria) this;
        }

        public Criteria andSuborderIdEqualTo(String value) {
            addCriterion("suborder_id =", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdNotEqualTo(String value) {
            addCriterion("suborder_id <>", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdGreaterThan(String value) {
            addCriterion("suborder_id >", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdGreaterThanOrEqualTo(String value) {
            addCriterion("suborder_id >=", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdLessThan(String value) {
            addCriterion("suborder_id <", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdLessThanOrEqualTo(String value) {
            addCriterion("suborder_id <=", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdLike(String value) {
            addCriterion("suborder_id like", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdNotLike(String value) {
            addCriterion("suborder_id not like", value, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdIn(List<String> values) {
            addCriterion("suborder_id in", values, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdNotIn(List<String> values) {
            addCriterion("suborder_id not in", values, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdBetween(String value1, String value2) {
            addCriterion("suborder_id between", value1, value2, "suborderId");
            return (Criteria) this;
        }

        public Criteria andSuborderIdNotBetween(String value1, String value2) {
            addCriterion("suborder_id not between", value1, value2, "suborderId");
            return (Criteria) this;
        }

        public Criteria andUnionidIsNull() {
            addCriterion("unionid is null");
            return (Criteria) this;
        }

        public Criteria andUnionidIsNotNull() {
            addCriterion("unionid is not null");
            return (Criteria) this;
        }

        public Criteria andUnionidEqualTo(String value) {
            addCriterion("unionid =", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidNotEqualTo(String value) {
            addCriterion("unionid <>", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidGreaterThan(String value) {
            addCriterion("unionid >", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidGreaterThanOrEqualTo(String value) {
            addCriterion("unionid >=", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidLessThan(String value) {
            addCriterion("unionid <", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidLessThanOrEqualTo(String value) {
            addCriterion("unionid <=", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidLike(String value) {
            addCriterion("unionid like", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidNotLike(String value) {
            addCriterion("unionid not like", value, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidIn(List<String> values) {
            addCriterion("unionid in", values, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidNotIn(List<String> values) {
            addCriterion("unionid not in", values, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidBetween(String value1, String value2) {
            addCriterion("unionid between", value1, value2, "unionid");
            return (Criteria) this;
        }

        public Criteria andUnionidNotBetween(String value1, String value2) {
            addCriterion("unionid not between", value1, value2, "unionid");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNull() {
            addCriterion("store_id is null");
            return (Criteria) this;
        }

        public Criteria andStoreIdIsNotNull() {
            addCriterion("store_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoreIdEqualTo(String value) {
            addCriterion("store_id =", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotEqualTo(String value) {
            addCriterion("store_id <>", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThan(String value) {
            addCriterion("store_id >", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdGreaterThanOrEqualTo(String value) {
            addCriterion("store_id >=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThan(String value) {
            addCriterion("store_id <", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLessThanOrEqualTo(String value) {
            addCriterion("store_id <=", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdLike(String value) {
            addCriterion("store_id like", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotLike(String value) {
            addCriterion("store_id not like", value, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdIn(List<String> values) {
            addCriterion("store_id in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotIn(List<String> values) {
            addCriterion("store_id not in", values, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdBetween(String value1, String value2) {
            addCriterion("store_id between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andStoreIdNotBetween(String value1, String value2) {
            addCriterion("store_id not between", value1, value2, "storeId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(String value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(String value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(String value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(String value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(String value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(String value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLike(String value) {
            addCriterion("room_id like", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotLike(String value) {
            addCriterion("room_id not like", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<String> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<String> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(String value1, String value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(String value1, String value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdIsNull() {
            addCriterion("service_item_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdIsNotNull() {
            addCriterion("service_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdEqualTo(String value) {
            addCriterion("service_item_id =", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdNotEqualTo(String value) {
            addCriterion("service_item_id <>", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdGreaterThan(String value) {
            addCriterion("service_item_id >", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdGreaterThanOrEqualTo(String value) {
            addCriterion("service_item_id >=", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdLessThan(String value) {
            addCriterion("service_item_id <", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdLessThanOrEqualTo(String value) {
            addCriterion("service_item_id <=", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdLike(String value) {
            addCriterion("service_item_id like", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdNotLike(String value) {
            addCriterion("service_item_id not like", value, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdIn(List<String> values) {
            addCriterion("service_item_id in", values, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdNotIn(List<String> values) {
            addCriterion("service_item_id not in", values, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdBetween(String value1, String value2) {
            addCriterion("service_item_id between", value1, value2, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andServiceItemIdNotBetween(String value1, String value2) {
            addCriterion("service_item_id not between", value1, value2, "serviceItemId");
            return (Criteria) this;
        }

        public Criteria andBookTimeIsNull() {
            addCriterion("book_time is null");
            return (Criteria) this;
        }

        public Criteria andBookTimeIsNotNull() {
            addCriterion("book_time is not null");
            return (Criteria) this;
        }

        public Criteria andBookTimeEqualTo(LocalDateTime value) {
            addCriterion("book_time =", value, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeNotEqualTo(LocalDateTime value) {
            addCriterion("book_time <>", value, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeGreaterThan(LocalDateTime value) {
            addCriterion("book_time >", value, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("book_time >=", value, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeLessThan(LocalDateTime value) {
            addCriterion("book_time <", value, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("book_time <=", value, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeIn(List<LocalDateTime> values) {
            addCriterion("book_time in", values, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeNotIn(List<LocalDateTime> values) {
            addCriterion("book_time not in", values, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("book_time between", value1, value2, "bookTime");
            return (Criteria) this;
        }

        public Criteria andBookTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("book_time not between", value1, value2, "bookTime");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceIsNull() {
            addCriterion("suborder_origin_price is null");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceIsNotNull() {
            addCriterion("suborder_origin_price is not null");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceEqualTo(BigDecimal value) {
            addCriterion("suborder_origin_price =", value, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceNotEqualTo(BigDecimal value) {
            addCriterion("suborder_origin_price <>", value, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceGreaterThan(BigDecimal value) {
            addCriterion("suborder_origin_price >", value, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suborder_origin_price >=", value, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceLessThan(BigDecimal value) {
            addCriterion("suborder_origin_price <", value, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suborder_origin_price <=", value, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceIn(List<BigDecimal> values) {
            addCriterion("suborder_origin_price in", values, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceNotIn(List<BigDecimal> values) {
            addCriterion("suborder_origin_price not in", values, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suborder_origin_price between", value1, value2, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderOriginPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suborder_origin_price not between", value1, value2, "suborderOriginPrice");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountIsNull() {
            addCriterion("suborder_reduction_amount is null");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountIsNotNull() {
            addCriterion("suborder_reduction_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountEqualTo(BigDecimal value) {
            addCriterion("suborder_reduction_amount =", value, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountNotEqualTo(BigDecimal value) {
            addCriterion("suborder_reduction_amount <>", value, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountGreaterThan(BigDecimal value) {
            addCriterion("suborder_reduction_amount >", value, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suborder_reduction_amount >=", value, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountLessThan(BigDecimal value) {
            addCriterion("suborder_reduction_amount <", value, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suborder_reduction_amount <=", value, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountIn(List<BigDecimal> values) {
            addCriterion("suborder_reduction_amount in", values, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountNotIn(List<BigDecimal> values) {
            addCriterion("suborder_reduction_amount not in", values, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suborder_reduction_amount between", value1, value2, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderReductionAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suborder_reduction_amount not between", value1, value2, "suborderReductionAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountIsNull() {
            addCriterion("suborder_amount is null");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountIsNotNull() {
            addCriterion("suborder_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountEqualTo(BigDecimal value) {
            addCriterion("suborder_amount =", value, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountNotEqualTo(BigDecimal value) {
            addCriterion("suborder_amount <>", value, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountGreaterThan(BigDecimal value) {
            addCriterion("suborder_amount >", value, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("suborder_amount >=", value, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountLessThan(BigDecimal value) {
            addCriterion("suborder_amount <", value, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("suborder_amount <=", value, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountIn(List<BigDecimal> values) {
            addCriterion("suborder_amount in", values, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountNotIn(List<BigDecimal> values) {
            addCriterion("suborder_amount not in", values, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suborder_amount between", value1, value2, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("suborder_amount not between", value1, value2, "suborderAmount");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusIsNull() {
            addCriterion("suborder_status is null");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusIsNotNull() {
            addCriterion("suborder_status is not null");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusEqualTo(Short value) {
            addCriterion("suborder_status =", value, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusNotEqualTo(Short value) {
            addCriterion("suborder_status <>", value, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusGreaterThan(Short value) {
            addCriterion("suborder_status >", value, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusGreaterThanOrEqualTo(Short value) {
            addCriterion("suborder_status >=", value, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusLessThan(Short value) {
            addCriterion("suborder_status <", value, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusLessThanOrEqualTo(Short value) {
            addCriterion("suborder_status <=", value, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusIn(List<Short> values) {
            addCriterion("suborder_status in", values, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusNotIn(List<Short> values) {
            addCriterion("suborder_status not in", values, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusBetween(Short value1, Short value2) {
            addCriterion("suborder_status between", value1, value2, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andSuborderStatusNotBetween(Short value1, Short value2) {
            addCriterion("suborder_status not between", value1, value2, "suborderStatus");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(LocalDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(LocalDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(LocalDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<LocalDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Byte value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Byte value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Byte value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Byte value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Byte value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Byte value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Byte> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Byte> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Byte value1, Byte value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Byte value1, Byte value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}