package com.relle.enums;

public enum ServiceOrderStatusEnum {
    WAIT_PAY((short)1, "待支付"),
    PAY_SUCCESS((short)2 ,"已支付/待服务"),
    FINISHED((short)4, "已完成"),
    CANCELING((short)8, "售后中/退款中"),
    CANCELED((short)16, "已退款"),
    PART_CANCELED((short)32, "已部分退款"),
    CANCEL_FAIL((short)64, "退款失败"),
    FOLLOWUPED((short)128, "已回访"),
    CLOSED_BY_JOB((short)1024, "已关闭（未支付，系统关闭）"),
    CLOSED_BY_USER((short)2048, "已关闭（未支付，用户主动）")
    ; //此写法防止扩充时忘记分号

    private short code;
    private String name;

    private ServiceOrderStatusEnum(short code, String name) {
        this.code = code;
        this.name = name;
    }

    public short getCode() {
        return code;
    }

    public void setCode(short code) {
        this.code = code;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
