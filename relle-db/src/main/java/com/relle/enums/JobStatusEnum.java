package com.relle.enums;

public enum JobStatusEnum {
    CLOSED("closed", "已关闭"),
    WAITING("waiting", "等待中"),
    FINISHED("finished", "已完成"),
    ; //此写法防止扩充时忘记分号

    private String code;
    private String name;

    private JobStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
