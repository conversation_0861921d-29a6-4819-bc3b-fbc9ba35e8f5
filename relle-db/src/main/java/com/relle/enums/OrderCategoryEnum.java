package com.relle.enums;

public enum OrderCategoryEnum {
    SERVICE((byte)1, "服务订单"),
    COUPON((byte)2, "代金券订单"),
    TEAM((byte)4, "团购订单"),
    CONSULT((byte)8, "问诊订单"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private OrderCategoryEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
