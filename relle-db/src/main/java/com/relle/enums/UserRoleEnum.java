package com.relle.enums;

public enum UserRoleEnum {
    NEW_USER((byte)1, "新用户"),
    OLD_USER((byte)2, "老用户"),
    ONWER((byte)4, "股东"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private UserRoleEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getRoleName(byte code){
        UserRoleEnum[] enums = UserRoleEnum.values();
        for (int i=0;i<enums.length;i++){
            UserRoleEnum em =  enums[i];
            if(em.code == code){
                return em.name;
            }
        }
        return "特殊用户";
    }
}
