package com.relle.enums;

public enum ServiceItemMediaTypeEnum {
    THUMBNAIL("thumbnail", "缩略图"),
    PRODUCT("product", "产品图片/视频"),
    PRODUCT_DETAIL("product_detail", "产品详情"),
    ; //此写法防止扩充时忘记分号

    private String code;
    private String name;

    private ServiceItemMediaTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
