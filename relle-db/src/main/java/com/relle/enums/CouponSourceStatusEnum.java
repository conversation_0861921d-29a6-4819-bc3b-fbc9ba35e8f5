package com.relle.enums;

public enum CouponSourceStatusEnum {
    CAN_USE((byte)1, "正常使用"),
    INVALID((byte)2, "已失效"), //可见，无法使用
    OFFLINE((byte)64, "已下架"),//下架优惠券不可见
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private CouponSourceStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
