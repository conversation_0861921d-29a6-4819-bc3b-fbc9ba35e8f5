package com.relle.enums;

public enum ActivityCategoryEnum {
    COUPON(2, "优惠券活动"),
    TEAM(4, "团购"),
    RECRUITNEW(16, "带新"),
    REBATE_MONEY(32, "返利-代金券"),
    REBATE_SERVICE(64, "返利-产品"),
    ; //此写法防止扩充时忘记分号

    private Integer code;
    private String name;

    private ActivityCategoryEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
