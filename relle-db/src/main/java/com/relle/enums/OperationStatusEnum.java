package com.relle.enums;

public enum OperationStatusEnum {
    TEST_START((byte)1, "皮肤检测开始"),
    TEST_END((byte)2 ,"皮肤检测结束"),
    NURSE_START((byte)4, "开始护理"),
    NURSE_END((byte)8, "护理结束"),
    FINISHED((byte)16, "服务结束"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private OperationStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
