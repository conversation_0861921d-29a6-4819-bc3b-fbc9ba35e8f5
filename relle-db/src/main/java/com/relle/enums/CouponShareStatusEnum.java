package com.relle.enums;

public enum CouponShareStatusEnum {
    SHAREING((byte)1, "赠送中"),
    SHARED((byte)2, "已赠送/已领取"),
    RETURNED((byte)4, "已退回"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private CouponShareStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
