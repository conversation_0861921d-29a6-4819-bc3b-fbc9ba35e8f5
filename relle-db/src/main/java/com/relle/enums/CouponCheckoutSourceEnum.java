package com.relle.enums;

public enum CouponCheckoutSourceEnum {
    SELF_RECEIVE(1, "用户自己领取"),
    SYS_SEND(2, "系统发放"),
    USER_BUY(4, "用户购买(代金券)"),
    USER_SHARE(8, "其他人赠送"),
    TEAM_BUY(16, "团购"),
    RECRUIT_NEW(32, "老带新"),
    MT(64, "美团大众点评"),
    REBATE_MONEY(128, "任意消费返利"),
    REBATE_SERVICE(256, "任意计划返利"),
    ACTIVITY_QRCODE(512, "活动扫码领取"),


    ; //此写法防止扩充时忘记分号

    private int code;
    private String name;

    private CouponCheckoutSourceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static void main(String[] args) {
        byte aa = (byte)128;
        System.out.println(aa);
    }
}
