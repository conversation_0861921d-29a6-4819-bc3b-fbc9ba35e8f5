package com.relle.enums;

public enum OderRefundStatusEnum {
    REFUNDING((byte)1, "退款中"),
    REFUND_FINISHED((byte)2, "退款成功"),
    REFUND_FAIL((byte)4, "退款失败"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private OderRefundStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
