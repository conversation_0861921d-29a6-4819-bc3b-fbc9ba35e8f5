package com.relle.enums;

public enum ShareTeamOrderStatusEnum {
    SHAREING_WAITBUY((byte)1, "团购中-我未团"),
    SHAREING_BUYING_MYBUY((byte)2, "团购中-我已团"),
    SHAREING_BUYED((byte)4, "团购已完成"),
    SHAREING_FAILED((byte)8, "团购已失效"),
    SHAREING_NOPRIV((byte)16, "无参团权限"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private ShareTeamOrderStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
