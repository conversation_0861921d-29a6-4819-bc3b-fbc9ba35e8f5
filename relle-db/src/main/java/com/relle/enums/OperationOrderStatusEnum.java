package com.relle.enums;

public enum OperationOrderStatusEnum {
    WAIT_SERVICE(1, "待服务"),
    SERVERING(2 ,"服务中"),
    WAIT_FEEKBACK(4, "待评价"),
    FINISHED(8, "已完成"),
    CANCELED(16, "已取消"),
    ; //此写法防止扩充时忘记分号

    private int code;
    private String name;

    private OperationOrderStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
