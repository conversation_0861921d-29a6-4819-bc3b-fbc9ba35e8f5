package com.relle.enums;

public enum TestMediaCategoryEnum {
    BEFORE((byte)1, "服务前"),
    AFTER((byte)8 ,"服务后"),
    ;

    private byte code;
    private String name;

    private TestMediaCategoryEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }
    public static String getTitle(Byte code) {
        TestMediaCategoryEnum[] carTypeEnums = values();
        for (TestMediaCategoryEnum carTypeEnum : carTypeEnums) {
            if (carTypeEnum.getCode()==code) {
                return carTypeEnum.getName();
            }
        }
        return null;
    }

    public static Byte getCode(String name) {
        TestMediaCategoryEnum[] carTypeEnums = values();
        for (TestMediaCategoryEnum carTypeEnum : carTypeEnums) {
            if (carTypeEnum.getName().equals(name)) {
                return carTypeEnum.getCode();
            }
        }
        return null;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static void main(String[] args) {
        byte a = 1;
        System.out.println(a&-1);
    }
}
