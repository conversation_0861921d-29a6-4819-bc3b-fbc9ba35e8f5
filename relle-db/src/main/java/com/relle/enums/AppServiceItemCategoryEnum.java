package com.relle.enums;

// import com.relle.service.impl.AppServiceBundleOrderServiceImpl;
// import com.relle.service.impl.AppServiceOrderServiceImpl;
// import com.relle.service.impl.AppServiceTeamOrderServiceImpl;

public enum AppServiceItemCategoryEnum {
    SERVICE((byte)1, "服务产品", "AppServiceOrderServiceImpl","com.relle.task.AutoCloseOrderJob"),
    CASH_COUPON((byte)2, "代金券", "AppServiceBundleOrderServiceImpl","com.relle.task.AutoCloseBundleOrderJob"),
    TEAM_COUPON((byte)4, "团购-代金券", "AppServiceTeamOrderServiceImpl","com.relle.task.AutoCloseTeamOrderJob"),
    POPULARIZE_COUPON((byte)8, "推广-代金券", "AppServiceBundleOrderServiceImpl","com.relle.task.AutoCloseBundleOrderJob"),

    RECRUITNEW((byte)16, "拉新", "AppServiceOrderServiceImpl","com.relle.task.AutoCloseOrderJob"),

    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;
    private String serviceImpClassName;
    private String jobName;

    private AppServiceItemCategoryEnum(byte code, String name, String serviceImpClassName, String jobName) {
        this.code = code;
        this.name = name;
        this.serviceImpClassName = serviceImpClassName;
        this.jobName = jobName;
    }

    public static String getServiceImpClassName(byte code){
        AppServiceItemCategoryEnum[] enums = AppServiceItemCategoryEnum.values();
        for (int i=0;i<enums.length;i++){
            AppServiceItemCategoryEnum em =  enums[i];
            if(em.code == code){
                return em.serviceImpClassName;
            }
        }
        return null;
    }

    public static String getJobName(byte code){
        AppServiceItemCategoryEnum[] enums = AppServiceItemCategoryEnum.values();
        for (int i=0;i<enums.length;i++){
            AppServiceItemCategoryEnum em =  enums[i];
            if(em.code == code){
                return em.jobName;
            }
        }
        return null;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getServiceImpClass(Byte serviceItemCategory) {
        return getServiceImpClassName(serviceItemCategory);
    }

    public void setServiceImpClass(String serviceImpClass) {
        this.serviceImpClassName = serviceImpClass;
    }
}
