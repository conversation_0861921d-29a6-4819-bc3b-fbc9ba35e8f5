package com.relle.enums;

public enum CouponStatusEnum {
    NO_USE((byte)1, "未使用"),
    USED((byte)2, "已使用"),
    EXPIRE((byte)4, "已过期"),
    SHAREING((byte)8, "分享中"),
    SHAREED((byte)16, "已分享"),
    WAIT_TEAM((byte)32, "待团购完成"),
    offline((byte)128, "已下架"),
    ; //此写法防止扩充时忘记分号

    private byte code;
    private String name;

    private CouponStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public byte getCode() {
        return code;
    }

    public void setCode(byte code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
