package com.relle.db.service;

import com.relle.db.entity.AppServiceOrder;
import com.relle.db.repository.AppServiceOrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * JPA-based Service Order Service Implementation
 * This demonstrates how to migrate from MyBatis to JPA for order management
 */
@Service
@Transactional
public class AppServiceOrderJpaService {

    @Autowired
    private AppServiceOrderRepository appServiceOrderRepository;

    /**
     * Find order by order ID
     */
    public AppServiceOrder getOrderByOrderId(String orderId) {
        return appServiceOrderRepository.findByOrderIdAndDeleted(orderId, (byte) 0)
                .orElse(null);
    }

    /**
     * Find orders by unionid
     */
    public List<AppServiceOrder> getOrdersByUnionid(String unionid) {
        return appServiceOrderRepository.findByUnionidAndDeleted(unionid, (byte) 0);
    }

    /**
     * Find orders by store ID
     */
    public List<AppServiceOrder> getOrdersByStoreId(String storeId) {
        return appServiceOrderRepository.findBySotreIdAndDeleted(storeId, (byte) 0);
    }

    /**
     * Find orders by status
     */
    public List<AppServiceOrder> getOrdersByStatus(Short status) {
        return appServiceOrderRepository.findByOrderStatusAndDeleted(status, (byte) 0);
    }

    /**
     * Find orders by contact phone
     */
    public List<AppServiceOrder> getOrdersByContactPhone(String contactPhone) {
        return appServiceOrderRepository.findByContactPhoneAndDeleted(contactPhone, (byte) 0);
    }

    /**
     * Find orders by date range
     */
    public List<AppServiceOrder> getOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return appServiceOrderRepository.findByCreateTimeBetweenAndDeleted(startDate, endDate, (byte) 0);
    }

    /**
     * Find orders by unionid and date range
     */
    public List<AppServiceOrder> getOrdersByUnionidAndDateRange(String unionid, LocalDateTime startDate, LocalDateTime endDate) {
        return appServiceOrderRepository.findByUnionidAndDateRangeAndDeleted(unionid, startDate, endDate, (byte) 0);
    }

    /**
     * Find orders by store and date range
     */
    public List<AppServiceOrder> getOrdersByStoreAndDateRange(String storeId, LocalDateTime startDate, LocalDateTime endDate) {
        return appServiceOrderRepository.findByStoreAndDateRangeAndDeleted(storeId, startDate, endDate, (byte) 0);
    }

    /**
     * Find pending payment orders
     */
    public List<AppServiceOrder> getPendingPaymentOrders() {
        return appServiceOrderRepository.findPendingPaymentOrders((byte) 0);
    }

    /**
     * Find completed orders
     */
    public List<AppServiceOrder> getCompletedOrders() {
        return appServiceOrderRepository.findCompletedOrders((byte) 0);
    }

    /**
     * Save or update order
     */
    public AppServiceOrder saveOrder(AppServiceOrder order) {
        return appServiceOrderRepository.save(order);
    }

    /**
     * Soft delete order
     */
    public void deleteOrder(Long id) {
        Optional<AppServiceOrder> orderOpt = appServiceOrderRepository.findById(id);
        if (orderOpt.isPresent()) {
            AppServiceOrder order = orderOpt.get();
            order.setDeleted((byte) 1);
            appServiceOrderRepository.save(order);
        }
    }

    /**
     * Check if order exists by order ID
     */
    public boolean existsByOrderId(String orderId) {
        return appServiceOrderRepository.existsByOrderIdAndDeleted(orderId, (byte) 0);
    }

    /**
     * Count orders by status
     */
    public long countByStatus(Short status) {
        return appServiceOrderRepository.countByOrderStatusAndDeleted(status, (byte) 0);
    }

    /**
     * Count orders by store
     */
    public long countByStore(String storeId) {
        return appServiceOrderRepository.countBySotreIdAndDeleted(storeId, (byte) 0);
    }

    /**
     * Count orders by unionid
     */
    public long countByUnionid(String unionid) {
        return appServiceOrderRepository.countByUnionidAndDeleted(unionid, (byte) 0);
    }

    /**
     * Calculate total amount by store and date range
     */
    public BigDecimal calculateTotalAmountByStoreAndDateRange(String storeId, LocalDateTime startDate, LocalDateTime endDate) {
        return appServiceOrderRepository.calculateTotalAmountByStoreAndDateRange(storeId, startDate, endDate, (byte) 0);
    }

    /**
     * Advanced search with dynamic criteria (replaces MyBatis Example classes)
     */
    public Page<AppServiceOrder> searchOrders(String orderId, String unionid, String storeId, String contactName,
                                            String contactPhone, Short orderStatus, BigDecimal minAmount, BigDecimal maxAmount,
                                            LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        Specification<AppServiceOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Always filter out deleted records
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            
            // Add dynamic filters
            if (orderId != null && !orderId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("orderId"), orderId));
            }
            
            if (unionid != null && !unionid.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("unionid"), unionid));
            }
            
            if (storeId != null && !storeId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("sotreId"), storeId));
            }
            
            if (contactName != null && !contactName.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("contactName")), 
                    "%" + contactName.toLowerCase() + "%"
                ));
            }
            
            if (contactPhone != null && !contactPhone.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("contactPhone"), contactPhone));
            }
            
            if (orderStatus != null) {
                predicates.add(criteriaBuilder.equal(root.get("orderStatus"), orderStatus));
            }
            
            if (minAmount != null && maxAmount != null) {
                predicates.add(criteriaBuilder.between(root.get("orderAmount"), minAmount, maxAmount));
            } else if (minAmount != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("orderAmount"), minAmount));
            } else if (maxAmount != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("orderAmount"), maxAmount));
            }
            
            if (startDate != null && endDate != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), startDate, endDate));
            } else if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), startDate));
            } else if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), endDate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return appServiceOrderRepository.findAll(spec, pageable);
    }

    /**
     * Get all orders with pagination
     */
    public Page<AppServiceOrder> getAllOrders(Pageable pageable) {
        Specification<AppServiceOrder> spec = (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("deleted"), 0);
        return appServiceOrderRepository.findAll(spec, pageable);
    }

    /**
     * Update order status
     */
    public void updateOrderStatus(String orderId, Short newStatus) {
        Optional<AppServiceOrder> orderOpt = appServiceOrderRepository.findByOrderIdAndDeleted(orderId, (byte) 0);
        if (orderOpt.isPresent()) {
            AppServiceOrder order = orderOpt.get();
            order.setOrderStatus(newStatus);
            appServiceOrderRepository.save(order);
        }
    }

    /**
     * Batch save orders
     */
    public List<AppServiceOrder> saveAllOrders(List<AppServiceOrder> orders) {
        return appServiceOrderRepository.saveAll(orders);
    }

    /**
     * Find orders by multiple statuses
     */
    public List<AppServiceOrder> getOrdersByStatuses(List<Short> statuses) {
        Specification<AppServiceOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            predicates.add(root.get("orderStatus").in(statuses));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        return appServiceOrderRepository.findAll(spec);
    }

    /**
     * Find recent orders (last N days)
     */
    public List<AppServiceOrder> getRecentOrders(int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        LocalDateTime endDate = LocalDateTime.now();
        return appServiceOrderRepository.findByCreateTimeBetweenAndDeleted(startDate, endDate, (byte) 0);
    }
}
