package com.relle.db.service;

import com.relle.db.entity.ServiceOrder;
import com.relle.db.repository.ServiceOrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * JPA-based Service Order Service Implementation
 * This demonstrates how to migrate from MyBatis to JPA for order management
 */
@Service
@Transactional
public class AppServiceOrderJpaService {

    @Autowired
    private ServiceOrderRepository serviceOrderRepository;

    /**
     * Find order by order ID
     */
    public ServiceOrder getOrderByOrderId(String orderId) {
        return serviceOrderRepository.findByOrderIdAndDeleted(orderId, (byte) 0)
                .orElse(null);
    }

    /**
     * Find orders by unionid
     */
    public List<ServiceOrder> getOrdersByUnionid(String unionid) {
        return serviceOrderRepository.findByUnionidAndDeleted(unionid, (byte) 0);
    }

    /**
     * Find orders by store ID
     */
    public List<ServiceOrder> getOrdersByStoreId(String storeId) {
        return serviceOrderRepository.findBySotreIdAndDeleted(storeId, (byte) 0);
    }

    /**
     * Find orders by status
     */
    public List<ServiceOrder> getOrdersByStatus(Short status) {
        return serviceOrderRepository.findByOrderStatusAndDeleted(status, (byte) 0);
    }

    /**
     * Find orders by contact phone
     */
    public List<ServiceOrder> getOrdersByContactPhone(String contactPhone) {
        return serviceOrderRepository.findByContactPhoneAndDeleted(contactPhone, (byte) 0);
    }

    /**
     * Find orders by date range
     */
    public List<ServiceOrder> getOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return serviceOrderRepository.findByCreateTimeBetweenAndDeleted(startDate, endDate, (byte) 0);
    }

    /**
     * Find orders by unionid and date range
     */
    public List<ServiceOrder> getOrdersByUnionidAndDateRange(String unionid, LocalDateTime startDate, LocalDateTime endDate) {
        return serviceOrderRepository.findByUnionidAndDateRangeAndDeleted(unionid, startDate, endDate, (byte) 0);
    }

    /**
     * Find orders by store and date range
     */
    public List<ServiceOrder> getOrdersByStoreAndDateRange(String storeId, LocalDateTime startDate, LocalDateTime endDate) {
        return serviceOrderRepository.findByStoreAndDateRangeAndDeleted(storeId, startDate, endDate, (byte) 0);
    }

    /**
     * Find pending payment orders
     */
    public List<ServiceOrder> getPendingPaymentOrders() {
        return serviceOrderRepository.findPendingPaymentOrders((byte) 0);
    }

    /**
     * Find completed orders
     */
    public List<ServiceOrder> getCompletedOrders() {
        return serviceOrderRepository.findCompletedOrders((byte) 0);
    }

    /**
     * Save or update order
     */
    public ServiceOrder saveOrder(ServiceOrder order) {
        return serviceOrderRepository.save(order);
    }

    /**
     * Soft delete order
     */
    public void deleteOrder(Long id) {
        Optional<ServiceOrder> orderOpt = serviceOrderRepository.findById(id);
        if (orderOpt.isPresent()) {
            ServiceOrder order = orderOpt.get();
            order.setDeleted(1);
            serviceOrderRepository.save(order);
        }
    }

    /**
     * Check if order exists by order ID
     */
    public boolean existsByOrderId(String orderId) {
        return serviceOrderRepository.existsByOrderIdAndDeleted(orderId, (byte) 0);
    }

    /**
     * Count orders by status
     */
    public long countByStatus(Short status) {
        return serviceOrderRepository.countByOrderStatusAndDeleted(status, (byte) 0);
    }

    /**
     * Count orders by store
     */
    public long countByStore(String storeId) {
        return serviceOrderRepository.countBySotreIdAndDeleted(storeId, (byte) 0);
    }

    /**
     * Count orders by unionid
     */
    public long countByUnionid(String unionid) {
        return serviceOrderRepository.countByUnionidAndDeleted(unionid, (byte) 0);
    }

    /**
     * Calculate total amount by store and date range
     */
    public BigDecimal calculateTotalAmountByStoreAndDateRange(String storeId, LocalDateTime startDate, LocalDateTime endDate) {
        return serviceOrderRepository.calculateTotalAmountByStoreAndDateRange(storeId, startDate, endDate, (byte) 0);
    }

    /**
     * Advanced search with dynamic criteria (replaces MyBatis Example classes)
     */
    public Page<ServiceOrder> searchOrders(String orderId, String unionid, String storeId, String contactName,
                                            String contactPhone, Short orderStatus, BigDecimal minAmount, BigDecimal maxAmount,
                                            LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        Specification<ServiceOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Always filter out deleted records
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            
            // Add dynamic filters
            if (orderId != null && !orderId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("orderId"), orderId));
            }
            
            if (unionid != null && !unionid.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("unionid"), unionid));
            }
            
            if (storeId != null && !storeId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("sotreId"), storeId));
            }
            
            if (contactName != null && !contactName.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("contactName")), 
                    "%" + contactName.toLowerCase() + "%"
                ));
            }
            
            if (contactPhone != null && !contactPhone.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("contactPhone"), contactPhone));
            }
            
            if (orderStatus != null) {
                predicates.add(criteriaBuilder.equal(root.get("orderStatus"), orderStatus));
            }
            
            if (minAmount != null && maxAmount != null) {
                predicates.add(criteriaBuilder.between(root.get("orderAmount"), minAmount, maxAmount));
            } else if (minAmount != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("orderAmount"), minAmount));
            } else if (maxAmount != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("orderAmount"), maxAmount));
            }
            
            if (startDate != null && endDate != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), startDate, endDate));
            } else if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), startDate));
            } else if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), endDate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return serviceOrderRepository.findAll(spec, pageable);
    }

    /**
     * Get all orders with pagination
     */
    public Page<ServiceOrder> getAllOrders(Pageable pageable) {
        Specification<ServiceOrder> spec = (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("deleted"), 0);
        return serviceOrderRepository.findAll(spec, pageable);
    }

    /**
     * Update order status
     */
    public void updateOrderStatus(String orderId, Short newStatus) {
        Optional<ServiceOrder> orderOpt = serviceOrderRepository.findByOrderIdAndDeleted(orderId, (byte) 0);
        if (orderOpt.isPresent()) {
            ServiceOrder order = orderOpt.get();
            order.setOrderStatus(newStatus);
            serviceOrderRepository.save(order);
        }
    }

    /**
     * Batch save orders
     */
    public List<ServiceOrder> saveAllOrders(List<ServiceOrder> orders) {
        return serviceOrderRepository.saveAll(orders);
    }

    /**
     * Find orders by multiple statuses
     */
    public List<ServiceOrder> getOrdersByStatuses(List<Short> statuses) {
        Specification<ServiceOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            predicates.add(root.get("orderStatus").in(statuses));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        return serviceOrderRepository.findAll(spec);
    }

    /**
     * Find recent orders (last N days)
     */
    public List<ServiceOrder> getRecentOrders(int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        LocalDateTime endDate = LocalDateTime.now();
        return serviceOrderRepository.findByCreateTimeBetweenAndDeleted(startDate, endDate, (byte) 0);
    }
}
