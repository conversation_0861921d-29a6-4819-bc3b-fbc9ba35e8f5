package com.relle.db.service;

import com.relle.db.entity.AppEmployee;
import com.relle.db.repository.AppEmployeeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * JPA-based Employee Service Implementation
 * This demonstrates how to migrate from MyBatis to JPA
 */
@Service
@Transactional
public class AppEmployeeJpaService {

    @Autowired
    private AppEmployeeRepository appEmployeeRepository;

    /**
     * Find employee by OpenID (migrated from MyBatis)
     * Original MyBatis method: getAppEmployeeByOpenId
     */
    public AppEmployee getAppEmployeeByOpenId(String openId) {
        return appEmployeeRepository.findByEmployeeOpenIdAndDeleted(openId, (byte) 0)
                .orElse(null);
    }

    /**
     * Find employee by ID
     */
    public AppEmployee getAppEmployeeById(Long id) {
        return appEmployeeRepository.findById(id)
                .filter(employee -> employee.getDeleted() == 0)
                .orElse(null);
    }

    /**
     * Find employee by employee ID
     */
    public AppEmployee getAppEmployeeByEmployeeId(String employeeId) {
        return appEmployeeRepository.findByEmployeeIdAndDeleted(employeeId, (byte) 0)
                .orElse(null);
    }

    /**
     * Find employee by phone
     */
    public AppEmployee getAppEmployeeByPhone(String phone) {
        return appEmployeeRepository.findByEmployeePhoneAndDeleted(phone, (byte) 0)
                .orElse(null);
    }

    /**
     * Get all active employees
     */
    public List<AppEmployee> getAllActiveEmployees() {
        return appEmployeeRepository.findActiveEmployees((byte) 0);
    }

    /**
     * Get employees by department
     */
    public List<AppEmployee> getEmployeesByDepartment(Byte departmentId) {
        return appEmployeeRepository.findByDepartmentIdAndDeleted(departmentId, (byte) 0);
    }

    /**
     * Get employees by status
     */
    public List<AppEmployee> getEmployeesByStatus(Byte status) {
        return appEmployeeRepository.findByEmployeeStatusAndDeleted(status, (byte) 0);
    }

    /**
     * Search employees by name
     */
    public List<AppEmployee> searchEmployeesByName(String name) {
        return appEmployeeRepository.findByEmployeeNameContainingIgnoreCaseAndDeleted(name, (byte) 0);
    }

    /**
     * Save or update employee
     */
    public AppEmployee saveEmployee(AppEmployee employee) {
        return appEmployeeRepository.save(employee);
    }

    /**
     * Soft delete employee
     */
    public void deleteEmployee(Long id) {
        Optional<AppEmployee> employeeOpt = appEmployeeRepository.findById(id);
        if (employeeOpt.isPresent()) {
            AppEmployee employee = employeeOpt.get();
            employee.setDeleted((byte) 1);
            appEmployeeRepository.save(employee);
        }
    }

    /**
     * Check if employee exists by employee ID
     */
    public boolean existsByEmployeeId(String employeeId) {
        return appEmployeeRepository.existsByEmployeeIdAndDeleted(employeeId, (byte) 0);
    }

    /**
     * Check if employee exists by phone
     */
    public boolean existsByPhone(String phone) {
        return appEmployeeRepository.existsByEmployeePhoneAndDeleted(phone, (byte) 0);
    }

    /**
     * Count employees by status
     */
    public long countByStatus(Byte status) {
        return appEmployeeRepository.countByEmployeeStatusAndDeleted(status, (byte) 0);
    }

    /**
     * Count employees by department
     */
    public long countByDepartment(Byte departmentId) {
        return appEmployeeRepository.countByDepartmentIdAndDeleted(departmentId, (byte) 0);
    }

    /**
     * Advanced search with dynamic criteria (replaces MyBatis Example classes)
     */
    public Page<AppEmployee> searchEmployees(String name, String employeeId, String phone, 
                                           Byte status, Byte departmentId, Pageable pageable) {
        Specification<AppEmployee> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Always filter out deleted records
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            
            // Add dynamic filters
            if (name != null && !name.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeName")), 
                    "%" + name.toLowerCase() + "%"
                ));
            }
            
            if (employeeId != null && !employeeId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("employeeId"), employeeId));
            }
            
            if (phone != null && !phone.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("employeePhone"), phone));
            }
            
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeStatus"), status));
            }
            
            if (departmentId != null) {
                predicates.add(criteriaBuilder.equal(root.get("departmentId"), departmentId));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return appEmployeeRepository.findAll(spec, pageable);
    }

    /**
     * Get all employees with pagination
     */
    public Page<AppEmployee> getAllEmployees(Pageable pageable) {
        Specification<AppEmployee> spec = (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("deleted"), 0);
        return appEmployeeRepository.findAll(spec, pageable);
    }
}
