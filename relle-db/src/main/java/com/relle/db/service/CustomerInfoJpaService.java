package com.relle.db.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.relle.db.entity.CustomerInfo;
import com.relle.db.repository.CustomerInfoRepository;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * JPA-based Customer Info Service Implementation
 * This demonstrates how to migrate from MyBatis to JPA
 */
@Service
@Transactional
public class CustomerInfoJpaService {

    @Autowired
    private CustomerInfoRepository customerInfoRepository;

    /**
     * Find customer by unionid
     */
    public CustomerInfo getCustomerByUnionid(String unionid) {
        return customerInfoRepository.findByUnionidAndDeleted(unionid, 0)
                .orElse(null);
    }

    /**
     * Find customer by mini openid
     */
    public CustomerInfo getCustomerByMiniOpenid(String miniOpenid) {
        return customerInfoRepository.findByMiniOpenidAndDeleted(miniOpenid,  0)
                .orElse(null);
    }

    /**
     * Find customer by customer ID
     */
    public CustomerInfo getCustomerByCustomerId(String customerId) {
        return customerInfoRepository.findByCustomerIdAndDeleted(customerId, 0)
                .orElse(null);
    }

    /**
     * Find customer by phone
     */
    public CustomerInfo getCustomerByPhone(String phone) {
        return customerInfoRepository.findByWechatPhoneAndDeleted(phone, 0)
                .orElse(null);
    }

    /**
     * Find customer by ID
     */
    public CustomerInfo getCustomerById(Long id) {
        return customerInfoRepository.findById(id)
                .filter(customer -> customer.getDeleted() == 0)
                .orElse(null);
    }

    /**
     * Get all active customers
     */
    public List<CustomerInfo> getAllActiveCustomers() {
        return customerInfoRepository.findByDeleted( 0);
    }

    /**
     * Search customers by wechat nickname
     */
    public List<CustomerInfo> searchCustomersByWechatNickname(String nickname) {
        return customerInfoRepository.findByWechatNicknameContainingIgnoreCaseAndDeleted(nickname,0);
    }

    /**
     * Search customers by user name
     */
    public List<CustomerInfo> searchCustomersByUserName(String userName) {
        return customerInfoRepository.findByUserNameContainingIgnoreCaseAndDeleted(userName, 0);
    }

    /**
     * Get customers by gender
     */
    public List<CustomerInfo> getCustomersByGender(Integer gender) {
        return customerInfoRepository.findByUserGenderAndDeleted(gender, 0);
    }

    /**
     * Save or update customer
     */
    public CustomerInfo saveCustomer(CustomerInfo customer) {
        return customerInfoRepository.save(customer);
    }

    /**
     * Soft delete customer
     */
    public void deleteCustomer(Long id) {
        Optional<CustomerInfo> customerOpt = customerInfoRepository.findById(id);
        if (customerOpt.isPresent()) {
            CustomerInfo customer = customerOpt.get();
            customer.setDeleted(1);
            customerInfoRepository.save(customer);
        }
    }

    /**
     * Check if customer exists by unionid
     */
    public boolean existsByUnionid(String unionid) {
        return customerInfoRepository.existsByUnionidAndDeleted(unionid, 0);
    }

    /**
     * Check if customer exists by mini openid
     */
    public boolean existsByMiniOpenid(String miniOpenid) {
        return customerInfoRepository.existsByMiniOpenidAndDeleted(miniOpenid,  0);
    }

    /**
     * Check if customer exists by phone
     */
    public boolean existsByPhone(String phone) {
        return customerInfoRepository.existsByWechatPhoneAndDeleted(phone, 0);
    }

    /**
     * Count all active customers
     */
    public long countActiveCustomers() {
        return customerInfoRepository.countByDeleted(0);
    }

    /**
     * Advanced search with dynamic criteria (replaces MyBatis Example classes)
     */
    public Page<CustomerInfo> searchCustomers(String unionid, String miniOpenid, String customerId,
                                               String wechatNickname, String userName, String phone,
                                               Byte gender, Pageable pageable) {
        Specification<CustomerInfo> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Always filter out deleted records
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            
            // Add dynamic filters
            if (unionid != null && !unionid.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("unionid"), unionid));
            }
            
            if (miniOpenid != null && !miniOpenid.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("miniOpenid"), miniOpenid));
            }
            
            if (customerId != null && !customerId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("customerId"), customerId));
            }
            
            if (wechatNickname != null && !wechatNickname.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("wechatNickname")), 
                    "%" + wechatNickname.toLowerCase() + "%"
                ));
            }
            
            if (userName != null && !userName.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("userName")), 
                    "%" + userName.toLowerCase() + "%"
                ));
            }
            
            if (phone != null && !phone.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("wechatPhone"), phone));
            }
            
            if (gender != null) {
                predicates.add(criteriaBuilder.equal(root.get("userGender"), gender));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return customerInfoRepository.findAll(spec, pageable);
    }

    /**
     * Get all customers with pagination
     */
    public Page<CustomerInfo> getAllCustomers(Pageable pageable) {
        Specification<CustomerInfo> spec = (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("deleted"), 0);
        return customerInfoRepository.findAll(spec, pageable);
    }

    /**
     * Batch save customers
     */
    public List<CustomerInfo> saveAllCustomers(List<CustomerInfo> customers) {
        return customerInfoRepository.saveAll(customers);
    }

    /**
     * Find customers by unionid list
     */
    public List<CustomerInfo> getCustomersByUnionids(List<String> unionids) {
        Specification<CustomerInfo> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            predicates.add(root.get("unionid").in(unionids));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        return customerInfoRepository.findAll(spec);
    }
}
