package com.relle.db.service;

import com.relle.db.entity.AppCustomerInfo;
import com.relle.db.repository.AppCustomerInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * JPA-based Customer Info Service Implementation
 * This demonstrates how to migrate from MyBatis to JPA
 */
@Service
@Transactional
public class AppCustomerInfoJpaService {

    @Autowired
    private AppCustomerInfoRepository appCustomerInfoRepository;

    /**
     * Find customer by unionid
     */
    public AppCustomerInfo getCustomerByUnionid(String unionid) {
        return appCustomerInfoRepository.findByUnionidAndDeleted(unionid, (byte) 0)
                .orElse(null);
    }

    /**
     * Find customer by mini openid
     */
    public AppCustomerInfo getCustomerByMiniOpenid(String miniOpenid) {
        return appCustomerInfoRepository.findByMiniOpenidAndDeleted(miniOpenid, (byte) 0)
                .orElse(null);
    }

    /**
     * Find customer by customer ID
     */
    public AppCustomerInfo getCustomerByCustomerId(String customerId) {
        return appCustomerInfoRepository.findByCustomerIdAndDeleted(customerId, (byte) 0)
                .orElse(null);
    }

    /**
     * Find customer by phone
     */
    public AppCustomerInfo getCustomerByPhone(String phone) {
        return appCustomerInfoRepository.findByWechatPhoneAndDeleted(phone, (byte) 0)
                .orElse(null);
    }

    /**
     * Find customer by ID
     */
    public AppCustomerInfo getCustomerById(Long id) {
        return appCustomerInfoRepository.findById(id)
                .filter(customer -> customer.getDeleted() == 0)
                .orElse(null);
    }

    /**
     * Get all active customers
     */
    public List<AppCustomerInfo> getAllActiveCustomers() {
        return appCustomerInfoRepository.findByDeleted((byte) 0);
    }

    /**
     * Search customers by wechat nickname
     */
    public List<AppCustomerInfo> searchCustomersByWechatNickname(String nickname) {
        return appCustomerInfoRepository.findByWechatNicknameContainingIgnoreCaseAndDeleted(nickname, (byte) 0);
    }

    /**
     * Search customers by user name
     */
    public List<AppCustomerInfo> searchCustomersByUserName(String userName) {
        return appCustomerInfoRepository.findByUserNameContainingIgnoreCaseAndDeleted(userName, (byte) 0);
    }

    /**
     * Get customers by gender
     */
    public List<AppCustomerInfo> getCustomersByGender(Byte gender) {
        return appCustomerInfoRepository.findByUserGenderAndDeleted(gender, (byte) 0);
    }

    /**
     * Save or update customer
     */
    public AppCustomerInfo saveCustomer(AppCustomerInfo customer) {
        return appCustomerInfoRepository.save(customer);
    }

    /**
     * Soft delete customer
     */
    public void deleteCustomer(Long id) {
        Optional<AppCustomerInfo> customerOpt = appCustomerInfoRepository.findById(id);
        if (customerOpt.isPresent()) {
            AppCustomerInfo customer = customerOpt.get();
            customer.setDeleted((byte) 1);
            appCustomerInfoRepository.save(customer);
        }
    }

    /**
     * Check if customer exists by unionid
     */
    public boolean existsByUnionid(String unionid) {
        return appCustomerInfoRepository.existsByUnionidAndDeleted(unionid, (byte) 0);
    }

    /**
     * Check if customer exists by mini openid
     */
    public boolean existsByMiniOpenid(String miniOpenid) {
        return appCustomerInfoRepository.existsByMiniOpenidAndDeleted(miniOpenid, (byte) 0);
    }

    /**
     * Check if customer exists by phone
     */
    public boolean existsByPhone(String phone) {
        return appCustomerInfoRepository.existsByWechatPhoneAndDeleted(phone, (byte) 0);
    }

    /**
     * Count all active customers
     */
    public long countActiveCustomers() {
        return appCustomerInfoRepository.countByDeleted((byte) 0);
    }

    /**
     * Advanced search with dynamic criteria (replaces MyBatis Example classes)
     */
    public Page<AppCustomerInfo> searchCustomers(String unionid, String miniOpenid, String customerId,
                                               String wechatNickname, String userName, String phone,
                                               Byte gender, Pageable pageable) {
        Specification<AppCustomerInfo> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Always filter out deleted records
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            
            // Add dynamic filters
            if (unionid != null && !unionid.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("unionid"), unionid));
            }
            
            if (miniOpenid != null && !miniOpenid.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("miniOpenid"), miniOpenid));
            }
            
            if (customerId != null && !customerId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("customerId"), customerId));
            }
            
            if (wechatNickname != null && !wechatNickname.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("wechatNickname")), 
                    "%" + wechatNickname.toLowerCase() + "%"
                ));
            }
            
            if (userName != null && !userName.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("userName")), 
                    "%" + userName.toLowerCase() + "%"
                ));
            }
            
            if (phone != null && !phone.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("wechatPhone"), phone));
            }
            
            if (gender != null) {
                predicates.add(criteriaBuilder.equal(root.get("userGender"), gender));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return appCustomerInfoRepository.findAll(spec, pageable);
    }

    /**
     * Get all customers with pagination
     */
    public Page<AppCustomerInfo> getAllCustomers(Pageable pageable) {
        Specification<AppCustomerInfo> spec = (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("deleted"), 0);
        return appCustomerInfoRepository.findAll(spec, pageable);
    }

    /**
     * Batch save customers
     */
    public List<AppCustomerInfo> saveAllCustomers(List<AppCustomerInfo> customers) {
        return appCustomerInfoRepository.saveAll(customers);
    }

    /**
     * Find customers by unionid list
     */
    public List<AppCustomerInfo> getCustomersByUnionids(List<String> unionids) {
        Specification<AppCustomerInfo> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            predicates.add(root.get("unionid").in(unionids));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        return appCustomerInfoRepository.findAll(spec);
    }
}
