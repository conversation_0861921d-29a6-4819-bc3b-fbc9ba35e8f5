package com.relle.db.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.relle.db.entity.EmployeeInfo;
import com.relle.db.repository.EmployeeInfoRepository;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * JPA-based Employee Service Implementation
 * This demonstrates how to migrate from MyBatis to JPA
 */
@Service
@Transactional
public class EmployeeInfoJpaService {

    @Autowired
    private EmployeeInfoRepository appEmployeeRepository;

    /**
     * Find employee by OpenID (migrated from MyBatis)
     * Original MyBatis method: getAppEmployeeByOpenId
     */
    public EmployeeInfo getAppEmployeeByOpenId(String openId) {
        return appEmployeeRepository.findByEmployeeOpenIdAndDeleted(openId,  0)
                .orElse(null);
    }

    /**
     * Find employee by ID
     */
    public EmployeeInfo getAppEmployeeById(Long id) {
        return appEmployeeRepository.findById(id)
                .filter(employee -> employee.getDeleted() == 0)
                .orElse(null);
    }

    /**
     * Find employee by employee ID
     */
    public EmployeeInfo getAppEmployeeByEmployeeId(String employeeId) {
        return appEmployeeRepository.findByEmployeeIdAndDeleted(employeeId,  0)
                .orElse(null);
    }

    /**
     * Find employee by phone
     */
    public EmployeeInfo getAppEmployeeByPhone(String phone) {
        return appEmployeeRepository.findByEmployeePhoneAndDeleted(phone,  0)
                .orElse(null);
    }

    /**
     * Get all active employees
     */
    public List<EmployeeInfo> getAllActiveEmployees() {
        return appEmployeeRepository.findActiveEmployees( 0);
    }

    /**
     * Get employees by department
     */
    public List<EmployeeInfo> getEmployeesByDepartment(Integer departmentId) {
        return appEmployeeRepository.findByDepartmentIdAndDeleted(departmentId,  0);
    }

    /**
     * Get employees by status
     */
    public List<EmployeeInfo> getEmployeesByStatus(Integer status) {
        return appEmployeeRepository.findByEmployeeStatusAndDeleted(status,  0);
    }

    /**
     * Search employees by name
     */
    public List<EmployeeInfo> searchEmployeesByName(String name) {
        return appEmployeeRepository.findByEmployeeNameContainingIgnoreCaseAndDeleted(name,  0);
    }

    /**
     * Save or update employee
     */
    public EmployeeInfo saveEmployee(EmployeeInfo employee) {
        return appEmployeeRepository.save(employee);
    }

    /**
     * Soft delete employee
     */
    public void deleteEmployee(Long id) {
        Optional<EmployeeInfo> employeeOpt = appEmployeeRepository.findById(id);
        if (employeeOpt.isPresent()) {
            EmployeeInfo employee = employeeOpt.get();
            employee.setDeleted(1);
            appEmployeeRepository.save(employee);
        }
    }

    /**
     * Check if employee exists by employee ID
     */
    public boolean existsByEmployeeId(String employeeId) {
        return appEmployeeRepository.existsByEmployeeIdAndDeleted(employeeId,  0);
    }

    /**
     * Check if employee exists by phone
     */
    public boolean existsByPhone(String phone) {
        return appEmployeeRepository.existsByEmployeePhoneAndDeleted(phone,  0);
    }

    /**
     * Count employees by status
     */
    public long countByStatus(Integer status) {
        return appEmployeeRepository.countByEmployeeStatusAndDeleted(status,  0);
    }

    /**
     * Count employees by department
     */
    public long countByDepartment(Byte departmentId) {
        return appEmployeeRepository.countByDepartmentIdAndDeleted(departmentId,  0);
    }

    /**
     * Advanced search with dynamic criteria (replaces MyBatis Example classes)
     */
    public Page<EmployeeInfo> searchEmployees(String name, String employeeId, String phone, 
                                           Byte status, Byte departmentId, Pageable pageable) {
        Specification<EmployeeInfo> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // Always filter out deleted records
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            
            // Add dynamic filters
            if (name != null && !name.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeName")), 
                    "%" + name.toLowerCase() + "%"
                ));
            }
            
            if (employeeId != null && !employeeId.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("employeeId"), employeeId));
            }
            
            if (phone != null && !phone.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("employeePhone"), phone));
            }
            
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("employeeStatus"), status));
            }
            
            if (departmentId != null) {
                predicates.add(criteriaBuilder.equal(root.get("departmentId"), departmentId));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return appEmployeeRepository.findAll(spec, pageable);
    }

    /**
     * Get all employees with pagination
     */
    public Page<EmployeeInfo> getAllEmployees(Pageable pageable) {
        Specification<EmployeeInfo> spec = (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("deleted"), 0);
        return appEmployeeRepository.findAll(spec, pageable);
    }
}
