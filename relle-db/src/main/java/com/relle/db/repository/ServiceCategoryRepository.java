package com.relle.db.repository;

import com.relle.db.entity.ServiceCategory;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * ServiceCategory JPA Repository
 * Provides CRUD operations and custom queries for service category information
 */
@Repository
public interface ServiceCategoryRepository extends JpaRepository<ServiceCategory, Long>, JpaSpecificationExecutor<ServiceCategory> {

    /**
     * Find category by service category ID and not deleted
     */
    Optional<ServiceCategory> findByServiceCategoryIdAndDeleted(String serviceCategoryId, Byte deleted);

    /**
     * Find categories by parent ID and not deleted
     */
    List<ServiceCategory> findByServiceCategoryParentIdAndDeleted(String serviceCategoryParentId, Byte deleted);

    /**
     * Find categories by name containing text (case insensitive) and not deleted
     */
    @Query("SELECT c FROM ServiceCategory c WHERE LOWER(c.serviceCategoryName) LIKE LOWER(CONCAT('%', :name, '%')) AND c.deleted = :deleted")
    List<ServiceCategory> findByServiceCategoryNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Byte deleted);

    /**
     * Find all categories that are not deleted
     */
    List<ServiceCategory> findByDeleted(Byte deleted);

    /**
     * Find root categories (parent ID is null or empty) and not deleted
     */
    @Query("SELECT c FROM ServiceCategory c WHERE (c.serviceCategoryParentId IS NULL OR c.serviceCategoryParentId = '') AND c.deleted = :deleted")
    List<ServiceCategory> findRootCategories(@Param("deleted") Byte deleted);

    /**
     * Count categories by parent ID and not deleted
     */
    long countByServiceCategoryParentIdAndDeleted(String serviceCategoryParentId, Byte deleted);

    /**
     * Check if category exists by service category ID and not deleted
     */
    boolean existsByServiceCategoryIdAndDeleted(String serviceCategoryId, Byte deleted);

    /**
     * Find categories ordered by name and not deleted
     */
    @Query("SELECT c FROM ServiceCategory c WHERE c.deleted = :deleted ORDER BY c.serviceCategoryName ASC")
    List<ServiceCategory> findByDeletedOrderByServiceCategoryNameAsc(@Param("deleted") Byte deleted);

    /**
     * Find categories by parent ID ordered by name and not deleted
     */
    @Query("SELECT c FROM ServiceCategory c WHERE c.serviceCategoryParentId = :parentId AND c.deleted = :deleted ORDER BY c.serviceCategoryName ASC")
    List<ServiceCategory> findByParentIdOrderByNameAsc(@Param("parentId") String parentId, @Param("deleted") Byte deleted);
}
