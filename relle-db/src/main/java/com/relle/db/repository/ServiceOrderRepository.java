package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.ServiceOrder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * ServiceOrder JPA Repository
 * Provides CRUD operations and custom queries for service order information
 */
@Repository
public interface ServiceOrderRepository extends JpaRepository<ServiceOrder, Long>, JpaSpecificationExecutor<ServiceOrder> {

    /**
     * Find order by order ID and not deleted
     */
    Optional<ServiceOrder> findByOrderIdAndDeleted(String orderId, Integer deleted);

    /**
     * Find orders by unionid and not deleted
     */
    List<ServiceOrder> findByUnionidAndDeleted(String unionid, Integer deleted);

    /**
     * Find orders by store ID and not deleted
     */
    List<ServiceOrder> findBySotreIdAndDeleted(String sotreId, Integer deleted);

    /**
     * Find orders by room ID and not deleted
     */
    List<ServiceOrder> findByRoomIdAndDeleted(String roomId, Integer deleted);

    /**
     * Find orders by order status and not deleted
     */
    List<ServiceOrder> findByOrderStatusAndDeleted(Short orderStatus, Integer deleted);

    /**
     * Find orders by contact phone and not deleted
     */
    List<ServiceOrder> findByContactPhoneAndDeleted(String contactPhone, Integer deleted);

    /**
     * Find orders by write-off status and not deleted
     */
    List<ServiceOrder> findByWriteOffAndDeleted(Integer writeOff, Integer deleted);

    /**
     * Find all orders that are not deleted
     */
    List<ServiceOrder> findByDeleted(Integer deleted);

    /**
     * Find orders by unionid and status and not deleted
     */
    List<ServiceOrder> findByUnionidAndOrderStatusAndDeleted(String unionid, Short orderStatus, Integer deleted);

    /**
     * Find orders by store and status and not deleted
     */
    List<ServiceOrder> findBySotreIdAndOrderStatusAndDeleted(String sotreId, Short orderStatus, Integer deleted);

    /**
     * Find orders by amount range and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE o.orderAmount BETWEEN :minAmount AND :maxAmount AND o.deleted = :deleted")
    List<ServiceOrder> findByOrderAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Integer deleted);

    /**
     * Find orders by contact name containing text (case insensitive) and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE LOWER(o.contactName) LIKE LOWER(CONCAT('%', :name, '%')) AND o.deleted = :deleted")
    List<ServiceOrder> findByContactNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Integer deleted);

    /**
     * Find orders created between dates and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE o.createTime BETWEEN :startDate AND :endDate AND o.deleted = :deleted")
    List<ServiceOrder> findByCreateTimeBetweenAndDeleted(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Integer deleted);

    /**
     * Find orders by customer source and not deleted
     */
    List<ServiceOrder> findByCustomerSourceAndDeleted(String customerSource, Integer deleted);

    /**
     * Find orders by source order ID and not deleted
     */
    List<ServiceOrder> findBySourceOrderIdAndDeleted(String sourceOrderId, Integer deleted);

    /**
     * Find orders written off by user and not deleted
     */
    List<ServiceOrder> findByWriteOffUserAndDeleted(String writeOffUser, Integer deleted);

    /**
     * Count orders by status and not deleted
     */
    long countByOrderStatusAndDeleted(Short orderStatus, Integer deleted);

    /**
     * Count orders by store and not deleted
     */
    long countBySotreIdAndDeleted(String sotreId, Integer deleted);

    /**
     * Count orders by unionid and not deleted
     */
    long countByUnionidAndDeleted(String unionid, Integer deleted);

    /**
     * Check if order exists by order ID and not deleted
     */
    boolean existsByOrderIdAndDeleted(String orderId, Integer deleted);

    /**
     * Find pending payment orders (status = 1) and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE o.orderStatus = 1 AND o.deleted = :deleted")
    List<ServiceOrder> findPendingPaymentOrders(@Param("deleted") Integer deleted);

    /**
     * Find completed orders (status = 8) and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE o.orderStatus = 8 AND o.deleted = :deleted")
    List<ServiceOrder> findCompletedOrders(@Param("deleted") Integer deleted);

    /**
     * Find orders by unionid and date range and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE o.unionid = :unionid AND o.createTime BETWEEN :startDate AND :endDate AND o.deleted = :deleted ORDER BY o.createTime DESC")
    List<ServiceOrder> findByUnionidAndDateRangeAndDeleted(@Param("unionid") String unionid, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Integer deleted);

    /**
     * Find orders by store and date range and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE o.sotreId = :sotreId AND o.createTime BETWEEN :startDate AND :endDate AND o.deleted = :deleted ORDER BY o.createTime DESC")
    List<ServiceOrder> findByStoreAndDateRangeAndDeleted(@Param("sotreId") String sotreId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Integer deleted);

    /**
     * Calculate total amount by store and date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(o.orderAmount), 0) FROM ServiceOrder o WHERE o.sotreId = :sotreId AND o.createTime BETWEEN :startDate AND :endDate AND o.deleted = :deleted")
    BigDecimal calculateTotalAmountByStoreAndDateRange(@Param("sotreId") String sotreId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Integer deleted);

    /**
     * Find orders ordered by creation time descending and not deleted
     */
    @Query("SELECT o FROM ServiceOrder o WHERE o.deleted = :deleted ORDER BY o.createTime DESC")
    List<ServiceOrder> findByDeletedOrderByCreateTimeDesc(@Param("deleted") Integer deleted);
}
