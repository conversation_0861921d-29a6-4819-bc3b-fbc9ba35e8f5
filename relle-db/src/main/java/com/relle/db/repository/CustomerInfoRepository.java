package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.CustomerInfo;

import java.util.List;
import java.util.Optional;

/**
 * CustomerInfo JPA Repository
 * Provides CRUD operations and custom queries for customer information
 */
@Repository
public interface CustomerInfoRepository extends JpaRepository<CustomerInfo, Long>, JpaSpecificationExecutor<CustomerInfo> {

    /**
     * Find customer by unionid and not deleted
     */
    Optional<CustomerInfo> findByUnionidAndDeleted(String unionid, Integer deleted);

    /**
     * Find customer by mini openid and not deleted
     */
    Optional<CustomerInfo> findByMiniOpenidAndDeleted(String miniOpenid, Integer deleted);

    /**
     * Find customer by customer ID and not deleted
     */
    Optional<CustomerInfo> findByCustomerIdAndDeleted(String customerId, Integer deleted);

    /**
     * Find customer by wechat phone and not deleted
     */
    Optional<CustomerInfo> findByWechatPhoneAndDeleted(String wechatPhone, Integer deleted);

    /**
     * Find all customers that are not deleted
     */
    List<CustomerInfo> findByDeleted(Integer deleted);

    /**
     * Find customers by unionid (including deleted ones)
     */
    List<CustomerInfo> findByUnionid(String unionid);

    /**
     * Count customers by deleted status
     */
    long countByDeleted(Integer deleted);

    /**
     * Find customers by wechat nickname containing text (case insensitive) and not deleted
     */
    @Query("SELECT c FROM CustomerInfo c WHERE LOWER(c.wechatNickname) LIKE LOWER(CONCAT('%', :nickname, '%')) AND c.deleted = :deleted")
    List<CustomerInfo> findByWechatNicknameContainingIgnoreCaseAndDeleted(@Param("nickname") String nickname, @Param("deleted") Integer deleted);

    /**
     * Find customers by user name containing text (case insensitive) and not deleted
     */
    @Query("SELECT c FROM CustomerInfo c WHERE LOWER(c.userName) LIKE LOWER(CONCAT('%', :userName, '%')) AND c.deleted = :deleted")
    List<CustomerInfo> findByUserNameContainingIgnoreCaseAndDeleted(@Param("userName") String userName, @Param("deleted") Integer deleted);

    /**
     * Find customers by gender and not deleted
     */
    List<CustomerInfo> findByUserGenderAndDeleted(Integer userGender, Integer deleted);

    /**
     * Check if customer exists by unionid and not deleted
     */
    boolean existsByUnionidAndDeleted(String unionid, Integer deleted);

    /**
     * Check if customer exists by mini openid and not deleted
     */
    boolean existsByMiniOpenidAndDeleted(String miniOpenid, Integer deleted);

    /**
     * Check if customer exists by wechat phone and not deleted
     */
    boolean existsByWechatPhoneAndDeleted(String wechatPhone, Integer deleted);
}
