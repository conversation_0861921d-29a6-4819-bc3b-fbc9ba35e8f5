package com.relle.db.repository;

import com.relle.db.entity.ServiceItem;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * ServiceItem JPA Repository
 * Provides CRUD operations and custom queries for service item information
 */
@Repository
public interface ServiceItemRepository extends JpaRepository<ServiceItem, Long>, JpaSpecificationExecutor<ServiceItem> {

    /**
     * Find service item by service ID and not deleted
     */
    Optional<ServiceItem> findByServiceIdAndDeleted(String serviceId, Integer deleted);

    /**
     * Find service items by service name containing text (case insensitive) and not deleted
     */
    @Query("SELECT s FROM ServiceItem s WHERE LOWER(s.serviceName) LIKE LOWER(CONCAT('%', :name, '%')) AND s.deleted = :deleted")
    List<ServiceItem> findByServiceNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Integer deleted);

    /**
     * Find service items by service status and not deleted
     */
    List<ServiceItem> findByServiceStatusAndDeleted(Integer serviceStatus, Integer deleted);

    /**
     * Find service items by service type and not deleted
     */
    List<ServiceItem> findByServiceTypeAndDeleted(String serviceType, Integer deleted);

    /**
     * Find service items by channel type and not deleted
     */
    List<ServiceItem> findByChannelTypeAndDeleted(Integer channelType, Integer deleted);

    /**
     * Find all service items that are not deleted
     */
    List<ServiceItem> findByDeleted(Integer deleted);

    /**
     * Find service items by price range and not deleted
     */
    @Query("SELECT s FROM ServiceItem s WHERE s.servicePrice BETWEEN :minPrice AND :maxPrice AND s.deleted = :deleted")
    List<ServiceItem> findByServicePriceBetweenAndDeleted(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, @Param("deleted") Integer deleted);

    /**
     * Find service items by tag tree containing text and not deleted
     */
    @Query("SELECT s FROM ServiceItem s WHERE s.serviceTagtree LIKE CONCAT('%', :tag, '%') AND s.deleted = :deleted")
    List<ServiceItem> findByServiceTagtreeContainingAndDeleted(@Param("tag") String tag, @Param("deleted") Integer deleted);

    /**
     * Find service items that can be added and not deleted
     */
    List<ServiceItem> findByCanAddItemAndDeleted(Integer canAddItem, Integer deleted);

    /**
     * Find service items by restricted role and not deleted
     */
    List<ServiceItem> findByRestrictedRoleAndDeleted(Integer restrictedRole, Integer deleted);

    /**
     * Find service items ordered by show sort and not deleted
     */
    @Query("SELECT s FROM ServiceItem s WHERE s.deleted = :deleted ORDER BY s.serviceShowSort ASC")
    List<ServiceItem> findByDeletedOrderByServiceShowSortAsc(@Param("deleted") Integer deleted);

    /**
     * Find active service items (status = 1) and not deleted
     */
    @Query("SELECT s FROM ServiceItem s WHERE s.serviceStatus = 1 AND s.deleted = :deleted")
    List<ServiceItem> findActiveServiceItems(@Param("deleted") Integer deleted);

    /**
     * Count service items by status and not deleted
     */
    long countByServiceStatusAndDeleted(Integer serviceStatus, Integer deleted);

    /**
     * Count service items by type and not deleted
     */
    long countByServiceTypeAndDeleted(String serviceType, Integer deleted);

    /**
     * Check if service item exists by service ID and not deleted
     */
    boolean existsByServiceIdAndDeleted(String serviceId, Integer deleted);

    /**
     * Find service items by duration range and not deleted
     */
    @Query("SELECT s FROM ServiceItem s WHERE s.serviceDurationSec BETWEEN :minDuration AND :maxDuration AND s.deleted = :deleted")
    List<ServiceItem> findByServiceDurationSecBetweenAndDeleted(@Param("minDuration") Integer minDuration, @Param("maxDuration") Integer maxDuration, @Param("deleted") Integer deleted);
}
