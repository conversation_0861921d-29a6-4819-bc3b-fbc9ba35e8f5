package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.CouponGive;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * CouponGive JPA Repository
 * Provides CRUD operations and custom queries for coupon give/distribution information
 */
@Repository
public interface CouponGiveRepository extends JpaRepository<CouponGive, Long>, JpaSpecificationExecutor<CouponGive> {

    /**
     * Find coupon give by coupon ID and not deleted
     */
    List<CouponGive> findByCouponIdAndDeleted(String couponId, Byte deleted);

    /**
     * Find coupon gives by give user unionid and not deleted
     */
    List<CouponGive> findByGiveUserUnionidAndDeleted(String giveUserUnionid, Byte deleted);

    /**
     * Find coupon gives by receive user unionid and not deleted
     */
    List<CouponGive> findByReceiveUserUnionidAndDeleted(String receiveUserUnionid, Byte deleted);

    /**
     * Find coupon gives by give status and not deleted
     */
    List<CouponGive> findByGiveStatusAndDeleted(Byte giveStatus, Byte deleted);

    /**
     * Find coupon give by coupon receive ID and not deleted
     */
    Optional<CouponGive> findByCouponReceiveIdAndDeleted(String couponReceiveId, Byte deleted);

    /**
     * Find all coupon gives that are not deleted
     */
    List<CouponGive> findByDeleted(Byte deleted);

    /**
     * Find coupon gives by give time range and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.giveTime BETWEEN :startTime AND :endTime AND c.deleted = :deleted")
    List<CouponGive> findByGiveTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find coupon gives by receive time range and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.receiveTime BETWEEN :startTime AND :endTime AND c.deleted = :deleted")
    List<CouponGive> findByReceiveTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find pending coupon gives (status = 1) and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.giveStatus = 1 AND c.deleted = :deleted")
    List<CouponGive> findPendingCouponGives(@Param("deleted") Byte deleted);

    /**
     * Find received coupon gives (status = 2) and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.giveStatus = 2 AND c.deleted = :deleted")
    List<CouponGive> findReceivedCouponGives(@Param("deleted") Byte deleted);

    /**
     * Find returned coupon gives (status = 4) and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.giveStatus = 4 AND c.deleted = :deleted")
    List<CouponGive> findReturnedCouponGives(@Param("deleted") Byte deleted);

    /**
     * Count coupon gives by give user and not deleted
     */
    long countByGiveUserUnionidAndDeleted(String giveUserUnionid, Byte deleted);

    /**
     * Count coupon gives by receive user and not deleted
     */
    long countByReceiveUserUnionidAndDeleted(String receiveUserUnionid, Byte deleted);

    /**
     * Count coupon gives by status and not deleted
     */
    long countByGiveStatusAndDeleted(Byte giveStatus, Byte deleted);

    /**
     * Check if coupon give exists by coupon receive ID and not deleted
     */
    boolean existsByCouponReceiveIdAndDeleted(String couponReceiveId, Byte deleted);

    /**
     * Find coupon gives by give user and status and not deleted
     */
    List<CouponGive> findByGiveUserUnionidAndGiveStatusAndDeleted(String giveUserUnionid, Byte giveStatus, Byte deleted);

    /**
     * Find coupon gives by receive user and status and not deleted
     */
    List<CouponGive> findByReceiveUserUnionidAndGiveStatusAndDeleted(String receiveUserUnionid, Byte giveStatus, Byte deleted);

    /**
     * Find coupon gives ordered by give time descending and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.deleted = :deleted ORDER BY c.giveTime DESC")
    List<CouponGive> findByDeletedOrderByGiveTimeDesc(@Param("deleted") Byte deleted);

    /**
     * Find unreceived coupon gives (receive time is null) and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.receiveTime IS NULL AND c.deleted = :deleted")
    List<CouponGive> findUnreceivedCouponGives(@Param("deleted") Byte deleted);

    /**
     * Find expired unreceived coupon gives and not deleted
     */
    @Query("SELECT c FROM CouponGive c WHERE c.receiveTime IS NULL AND c.giveTime < :expiryTime AND c.deleted = :deleted")
    List<CouponGive> findExpiredUnreceivedCouponGives(@Param("expiryTime") LocalDateTime expiryTime, @Param("deleted") Byte deleted);
}
