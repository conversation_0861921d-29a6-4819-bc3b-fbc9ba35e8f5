package com.relle.db.repository;

import com.relle.db.entity.AppServiceBundleOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * AppServiceBundleOrder JPA Repository
 * Provides CRUD operations and custom queries for service bundle order information
 */
@Repository
public interface AppServiceBundleOrderRepository extends JpaRepository<AppServiceBundleOrder, Long>, JpaSpecificationExecutor<AppServiceBundleOrder> {

    /**
     * Find bundle order by bundle order ID and not deleted
     */
    Optional<AppServiceBundleOrder> findByBundleOrderIdAndDeleted(String bundleOrderId, Byte deleted);

    /**
     * Find bundle orders by unionid and not deleted
     */
    List<AppServiceBundleOrder> findByUnionidAndDeleted(String unionid, Byte deleted);

    /**
     * Find bundle orders by store ID and not deleted
     */
    List<AppServiceBundleOrder> findByStoreIdAndDeleted(String storeId, Byte deleted);

    /**
     * Find bundle orders by bundle ID and not deleted
     */
    List<AppServiceBundleOrder> findByBundleIdAndDeleted(String bundleId, Byte deleted);

    /**
     * Find bundle orders by order status and not deleted
     */
    List<AppServiceBundleOrder> findByOrderStatusAndDeleted(Short orderStatus, Byte deleted);

    /**
     * Find bundle orders by contact phone and not deleted
     */
    List<AppServiceBundleOrder> findByContactPhoneAndDeleted(String contactPhone, Byte deleted);

    /**
     * Find all bundle orders that are not deleted
     */
    List<AppServiceBundleOrder> findByDeleted(Byte deleted);

    /**
     * Find bundle orders by book time range and not deleted
     */
    @Query("SELECT b FROM AppServiceBundleOrder b WHERE b.bookTime BETWEEN :startTime AND :endTime AND b.deleted = :deleted")
    List<AppServiceBundleOrder> findByBookTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find bundle orders by amount range and not deleted
     */
    @Query("SELECT b FROM AppServiceBundleOrder b WHERE b.bundleOrderAmount BETWEEN :minAmount AND :maxAmount AND b.deleted = :deleted")
    List<AppServiceBundleOrder> findByBundleOrderAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Byte deleted);

    /**
     * Count bundle orders by status and not deleted
     */
    long countByOrderStatusAndDeleted(Short orderStatus, Byte deleted);

    /**
     * Count bundle orders by store and not deleted
     */
    long countByStoreIdAndDeleted(String storeId, Byte deleted);

    /**
     * Check if bundle order exists by bundle order ID and not deleted
     */
    boolean existsByBundleOrderIdAndDeleted(String bundleOrderId, Byte deleted);

    /**
     * Find bundle orders ordered by book time descending and not deleted
     */
    @Query("SELECT b FROM AppServiceBundleOrder b WHERE b.deleted = :deleted ORDER BY b.bookTime DESC")
    List<AppServiceBundleOrder> findByDeletedOrderByBookTimeDesc(@Param("deleted") Byte deleted);

    /**
     * Calculate total bundle order amount by store and date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(b.bundleOrderAmount), 0) FROM AppServiceBundleOrder b WHERE b.storeId = :storeId AND b.createTime BETWEEN :startDate AND :endDate AND b.deleted = :deleted")
    BigDecimal calculateTotalAmountByStoreAndDateRange(@Param("storeId") String storeId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Byte deleted);
}
