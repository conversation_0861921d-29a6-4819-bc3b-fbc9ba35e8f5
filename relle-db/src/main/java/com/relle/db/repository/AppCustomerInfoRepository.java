package com.relle.db.repository;

import com.relle.db.entity.AppCustomerInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AppCustomerInfo JPA Repository
 * Provides CRUD operations and custom queries for customer information
 */
@Repository
public interface AppCustomerInfoRepository extends JpaRepository<AppCustomerInfo, Long>, JpaSpecificationExecutor<AppCustomerInfo> {

    /**
     * Find customer by unionid and not deleted
     */
    Optional<AppCustomerInfo> findByUnionidAndDeleted(String unionid, Byte deleted);

    /**
     * Find customer by mini openid and not deleted
     */
    Optional<AppCustomerInfo> findByMiniOpenidAndDeleted(String miniOpenid, Byte deleted);

    /**
     * Find customer by customer ID and not deleted
     */
    Optional<AppCustomerInfo> findByCustomerIdAndDeleted(String customerId, Byte deleted);

    /**
     * Find customer by wechat phone and not deleted
     */
    Optional<AppCustomerInfo> findByWechatPhoneAndDeleted(String wechatPhone, Byte deleted);

    /**
     * Find all customers that are not deleted
     */
    List<AppCustomerInfo> findByDeleted(Byte deleted);

    /**
     * Find customers by unionid (including deleted ones)
     */
    List<AppCustomerInfo> findByUnionid(String unionid);

    /**
     * Count customers by deleted status
     */
    long countByDeleted(Byte deleted);

    /**
     * Find customers by wechat nickname containing text (case insensitive) and not deleted
     */
    @Query("SELECT c FROM AppCustomerInfo c WHERE LOWER(c.wechatNickname) LIKE LOWER(CONCAT('%', :nickname, '%')) AND c.deleted = :deleted")
    List<AppCustomerInfo> findByWechatNicknameContainingIgnoreCaseAndDeleted(@Param("nickname") String nickname, @Param("deleted") Byte deleted);

    /**
     * Find customers by user name containing text (case insensitive) and not deleted
     */
    @Query("SELECT c FROM AppCustomerInfo c WHERE LOWER(c.userName) LIKE LOWER(CONCAT('%', :userName, '%')) AND c.deleted = :deleted")
    List<AppCustomerInfo> findByUserNameContainingIgnoreCaseAndDeleted(@Param("userName") String userName, @Param("deleted") Byte deleted);

    /**
     * Find customers by gender and not deleted
     */
    List<AppCustomerInfo> findByUserGenderAndDeleted(Byte userGender, Byte deleted);

    /**
     * Check if customer exists by unionid and not deleted
     */
    boolean existsByUnionidAndDeleted(String unionid, Byte deleted);

    /**
     * Check if customer exists by mini openid and not deleted
     */
    boolean existsByMiniOpenidAndDeleted(String miniOpenid, Byte deleted);

    /**
     * Check if customer exists by wechat phone and not deleted
     */
    boolean existsByWechatPhoneAndDeleted(String wechatPhone, Byte deleted);
}
