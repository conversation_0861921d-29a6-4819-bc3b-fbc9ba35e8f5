package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.ConfigInfo;

import java.util.List;
import java.util.Optional;

/**
 * ConfigInfo JPA Repository
 * Provides CRUD operations and custom queries for configuration information
 */
@Repository
public interface ConfigInfoRepository extends JpaRepository<ConfigInfo, Long>, JpaSpecificationExecutor<ConfigInfo> {

    /**
     * Find config by group number and rule name
     */
    Optional<ConfigInfo> findByGroupNoAndRuleName(String groupNo, String ruleName);

    /**
     * Find configs by group number
     */
    List<ConfigInfo> findByGroupNo(String groupNo);

    /**
     * Find configs by rule name
     */
    List<ConfigInfo> findByRuleName(String ruleName);

    /**
     * Find configs by rule name containing text (case insensitive)
     */
    @Query("SELECT c FROM ConfigInfo c WHERE LOWER(c.ruleName) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<ConfigInfo> findByRuleNameContainingIgnoreCase(@Param("name") String name);

    /**
     * Find configs by rule value containing text (case insensitive)
     */
    @Query("SELECT c FROM ConfigInfo c WHERE LOWER(c.ruleValue) LIKE LOWER(CONCAT('%', :value, '%'))")
    List<ConfigInfo> findByRuleValueContainingIgnoreCase(@Param("value") String value);

    /**
     * Find all configs ordered by group number and rule name
     */
    @Query("SELECT c FROM ConfigInfo c ORDER BY c.groupNo ASC, c.ruleName ASC")
    List<ConfigInfo> findAllOrderByGroupNoAscRuleNameAsc();

    /**
     * Find configs by group number ordered by rule name
     */
    @Query("SELECT c FROM ConfigInfo c WHERE c.groupNo = :groupNo ORDER BY c.ruleName ASC")
    List<ConfigInfo> findByGroupNoOrderByRuleNameAsc(@Param("groupNo") String groupNo);

    /**
     * Count configs by group number
     */
    long countByGroupNo(String groupNo);

    /**
     * Check if config exists by group number and rule name
     */
    boolean existsByGroupNoAndRuleName(String groupNo, String ruleName);

    /**
     * Find distinct group numbers
     */
    @Query("SELECT DISTINCT c.groupNo FROM ConfigInfo c ORDER BY c.groupNo ASC")
    List<String> findDistinctGroupNos();

    /**
     * Find distinct rule names
     */
    @Query("SELECT DISTINCT c.ruleName FROM ConfigInfo c ORDER BY c.ruleName ASC")
    List<String> findDistinctRuleNames();

    /**
     * Find configs by multiple group numbers
     */
    @Query("SELECT c FROM ConfigInfo c WHERE c.groupNo IN :groupNos ORDER BY c.groupNo ASC, c.ruleName ASC")
    List<ConfigInfo> findByGroupNoIn(@Param("groupNos") List<String> groupNos);

    /**
     * Find configs by multiple rule names
     */
    @Query("SELECT c FROM ConfigInfo c WHERE c.ruleName IN :ruleNames ORDER BY c.groupNo ASC, c.ruleName ASC")
    List<ConfigInfo> findByRuleNameIn(@Param("ruleNames") List<String> ruleNames);

    /**
     * Delete configs by group number
     */
    void deleteByGroupNo(String groupNo);

    /**
     * Delete config by group number and rule name
     */
    void deleteByGroupNoAndRuleName(String groupNo, String ruleName);

    /**
     * Find configs with empty or null rule values
     */
    @Query("SELECT c FROM ConfigInfo c WHERE c.ruleValue IS NULL OR c.ruleValue = ''")
    List<ConfigInfo> findConfigsWithEmptyValues();

    /**
     * Find configs with non-empty rule values
     */
    @Query("SELECT c FROM ConfigInfo c WHERE c.ruleValue IS NOT NULL AND c.ruleValue != ''")
    List<ConfigInfo> findConfigsWithNonEmptyValues();

    /**
     * Update rule value by group number and rule name
     */
    @Query("UPDATE ConfigInfo c SET c.ruleValue = :ruleValue WHERE c.groupNo = :groupNo AND c.ruleName = :ruleName")
    int updateRuleValueByGroupNoAndRuleName(@Param("groupNo") String groupNo, @Param("ruleName") String ruleName, @Param("ruleValue") String ruleValue);

    /**
     * Find configs by group number pattern
     */
    @Query("SELECT c FROM ConfigInfo c WHERE c.groupNo LIKE CONCAT(:groupPattern, '%') ORDER BY c.groupNo ASC, c.ruleName ASC")
    List<ConfigInfo> findByGroupNoStartingWith(@Param("groupPattern") String groupPattern);

    /**
     * Find configs by rule name pattern
     */
    @Query("SELECT c FROM ConfigInfo c WHERE c.ruleName LIKE CONCAT(:rulePattern, '%') ORDER BY c.groupNo ASC, c.ruleName ASC")
    List<ConfigInfo> findByRuleNameStartingWith(@Param("rulePattern") String rulePattern);
}
