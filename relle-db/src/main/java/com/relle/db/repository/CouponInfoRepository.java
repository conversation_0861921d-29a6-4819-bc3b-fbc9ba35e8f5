package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.CouponInfo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * CouponInfo JPA Repository
 * Provides CRUD operations and custom queries for coupon information
 */
@Repository
public interface CouponInfoRepository extends JpaRepository<CouponInfo, Long>, JpaSpecificationExecutor<CouponInfo> {

    /**
     * Find coupon by coupon ID and not deleted
     */
    Optional<CouponInfo> findByCouponIdAndDeleted(String couponId, Byte deleted);

    /**
     * Find coupons by coupon name containing text (case insensitive) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE LOWER(c.couponName) LIKE LOWER(CONCAT('%', :name, '%')) AND c.deleted = :deleted")
    List<CouponInfo> findByCouponNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Byte deleted);

    /**
     * Find coupons by category and not deleted
     */
    List<CouponInfo> findByCouponCategoryAndDeleted(Byte couponCategory, Byte deleted);

    /**
     * Find coupons by type and not deleted
     */
    List<CouponInfo> findByCouponTypeAndDeleted(String couponType, Byte deleted);

    /**
     * Find coupons by status and not deleted
     */
    List<CouponInfo> findByCouponStatusAndDeleted(Byte couponStatus, Byte deleted);

    /**
     * Find all coupons that are not deleted
     */
    List<CouponInfo> findByDeleted(Byte deleted);

    /**
     * Find coupons by value amount range and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponValueAmount BETWEEN :minAmount AND :maxAmount AND c.deleted = :deleted")
    List<CouponInfo> findByCouponValueAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Byte deleted);

    /**
     * Find coupons by minimum charge range and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponMinCharge BETWEEN :minCharge AND :maxCharge AND c.deleted = :deleted")
    List<CouponInfo> findByCouponMinChargeBetweenAndDeleted(@Param("minCharge") BigDecimal minCharge, @Param("maxCharge") BigDecimal maxCharge, @Param("deleted") Byte deleted);

    /**
     * Find coupons valid at specific time and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponValidDatetimeBegin <= :currentTime AND c.couponValidDatetimeEnd >= :currentTime AND c.deleted = :deleted")
    List<CouponInfo> findValidCouponsAtTime(@Param("currentTime") LocalDateTime currentTime, @Param("deleted") Byte deleted);

    /**
     * Find coupons valid within date range and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponValidDatetimeBegin <= :endTime AND c.couponValidDatetimeEnd >= :startTime AND c.deleted = :deleted")
    List<CouponInfo> findCouponsValidWithinRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find coupons by category and status and not deleted
     */
    List<CouponInfo> findByCouponCategoryAndCouponStatusAndDeleted(Byte couponCategory, Byte couponStatus, Byte deleted);

    /**
     * Find coupons by type and status and not deleted
     */
    List<CouponInfo> findByCouponTypeAndCouponStatusAndDeleted(String couponType, Byte couponStatus, Byte deleted);

    /**
     * Find coupons that can be superposed and not deleted
     */
    List<CouponInfo> findByCanSuperposeAndDeleted(Byte canSuperpose, Byte deleted);

    /**
     * Find coupons by receive limit and not deleted
     */
    List<CouponInfo> findByCouponReceiveLimitAndDeleted(Byte couponReceiveLimit, Byte deleted);

    /**
     * Count coupons by category and not deleted
     */
    long countByCouponCategoryAndDeleted(Byte couponCategory, Byte deleted);

    /**
     * Count coupons by status and not deleted
     */
    long countByCouponStatusAndDeleted(Byte couponStatus, Byte deleted);

    /**
     * Count coupons by type and not deleted
     */
    long countByCouponTypeAndDeleted(String couponType, Byte deleted);

    /**
     * Check if coupon exists by coupon ID and not deleted
     */
    boolean existsByCouponIdAndDeleted(String couponId, Byte deleted);

    /**
     * Find active coupons (status = 1) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponStatus = 1 AND c.deleted = :deleted")
    List<CouponInfo> findActiveCoupons(@Param("deleted") Byte deleted);

    /**
     * Find expired coupons (status = 2) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponStatus = 2 AND c.deleted = :deleted")
    List<CouponInfo> findExpiredCoupons(@Param("deleted") Byte deleted);

    /**
     * Find offline coupons (status = 64) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponStatus = 64 AND c.deleted = :deleted")
    List<CouponInfo> findOfflineCoupons(@Param("deleted") Byte deleted);

    /**
     * Find cash vouchers (category = 1) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponCategory = 1 AND c.deleted = :deleted")
    List<CouponInfo> findCashVouchers(@Param("deleted") Byte deleted);

    /**
     * Find discount vouchers (category = 2) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponCategory = 2 AND c.deleted = :deleted")
    List<CouponInfo> findDiscountVouchers(@Param("deleted") Byte deleted);

    /**
     * Find special vouchers (category = 3) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponCategory = 3 AND c.deleted = :deleted")
    List<CouponInfo> findSpecialVouchers(@Param("deleted") Byte deleted);

    /**
     * Find unlimited issue coupons (issue total = 0) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponIssueTotal = 0 AND c.deleted = :deleted")
    List<CouponInfo> findUnlimitedIssueCoupons(@Param("deleted") Byte deleted);

    /**
     * Find coupons expiring soon (within specified days) and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponValidDatetimeEnd BETWEEN :now AND :expiryThreshold AND c.deleted = :deleted")
    List<CouponInfo> findCouponsExpiringSoon(@Param("now") LocalDateTime now, @Param("expiryThreshold") LocalDateTime expiryThreshold, @Param("deleted") Byte deleted);

    /**
     * Find coupons ordered by value amount descending and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.deleted = :deleted ORDER BY c.couponValueAmount DESC")
    List<CouponInfo> findByDeletedOrderByCouponValueAmountDesc(@Param("deleted") Byte deleted);

    /**
     * Find coupons ordered by expiry date ascending and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.deleted = :deleted ORDER BY c.couponValidDatetimeEnd ASC")
    List<CouponInfo> findByDeletedOrderByCouponValidDatetimeEndAsc(@Param("deleted") Byte deleted);

    /**
     * Find coupons usable for specific order amount and not deleted
     */
    @Query("SELECT c FROM CouponInfo c WHERE c.couponMinCharge <= :orderAmount AND c.couponStatus = 1 AND c.deleted = :deleted ORDER BY c.couponValueAmount DESC")
    List<CouponInfo> findUsableCouponsForOrderAmount(@Param("orderAmount") BigDecimal orderAmount, @Param("deleted") Byte deleted);
}
