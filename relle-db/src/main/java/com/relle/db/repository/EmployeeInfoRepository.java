package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.EmployeeInfo;

import java.util.List;
import java.util.Optional;

/**
 * EmployeeInfo JPA Repository
 * Provides CRUD operations and custom queries for employee information
 */
@Repository
public interface EmployeeInfoRepository extends JpaRepository<EmployeeInfo, Long>, JpaSpecificationExecutor<EmployeeInfo> {

    /**
     * Find employee by employee ID and not deleted
     */
    Optional<EmployeeInfo> findByEmployeeIdAndDeleted(String employeeId, Byte deleted);

    /**
     * Find employee by employee open ID and not deleted
     */
    Optional<EmployeeInfo> findByEmployeeOpenIdAndDeleted(String employeeOpenId, Byte deleted);

    /**
     * Find employee by phone and not deleted
     */
    Optional<EmployeeInfo> findByEmployeePhoneAndDeleted(String employeePhone, Byte deleted);

    /**
     * Find employees by status and not deleted
     */
    List<EmployeeInfo> findByEmployeeStatusAndDeleted(Byte employeeStatus, Byte deleted);

    /**
     * Find employees by department and not deleted
     */
    List<EmployeeInfo> findByDepartmentIdAndDeleted(Byte departmentId, Byte deleted);

    /**
     * Find all employees that are not deleted
     */
    List<EmployeeInfo> findByDeleted(Byte deleted);

    /**
     * Find employees by name containing text (case insensitive) and not deleted
     */
    @Query("SELECT e FROM EmployeeInfo e WHERE LOWER(e.employeeName) LIKE LOWER(CONCAT('%', :name, '%')) AND e.deleted = :deleted")
    List<EmployeeInfo> findByEmployeeNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Byte deleted);

    /**
     * Count employees by status and not deleted
     */
    long countByEmployeeStatusAndDeleted(Byte employeeStatus, Byte deleted);

    /**
     * Count employees by department and not deleted
     */
    long countByDepartmentIdAndDeleted(Byte departmentId, Byte deleted);

    /**
     * Check if employee exists by employee ID and not deleted
     */
    boolean existsByEmployeeIdAndDeleted(String employeeId, Byte deleted);

    /**
     * Check if employee exists by employee open ID and not deleted
     */
    boolean existsByEmployeeOpenIdAndDeleted(String employeeOpenId, Byte deleted);

    /**
     * Check if employee exists by phone and not deleted
     */
    boolean existsByEmployeePhoneAndDeleted(String employeePhone, Byte deleted);

    /**
     * Find active employees (status = 1) and not deleted
     */
    @Query("SELECT e FROM EmployeeInfo e WHERE e.employeeStatus = 1 AND e.deleted = :deleted")
    List<EmployeeInfo> findActiveEmployees(@Param("deleted") Byte deleted);

    /**
     * Find employees by ID card and not deleted
     */
    Optional<EmployeeInfo> findByEmployeeIdcardAndDeleted(String employeeIdcard, Byte deleted);
}
