package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.EmployeeInfo;

import java.util.List;
import java.util.Optional;

/**
 * EmployeeInfo JPA Repository
 * Provides CRUD operations and custom queries for employee information
 */
@Repository
public interface EmployeeInfoRepository extends JpaRepository<EmployeeInfo, Long>, JpaSpecificationExecutor<EmployeeInfo> {

    /**
     * Find employee by employee ID and not deleted
     */
    Optional<EmployeeInfo> findByEmployeeIdAndDeleted(String employeeId, Integer deleted);

    /**
     * Find employee by employee open ID and not deleted
     */
    Optional<EmployeeInfo> findByEmployeeOpenIdAndDeleted(String employeeOpenId, Integer deleted);

    /**
     * Find employee by phone and not deleted
     */
    Optional<EmployeeInfo> findByEmployeePhoneAndDeleted(String employeePhone, Integer deleted);

    /**
     * Find employees by status and not deleted
     */
    List<EmployeeInfo> findByEmployeeStatusAndDeleted(Integer employeeStatus, Integer deleted);

    /**
     * Find employees by department and not deleted
     */
    List<EmployeeInfo> findByDepartmentIdAndDeleted(Integer departmentId, Integer deleted);

    /**
     * Find all employees that are not deleted
     */
    List<EmployeeInfo> findByDeleted(Integer deleted);

    /**
     * Find employees by name containing text (case insensitive) and not deleted
     */
    @Query("SELECT e FROM EmployeeInfo e WHERE LOWER(e.employeeName) LIKE LOWER(CONCAT('%', :name, '%')) AND e.deleted = :deleted")
    List<EmployeeInfo> findByEmployeeNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Integer deleted);

    /**
     * Count employees by status and not deleted
     */
    long countByEmployeeStatusAndDeleted(Integer employeeStatus, Integer deleted);

    /**
     * Count employees by department and not deleted
     */
    long countByDepartmentIdAndDeleted(Integer departmentId, Integer deleted);

    /**
     * Check if employee exists by employee ID and not deleted
     */
    boolean existsByEmployeeIdAndDeleted(String employeeId, Integer deleted);

    /**
     * Check if employee exists by employee open ID and not deleted
     */
    boolean existsByEmployeeOpenIdAndDeleted(String employeeOpenId, Integer deleted);

    /**
     * Check if employee exists by phone and not deleted
     */
    boolean existsByEmployeePhoneAndDeleted(String employeePhone, Integer deleted);

    /**
     * Find active employees (status = 1) and not deleted
     */
    @Query("SELECT e FROM EmployeeInfo e WHERE e.employeeStatus = 1 AND e.deleted = :deleted")
    List<EmployeeInfo> findActiveEmployees(@Param("deleted") Integer deleted);

    /**
     * Find employees by ID card and not deleted
     */
    Optional<EmployeeInfo> findByEmployeeIdcardAndDeleted(String employeeIdcard, Integer deleted);
}
