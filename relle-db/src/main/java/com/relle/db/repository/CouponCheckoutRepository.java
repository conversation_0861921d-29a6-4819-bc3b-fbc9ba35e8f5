package com.relle.db.repository;

import com.relle.db.entity.CouponCheckout;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * CouponCheckout JPA Repository
 * Provides CRUD operations and custom queries for coupon checkout information
 */
@Repository
public interface CouponCheckoutRepository extends JpaRepository<CouponCheckout, Long>, JpaSpecificationExecutor<CouponCheckout> {

    /**
     * Find coupon checkout by coupon receive ID and not deleted
     */
    Optional<CouponCheckout> findByCouponReceiveIdAndDeleted(String couponReceiveId, Integer deleted);

    /**
     * Find coupon checkouts by coupon ID and not deleted
     */
    List<CouponCheckout> findByCouponIdAndDeleted(String couponId, Integer deleted);

    /**
     * Find coupon checkouts by receive customer unionid and not deleted
     */
    List<CouponCheckout> findByReceiveCustomerUnionidAndDeleted(String receiveCustomerUnionid, Integer deleted);

    /**
     * Find coupon checkouts by coupon status and not deleted
     */
    List<CouponCheckout> findByCouponStatusAndDeleted(Integer couponStatus, Integer deleted);

    /**
     * Find coupon checkouts by coupon source and not deleted
     */
    List<CouponCheckout> findByCouponSourceAndDeleted(Integer couponSource, Integer deleted);

    /**
     * Find all coupon checkouts that are not deleted
     */
    List<CouponCheckout> findByDeleted(Integer deleted);

    /**
     * Find coupon checkouts by receive time range and not deleted
     */
    @Query("SELECT c FROM CouponCheckout c WHERE c.receiveTime BETWEEN :startTime AND :endTime AND c.deleted = :deleted")
    List<CouponCheckout> findByReceiveTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find coupon checkouts by use coupon time range and not deleted
     */
    @Query("SELECT c FROM CouponCheckout c WHERE c.useCouponTime BETWEEN :startTime AND :endTime AND c.deleted = :deleted")
    List<CouponCheckout> findByUseCouponTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find unused coupons (status = 1) and not deleted
     */
    @Query("SELECT c FROM CouponCheckout c WHERE c.couponStatus = 1 AND c.deleted = :deleted")
    List<CouponCheckout> findUnusedCoupons(@Param("deleted") Integer deleted);

    /**
     * Find used coupons (status = 2) and not deleted
     */
    @Query("SELECT c FROM CouponCheckout c WHERE c.couponStatus = 2 AND c.deleted = :deleted")
    List<CouponCheckout> findUsedCoupons(@Param("deleted") Integer deleted);

    /**
     * Find expired coupons (status = 4) and not deleted
     */
    @Query("SELECT c FROM CouponCheckout c WHERE c.couponStatus = 4 AND c.deleted = :deleted")
    List<CouponCheckout> findExpiredCoupons(@Param("deleted") Integer deleted);

    /**
     * Find coupons expiring soon and not deleted
     */
    @Query("SELECT c FROM CouponCheckout c WHERE c.couponValidEndtime BETWEEN :now AND :expiryThreshold AND c.couponStatus = 1 AND c.deleted = :deleted")
    List<CouponCheckout> findCouponsExpiringSoon(@Param("now") LocalDateTime now, @Param("expiryThreshold") LocalDateTime expiryThreshold, @Param("deleted") Integer deleted);

    /**
     * Count coupon checkouts by customer and not deleted
     */
    long countByReceiveCustomerUnionidAndDeleted(String receiveCustomerUnionid, Integer deleted);

    /**
     * Count coupon checkouts by status and not deleted
     */
    long countByCouponStatusAndDeleted(Integer couponStatus, Integer deleted);

    /**
     * Check if coupon checkout exists by coupon receive ID and not deleted
     */
    boolean existsByCouponReceiveIdAndDeleted(String couponReceiveId, Integer deleted);

    /**
     * Find coupon checkouts by customer and status and not deleted
     */
    List<CouponCheckout> findByReceiveCustomerUnionidAndCouponStatusAndDeleted(String receiveCustomerUnionid, Integer couponStatus, Integer deleted);

    /**
     * Find coupon checkouts by use coupon order ID and not deleted
     */
    List<CouponCheckout> findByUseCouponOrderidAndDeleted(String useCouponOrderid, Integer deleted);

    /**
     * Find coupon checkouts ordered by receive time descending and not deleted
     */
    @Query("SELECT c FROM CouponCheckout c WHERE c.deleted = :deleted ORDER BY c.receiveTime DESC")
    List<CouponCheckout> findByDeletedOrderByReceiveTimeDesc(@Param("deleted") Integer deleted);
}
