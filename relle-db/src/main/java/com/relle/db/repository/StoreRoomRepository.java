package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.StoreRoom;

import java.util.List;
import java.util.Optional;

/**
 * AppStoreRoom JPA Repository
 * Provides CRUD operations and custom queries for store room information
 */
@Repository
public interface StoreRoomRepository extends JpaRepository<StoreRoom, Long>, JpaSpecificationExecutor<StoreRoom> {

    /**
     * Find room by room ID and not deleted
     */
    Optional<StoreRoom> findByRoomIdAndDeleted(String roomId, Integer deleted);

    /**
     * Find rooms by store ID and not deleted
     */
    List<StoreRoom> findByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Find rooms by room type and not deleted
     */
    List<StoreRoom> findByRoomTypeAndDeleted(Integer roomType, Integer deleted);

    /**
     * Find rooms by store ID and room type and not deleted
     */
    List<StoreRoom> findByStoreIdAndRoomTypeAndDeleted(String storeId, Integer roomType, Integer deleted);

    /**
     * Find all rooms that are not deleted
     */
    List<StoreRoom> findByDeleted(Integer deleted);

    /**
     * Find rooms by room name containing text (case insensitive) and not deleted
     */
    @Query("SELECT r FROM StoreRoom r WHERE LOWER(r.roomName) LIKE LOWER(CONCAT('%', :name, '%')) AND r.deleted = :deleted")
    List<StoreRoom> findByRoomNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Integer deleted);

    /**
     * Find rooms by store ID and room name containing text (case insensitive) and not deleted
     */
    @Query("SELECT r FROM StoreRoom r WHERE r.storeId = :storeId AND LOWER(r.roomName) LIKE LOWER(CONCAT('%', :name, '%')) AND r.deleted = :deleted")
    List<StoreRoom> findByStoreIdAndRoomNameContainingIgnoreCaseAndDeleted(@Param("storeId") String storeId, @Param("name") String name, @Param("deleted") Integer deleted);

    /**
     * Count rooms by store ID and not deleted
     */
    long countByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Count rooms by room type and not deleted
     */
    long countByRoomTypeAndDeleted(Integer roomType, Integer deleted);

    /**
     * Count rooms by store ID and room type and not deleted
     */
    long countByStoreIdAndRoomTypeAndDeleted(String storeId, Integer roomType, Integer deleted);

    /**
     * Check if room exists by room ID and not deleted
     */
    boolean existsByRoomIdAndDeleted(String roomId, Integer deleted);

    /**
     * Check if room exists by store ID and room name and not deleted
     */
    boolean existsByStoreIdAndRoomNameAndDeleted(String storeId, String roomName, Integer deleted);

    /**
     * Find rooms by store ID ordered by room name ascending and not deleted
     */
    @Query("SELECT r FROM StoreRoom r WHERE r.storeId = :storeId AND r.deleted = :deleted ORDER BY r.roomName ASC")
    List<StoreRoom> findByStoreIdAndDeletedOrderByRoomNameAsc(@Param("storeId") String storeId, @Param("deleted") Integer deleted);

    /**
     * Find rooms by room type ordered by store ID and room name and not deleted
     */
    @Query("SELECT r FROM StoreRoom r WHERE r.roomType = :roomType AND r.deleted = :deleted ORDER BY r.storeId ASC, r.roomName ASC")
    List<StoreRoom> findByRoomTypeAndDeletedOrderByStoreIdAscRoomNameAsc(@Param("roomType") Integer roomType, @Param("deleted") Integer deleted);

    /**
     * Find all rooms ordered by store ID and room name and not deleted
     */
    @Query("SELECT r FROM StoreRoom r WHERE r.deleted = :deleted ORDER BY r.storeId ASC, r.roomName ASC")
    List<StoreRoom> findByDeletedOrderByStoreIdAscRoomNameAsc(@Param("deleted") Integer deleted);

    /**
     * Find available room types by store ID and not deleted
     */
    @Query("SELECT DISTINCT r.roomType FROM StoreRoom r WHERE r.storeId = :storeId AND r.deleted = :deleted ORDER BY r.roomType ASC")
    List<Integer> findDistinctRoomTypesByStoreIdAndDeleted(@Param("storeId") String storeId, @Param("deleted") Integer deleted);

    /**
     * Find stores that have rooms of specific type and not deleted
     */
    @Query("SELECT DISTINCT r.storeId FROM StoreRoom r WHERE r.roomType = :roomType AND r.deleted = :deleted ORDER BY r.storeId ASC")
    List<String> findDistinctStoreIdsByRoomTypeAndDeleted(@Param("roomType") Integer roomType, @Param("deleted") Integer deleted);

    /**
     * Find rooms by multiple room types and not deleted
     */
    @Query("SELECT r FROM StoreRoom r WHERE r.roomType IN :roomTypes AND r.deleted = :deleted")
    List<StoreRoom> findByRoomTypeInAndDeleted(@Param("roomTypes") List<Integer> roomTypes, @Param("deleted") Integer deleted);

    /**
     * Find rooms by store ID and multiple room types and not deleted
     */
    @Query("SELECT r FROM StoreRoom r WHERE r.storeId = :storeId AND r.roomType IN :roomTypes AND r.deleted = :deleted")
    List<StoreRoom> findByStoreIdAndRoomTypeInAndDeleted(@Param("storeId") String storeId, @Param("roomTypes") List<Integer> roomTypes, @Param("deleted") Integer deleted);
}
