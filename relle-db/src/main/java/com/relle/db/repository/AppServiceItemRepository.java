package com.relle.db.repository;

import com.relle.db.entity.AppServiceItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * AppServiceItem JPA Repository
 * Provides CRUD operations and custom queries for service item information
 */
@Repository
public interface AppServiceItemRepository extends JpaRepository<AppServiceItem, Long>, JpaSpecificationExecutor<AppServiceItem> {

    /**
     * Find service item by service ID and not deleted
     */
    Optional<AppServiceItem> findByServiceIdAndDeleted(String serviceId, Byte deleted);

    /**
     * Find service items by service name containing text (case insensitive) and not deleted
     */
    @Query("SELECT s FROM AppServiceItem s WHERE LOWER(s.serviceName) LIKE LOWER(CONCAT('%', :name, '%')) AND s.deleted = :deleted")
    List<AppServiceItem> findByServiceNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Byte deleted);

    /**
     * Find service items by service status and not deleted
     */
    List<AppServiceItem> findByServiceStatusAndDeleted(Byte serviceStatus, Byte deleted);

    /**
     * Find service items by service type and not deleted
     */
    List<AppServiceItem> findByServiceTypeAndDeleted(String serviceType, Byte deleted);

    /**
     * Find service items by channel type and not deleted
     */
    List<AppServiceItem> findByChannelTypeAndDeleted(Byte channelType, Byte deleted);

    /**
     * Find all service items that are not deleted
     */
    List<AppServiceItem> findByDeleted(Byte deleted);

    /**
     * Find service items by price range and not deleted
     */
    @Query("SELECT s FROM AppServiceItem s WHERE s.servicePrice BETWEEN :minPrice AND :maxPrice AND s.deleted = :deleted")
    List<AppServiceItem> findByServicePriceBetweenAndDeleted(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, @Param("deleted") Byte deleted);

    /**
     * Find service items by tag tree containing text and not deleted
     */
    @Query("SELECT s FROM AppServiceItem s WHERE s.serviceTagtree LIKE CONCAT('%', :tag, '%') AND s.deleted = :deleted")
    List<AppServiceItem> findByServiceTagtreeContainingAndDeleted(@Param("tag") String tag, @Param("deleted") Byte deleted);

    /**
     * Find service items that can be added and not deleted
     */
    List<AppServiceItem> findByCanAddItemAndDeleted(Byte canAddItem, Byte deleted);

    /**
     * Find service items by restricted role and not deleted
     */
    List<AppServiceItem> findByRestrictedRoleAndDeleted(Byte restrictedRole, Byte deleted);

    /**
     * Find service items ordered by show sort and not deleted
     */
    @Query("SELECT s FROM AppServiceItem s WHERE s.deleted = :deleted ORDER BY s.serviceShowSort ASC")
    List<AppServiceItem> findByDeletedOrderByServiceShowSortAsc(@Param("deleted") Byte deleted);

    /**
     * Find active service items (status = 1) and not deleted
     */
    @Query("SELECT s FROM AppServiceItem s WHERE s.serviceStatus = 1 AND s.deleted = :deleted")
    List<AppServiceItem> findActiveServiceItems(@Param("deleted") Byte deleted);

    /**
     * Count service items by status and not deleted
     */
    long countByServiceStatusAndDeleted(Byte serviceStatus, Byte deleted);

    /**
     * Count service items by type and not deleted
     */
    long countByServiceTypeAndDeleted(String serviceType, Byte deleted);

    /**
     * Check if service item exists by service ID and not deleted
     */
    boolean existsByServiceIdAndDeleted(String serviceId, Byte deleted);

    /**
     * Find service items by duration range and not deleted
     */
    @Query("SELECT s FROM AppServiceItem s WHERE s.serviceDurationSec BETWEEN :minDuration AND :maxDuration AND s.deleted = :deleted")
    List<AppServiceItem> findByServiceDurationSecBetweenAndDeleted(@Param("minDuration") Integer minDuration, @Param("maxDuration") Integer maxDuration, @Param("deleted") Byte deleted);
}
