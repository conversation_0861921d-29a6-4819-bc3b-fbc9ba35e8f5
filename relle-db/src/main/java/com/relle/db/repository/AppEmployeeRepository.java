package com.relle.db.repository;

import com.relle.db.entity.AppEmployee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AppEmployee JPA Repository
 * Provides CRUD operations and custom queries for employee information
 */
@Repository
public interface AppEmployeeRepository extends JpaRepository<AppEmployee, Long>, JpaSpecificationExecutor<AppEmployee> {

    /**
     * Find employee by employee ID and not deleted
     */
    Optional<AppEmployee> findByEmployeeIdAndDeleted(String employeeId, Byte deleted);

    /**
     * Find employee by employee open ID and not deleted
     */
    Optional<AppEmployee> findByEmployeeOpenIdAndDeleted(String employeeOpenId, Byte deleted);

    /**
     * Find employee by phone and not deleted
     */
    Optional<AppEmployee> findByEmployeePhoneAndDeleted(String employeePhone, Byte deleted);

    /**
     * Find employees by status and not deleted
     */
    List<AppEmployee> findByEmployeeStatusAndDeleted(Byte employeeStatus, Byte deleted);

    /**
     * Find employees by department and not deleted
     */
    List<AppEmployee> findByDepartmentIdAndDeleted(Byte departmentId, Byte deleted);

    /**
     * Find all employees that are not deleted
     */
    List<AppEmployee> findByDeleted(Byte deleted);

    /**
     * Find employees by name containing text (case insensitive) and not deleted
     */
    @Query("SELECT e FROM AppEmployee e WHERE LOWER(e.employeeName) LIKE LOWER(CONCAT('%', :name, '%')) AND e.deleted = :deleted")
    List<AppEmployee> findByEmployeeNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Byte deleted);

    /**
     * Count employees by status and not deleted
     */
    long countByEmployeeStatusAndDeleted(Byte employeeStatus, Byte deleted);

    /**
     * Count employees by department and not deleted
     */
    long countByDepartmentIdAndDeleted(Byte departmentId, Byte deleted);

    /**
     * Check if employee exists by employee ID and not deleted
     */
    boolean existsByEmployeeIdAndDeleted(String employeeId, Byte deleted);

    /**
     * Check if employee exists by employee open ID and not deleted
     */
    boolean existsByEmployeeOpenIdAndDeleted(String employeeOpenId, Byte deleted);

    /**
     * Check if employee exists by phone and not deleted
     */
    boolean existsByEmployeePhoneAndDeleted(String employeePhone, Byte deleted);

    /**
     * Find active employees (status = 1) and not deleted
     */
    @Query("SELECT e FROM AppEmployee e WHERE e.employeeStatus = 1 AND e.deleted = :deleted")
    List<AppEmployee> findActiveEmployees(@Param("deleted") Byte deleted);

    /**
     * Find employees by ID card and not deleted
     */
    Optional<AppEmployee> findByEmployeeIdcardAndDeleted(String employeeIdcard, Byte deleted);
}
