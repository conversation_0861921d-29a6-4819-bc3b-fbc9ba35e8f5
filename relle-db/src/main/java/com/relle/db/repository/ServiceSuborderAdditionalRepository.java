package com.relle.db.repository;

import com.relle.db.entity.ServiceSuborderAdditional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * AppServiceSuborderAdditional JPA Repository
 * Provides CRUD operations and custom queries for additional service suborder information
 */
@Repository
public interface ServiceSuborderAdditionalRepository extends JpaRepository<ServiceSuborderAdditional, Long>, JpaSpecificationExecutor<ServiceSuborderAdditional> {

    /**
     * Find additional suborder by suborder ID and not deleted
     */
    Optional<ServiceSuborderAdditional> findBySuborderIdAndDeleted(String suborderId, Integer deleted);

    /**
     * Find additional suborders by order ID and not deleted
     */
    List<ServiceSuborderAdditional> findByOrderIdAndDeleted(String orderId, Integer deleted);

    /**
     * Find additional suborders by unionid and not deleted
     */
    List<ServiceSuborderAdditional> findByUnionidAndDeleted(String unionid, Integer deleted);

    /**
     * Find additional suborders by store ID and not deleted
     */
    List<ServiceSuborderAdditional> findByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Find additional suborders by room ID and not deleted
     */
    List<ServiceSuborderAdditional> findByRoomIdAndDeleted(String roomId, Integer deleted);

    /**
     * Find additional suborders by service item ID and not deleted
     */
    List<ServiceSuborderAdditional> findByServiceItemIdAndDeleted(String serviceItemId, Integer deleted);

    /**
     * Find additional suborders by status and not deleted
     */
    List<ServiceSuborderAdditional> findBySuborderStatusAndDeleted(Short suborderStatus, Integer deleted);

    /**
     * Find all additional suborders that are not deleted
     */
    List<ServiceSuborderAdditional> findByDeleted(Integer deleted);

    /**
     * Find additional suborders by book time range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborderAdditional s WHERE s.bookTime BETWEEN :startTime AND :endTime AND s.deleted = :deleted")
    List<ServiceSuborderAdditional> findByBookTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find additional suborders by amount range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborderAdditional s WHERE s.suborderAmount BETWEEN :minAmount AND :maxAmount AND s.deleted = :deleted")
    List<ServiceSuborderAdditional> findBySuborderAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Integer deleted);

    /**
     * Count additional suborders by status and not deleted
     */
    long countBySuborderStatusAndDeleted(Short suborderStatus, Integer deleted);

    /**
     * Count additional suborders by store and not deleted
     */
    long countByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Check if additional suborder exists by suborder ID and not deleted
     */
    boolean existsBySuborderIdAndDeleted(String suborderId, Integer deleted);

    /**
     * Find pending payment additional suborders (status = 1) and not deleted
     */
    @Query("SELECT s FROM ServiceSuborderAdditional s WHERE s.suborderStatus = 1 AND s.deleted = :deleted")
    List<ServiceSuborderAdditional> findPendingPaymentSuborders(@Param("deleted") Integer deleted);

    /**
     * Find completed additional suborders (status = 8) and not deleted
     */
    @Query("SELECT s FROM ServiceSuborderAdditional s WHERE s.suborderStatus = 8 AND s.deleted = :deleted")
    List<ServiceSuborderAdditional> findCompletedSuborders(@Param("deleted") Integer deleted);

    /**
     * Calculate total amount by store and date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(s.suborderAmount), 0) FROM ServiceSuborderAdditional s WHERE s.storeId = :storeId AND s.createTime BETWEEN :startDate AND :endDate AND s.deleted = :deleted")
    BigDecimal calculateTotalAmountByStoreAndDateRange(@Param("storeId") String storeId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Integer deleted);

    /**
     * Find additional suborders ordered by book time ascending and not deleted
     */
    @Query("SELECT s FROM ServiceSuborderAdditional s WHERE s.deleted = :deleted ORDER BY s.bookTime ASC")
    List<ServiceSuborderAdditional> findByDeletedOrderByBookTimeAsc(@Param("deleted") Integer deleted);
}
