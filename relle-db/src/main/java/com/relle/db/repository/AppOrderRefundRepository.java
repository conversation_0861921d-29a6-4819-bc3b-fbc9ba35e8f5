package com.relle.db.repository;

import com.relle.db.entity.AppOrderRefund;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * AppOrderRefund JPA Repository
 * Provides CRUD operations and custom queries for order refund information
 */
@Repository
public interface AppOrderRefundRepository extends JpaRepository<AppOrderRefund, Long>, JpaSpecificationExecutor<AppOrderRefund> {

    /**
     * Find refunds by order ID and not deleted
     */
    List<AppOrderRefund> findByOrderIdAndDeleted(String orderId, Byte deleted);

    /**
     * Find refund by refund order ID and not deleted
     */
    Optional<AppOrderRefund> findByRefundOrderIdAndDeleted(String refundOrderId, Byte deleted);

    /**
     * Find refunds by refund status and not deleted
     */
    List<AppOrderRefund> findByRefundStatusAndDeleted(Byte refundStatus, Byte deleted);

    /**
     * Find all refunds that are not deleted
     */
    List<AppOrderRefund> findByDeleted(Byte deleted);

    /**
     * Find refunds by refund time range and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.refundTime BETWEEN :startTime AND :endTime AND r.deleted = :deleted")
    List<AppOrderRefund> findByRefundTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find refunds by refund amount range and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.refundAmount BETWEEN :minAmount AND :maxAmount AND r.deleted = :deleted")
    List<AppOrderRefund> findByRefundAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Byte deleted);

    /**
     * Find refunds by order amount range and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.orderAmount BETWEEN :minAmount AND :maxAmount AND r.deleted = :deleted")
    List<AppOrderRefund> findByOrderAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Byte deleted);

    /**
     * Find pending refunds (status = 1) and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.refundStatus = 1 AND r.deleted = :deleted")
    List<AppOrderRefund> findPendingRefunds(@Param("deleted") Byte deleted);

    /**
     * Find successful refunds (status = 2) and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.refundStatus = 2 AND r.deleted = :deleted")
    List<AppOrderRefund> findSuccessfulRefunds(@Param("deleted") Byte deleted);

    /**
     * Find failed refunds (status = 4) and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.refundStatus = 4 AND r.deleted = :deleted")
    List<AppOrderRefund> findFailedRefunds(@Param("deleted") Byte deleted);

    /**
     * Count refunds by status and not deleted
     */
    long countByRefundStatusAndDeleted(Byte refundStatus, Byte deleted);

    /**
     * Count refunds by order ID and not deleted
     */
    long countByOrderIdAndDeleted(String orderId, Byte deleted);

    /**
     * Check if refund exists by refund order ID and not deleted
     */
    boolean existsByRefundOrderIdAndDeleted(String refundOrderId, Byte deleted);

    /**
     * Check if refund exists by order ID and not deleted
     */
    boolean existsByOrderIdAndDeleted(String orderId, Byte deleted);

    /**
     * Find refunds by refund reason containing text (case insensitive) and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE LOWER(r.refundReason) LIKE LOWER(CONCAT('%', :reason, '%')) AND r.deleted = :deleted")
    List<AppOrderRefund> findByRefundReasonContainingIgnoreCaseAndDeleted(@Param("reason") String reason, @Param("deleted") Byte deleted);

    /**
     * Find refunds ordered by refund time descending and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.deleted = :deleted ORDER BY r.refundTime DESC")
    List<AppOrderRefund> findByDeletedOrderByRefundTimeDesc(@Param("deleted") Byte deleted);

    /**
     * Calculate total refund amount by date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(r.refundAmount), 0) FROM AppOrderRefund r WHERE r.refundTime BETWEEN :startTime AND :endTime AND r.refundStatus = 2 AND r.deleted = :deleted")
    BigDecimal calculateTotalRefundAmount(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Calculate total successful refund amount by date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(r.refundAmount), 0) FROM AppOrderRefund r WHERE r.refundTime BETWEEN :startTime AND :endTime AND r.refundStatus = 2 AND r.deleted = :deleted")
    BigDecimal calculateTotalSuccessfulRefundAmount(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find refunds by order ID and status and not deleted
     */
    List<AppOrderRefund> findByOrderIdAndRefundStatusAndDeleted(String orderId, Byte refundStatus, Byte deleted);

    /**
     * Find refunds by status and time range and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.refundStatus = :status AND r.refundTime BETWEEN :startTime AND :endTime AND r.deleted = :deleted ORDER BY r.refundTime DESC")
    List<AppOrderRefund> findByStatusAndTimeRange(@Param("status") Byte status, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find latest refund by order ID and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.orderId = :orderId AND r.deleted = :deleted ORDER BY r.refundTime DESC")
    List<AppOrderRefund> findLatestRefundByOrderId(@Param("orderId") String orderId, @Param("deleted") Byte deleted);

    /**
     * Find refunds with amount greater than specified value and not deleted
     */
    @Query("SELECT r FROM AppOrderRefund r WHERE r.refundAmount > :amount AND r.deleted = :deleted")
    List<AppOrderRefund> findRefundsWithAmountGreaterThan(@Param("amount") BigDecimal amount, @Param("deleted") Byte deleted);
}
