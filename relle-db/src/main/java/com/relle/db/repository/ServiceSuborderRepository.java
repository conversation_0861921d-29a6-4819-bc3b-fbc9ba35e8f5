package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.ServiceSuborder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * AppServiceSuborder JPA Repository
 * Provides CRUD operations and custom queries for service suborder information
 */
@Repository
public interface ServiceSuborderRepository extends JpaRepository<ServiceSuborder, Long>, JpaSpecificationExecutor<ServiceSuborder> {

    /**
     * Find suborder by suborder ID and not deleted
     */
    Optional<ServiceSuborder> findBySuborderIdAndDeleted(String suborderId, Integer deleted);

    /**
     * Find suborders by order ID and not deleted
     */
    List<ServiceSuborder> findByOrderIdAndDeleted(String orderId, Integer deleted);

    /**
     * Find suborders by unionid and not deleted
     */
    List<ServiceSuborder> findByUnionidAndDeleted(String unionid, Integer deleted);

    /**
     * Find suborders by store ID and not deleted
     */
    List<ServiceSuborder> findByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Find suborders by room ID and not deleted
     */
    List<ServiceSuborder> findByRoomIdAndDeleted(String roomId, Integer deleted);

    /**
     * Find suborders by service item ID and not deleted
     */
    List<ServiceSuborder> findByServiceItemIdAndDeleted(String serviceItemId, Integer deleted);

    /**
     * Find suborders by status and not deleted
     */
    List<ServiceSuborder> findBySuborderStatusAndDeleted(Short suborderStatus, Integer deleted);

    /**
     * Find all suborders that are not deleted
     */
    List<ServiceSuborder> findByDeleted(Integer deleted);

    /**
     * Find suborders by unionid and status and not deleted
     */
    List<ServiceSuborder> findByUnionidAndSuborderStatusAndDeleted(String unionid, Short suborderStatus, Integer deleted);

    /**
     * Find suborders by store and status and not deleted
     */
    List<ServiceSuborder> findByStoreIdAndSuborderStatusAndDeleted(String storeId, Short suborderStatus, Integer deleted);

    /**
     * Find suborders by book time range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.bookTime BETWEEN :startTime AND :endTime AND s.deleted = :deleted")
    List<ServiceSuborder> findByBookTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find suborders by amount range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.suborderAmount BETWEEN :minAmount AND :maxAmount AND s.deleted = :deleted")
    List<ServiceSuborder> findBySuborderAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Integer deleted);

    /**
     * Find suborders by store and book time range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.storeId = :storeId AND s.bookTime BETWEEN :startTime AND :endTime AND s.deleted = :deleted ORDER BY s.bookTime ASC")
    List<ServiceSuborder> findByStoreIdAndBookTimeBetweenAndDeleted(@Param("storeId") String storeId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find suborders by room and book time range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.roomId = :roomId AND s.bookTime BETWEEN :startTime AND :endTime AND s.deleted = :deleted ORDER BY s.bookTime ASC")
    List<ServiceSuborder> findByRoomIdAndBookTimeBetweenAndDeleted(@Param("roomId") String roomId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find suborders by unionid and book time range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.unionid = :unionid AND s.bookTime BETWEEN :startTime AND :endTime AND s.deleted = :deleted ORDER BY s.bookTime DESC")
    List<ServiceSuborder> findByUnionidAndBookTimeBetweenAndDeleted(@Param("unionid") String unionid, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Count suborders by status and not deleted
     */
    long countBySuborderStatusAndDeleted(Short suborderStatus, Integer deleted);

    /**
     * Count suborders by store and not deleted
     */
    long countByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Count suborders by service item and not deleted
     */
    long countByServiceItemIdAndDeleted(String serviceItemId, Integer deleted);

    /**
     * Count suborders by unionid and not deleted
     */
    long countByUnionidAndDeleted(String unionid, Integer deleted);

    /**
     * Check if suborder exists by suborder ID and not deleted
     */
    boolean existsBySuborderIdAndDeleted(String suborderId, Integer deleted);

    /**
     * Find pending payment suborders (status = 1) and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.suborderStatus = 1 AND s.deleted = :deleted")
    List<ServiceSuborder> findPendingPaymentSuborders(@Param("deleted") Integer deleted);

    /**
     * Find completed suborders (status = 8) and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.suborderStatus = 8 AND s.deleted = :deleted")
    List<ServiceSuborder> findCompletedSuborders(@Param("deleted") Integer deleted);

    /**
     * Find in-service suborders (status = 4) and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.suborderStatus = 4 AND s.deleted = :deleted")
    List<ServiceSuborder> findInServiceSuborders(@Param("deleted") Integer deleted);

    /**
     * Calculate total amount by store and date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(s.suborderAmount), 0) FROM ServiceSuborder s WHERE s.storeId = :storeId AND s.createTime BETWEEN :startDate AND :endDate AND s.deleted = :deleted")
    BigDecimal calculateTotalAmountByStoreAndDateRange(@Param("storeId") String storeId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Integer deleted);

    /**
     * Find suborders ordered by book time ascending and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.deleted = :deleted ORDER BY s.bookTime ASC")
    List<ServiceSuborder> findByDeletedOrderByBookTimeAsc(@Param("deleted") Integer deleted);

    /**
     * Find today's suborders by store and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.storeId = :storeId AND DATE(s.bookTime) = CURRENT_DATE AND s.deleted = :deleted ORDER BY s.bookTime ASC")
    List<ServiceSuborder> findTodaySubordersByStore(@Param("storeId") String storeId, @Param("deleted") Integer deleted);

    /**
     * Find suborders by service item and date range and not deleted
     */
    @Query("SELECT s FROM ServiceSuborder s WHERE s.serviceItemId = :serviceItemId AND s.bookTime BETWEEN :startTime AND :endTime AND s.deleted = :deleted")
    List<ServiceSuborder> findByServiceItemIdAndBookTimeBetweenAndDeleted(@Param("serviceItemId") String serviceItemId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);
}
