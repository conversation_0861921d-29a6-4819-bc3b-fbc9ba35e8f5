package com.relle.db.repository;

import com.relle.db.entity.OrderPay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * OrderPay JPA Repository
 * Provides CRUD operations and custom queries for order payment information
 */
@Repository
public interface OrderPayRepository extends JpaRepository<OrderPay, Long>, JpaSpecificationExecutor<OrderPay> {

    /**
     * Find payment by order ID and not deleted
     */
    Optional<OrderPay> findByOrderIdAndDeleted(String orderId, Byte deleted);

    /**
     * Find payments by pay system order ID and not deleted
     */
    Optional<OrderPay> findByPaySystemOrderidAndDeleted(String paySystemOrderid, Byte deleted);

    /**
     * Find payments by pay channel ID and not deleted
     */
    List<OrderPay> findByPayChanelIdAndDeleted(String payChanelId, Byte deleted);

    /**
     * Find payments by pay status and not deleted
     */
    List<OrderPay> findByPayStatusAndDeleted(Byte payStatus, Byte deleted);

    /**
     * Find payments by order source and not deleted
     */
    List<OrderPay> findByOrderSourceAndDeleted(String orderSource, Byte deleted);

    /**
     * Find all payments that are not deleted
     */
    List<OrderPay> findByDeleted(Byte deleted);

    /**
     * Find payments by pay time range and not deleted
     */
    @Query("SELECT p FROM OrderPay p WHERE p.payTime BETWEEN :startTime AND :endTime AND p.deleted = :deleted")
    List<OrderPay> findByPayTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find payments by pay amount range and not deleted
     */
    @Query("SELECT p FROM OrderPay p WHERE p.payAmount BETWEEN :minAmount AND :maxAmount AND p.deleted = :deleted")
    List<OrderPay> findByPayAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Byte deleted);

    /**
     * Find successful payments (status = 1) and not deleted
     */
    @Query("SELECT p FROM OrderPay p WHERE p.payStatus = 1 AND p.deleted = :deleted")
    List<OrderPay> findSuccessfulPayments(@Param("deleted") Byte deleted);

    /**
     * Find pending payments (status = 0) and not deleted
     */
    @Query("SELECT p FROM OrderPay p WHERE p.payStatus = 0 AND p.deleted = :deleted")
    List<OrderPay> findPendingPayments(@Param("deleted") Byte deleted);

    /**
     * Count payments by pay status and not deleted
     */
    long countByPayStatusAndDeleted(Byte payStatus, Byte deleted);

    /**
     * Count payments by pay channel and not deleted
     */
    long countByPayChanelIdAndDeleted(String payChanelId, Byte deleted);

    /**
     * Count payments by order source and not deleted
     */
    long countByOrderSourceAndDeleted(String orderSource, Byte deleted);

    /**
     * Check if payment exists by order ID and not deleted
     */
    boolean existsByOrderIdAndDeleted(String orderId, Byte deleted);

    /**
     * Check if payment exists by pay system order ID and not deleted
     */
    boolean existsByPaySystemOrderidAndDeleted(String paySystemOrderid, Byte deleted);

    /**
     * Find payments by channel and status and not deleted
     */
    List<OrderPay> findByPayChanelIdAndPayStatusAndDeleted(String payChanelId, Byte payStatus, Byte deleted);

    /**
     * Find payments by source and status and not deleted
     */
    List<OrderPay> findByOrderSourceAndPayStatusAndDeleted(String orderSource, Byte payStatus, Byte deleted);

    /**
     * Find payments ordered by pay time descending and not deleted
     */
    @Query("SELECT p FROM OrderPay p WHERE p.deleted = :deleted ORDER BY p.payTime DESC")
    List<OrderPay> findByDeletedOrderByPayTimeDesc(@Param("deleted") Byte deleted);

    /**
     * Calculate total payment amount by date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(p.payAmount), 0) FROM OrderPay p WHERE p.payTime BETWEEN :startTime AND :endTime AND p.payStatus = 1 AND p.deleted = :deleted")
    BigDecimal calculateTotalPaymentAmount(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Calculate total payment amount by channel and date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(p.payAmount), 0) FROM OrderPay p WHERE p.payChanelId = :channelId AND p.payTime BETWEEN :startTime AND :endTime AND p.payStatus = 1 AND p.deleted = :deleted")
    BigDecimal calculateTotalPaymentAmountByChannel(@Param("channelId") String channelId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find payments with pay end time before specified time and not deleted
     */
    @Query("SELECT p FROM OrderPay p WHERE p.payEndTime < :endTime AND p.deleted = :deleted")
    List<OrderPay> findPaymentsEndingBefore(@Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find payments by pay channel and time range and not deleted
     */
    @Query("SELECT p FROM OrderPay p WHERE p.payChanelId = :channelId AND p.payTime BETWEEN :startTime AND :endTime AND p.deleted = :deleted ORDER BY p.payTime DESC")
    List<OrderPay> findByChannelAndTimeRange(@Param("channelId") String channelId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);
}
