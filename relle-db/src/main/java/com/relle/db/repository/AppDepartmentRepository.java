package com.relle.db.repository;

import com.relle.db.entity.AppDepartment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AppDepartment JPA Repository
 * Provides CRUD operations and custom queries for department information
 */
@Repository
public interface AppDepartmentRepository extends JpaRepository<AppDepartment, Long>, JpaSpecificationExecutor<AppDepartment> {

    /**
     * Find department by name
     */
    Optional<AppDepartment> findByName(String name);

    /**
     * Find departments by name containing text (case insensitive)
     */
    @Query("SELECT d FROM AppDepartment d WHERE LOWER(d.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<AppDepartment> findByNameContainingIgnoreCase(@Param("name") String name);

    /**
     * Find all departments ordered by name
     */
    @Query("SELECT d FROM AppDepartment d ORDER BY d.name ASC")
    List<AppDepartment> findAllOrderByNameAsc();

    /**
     * Check if department exists by name
     */
    boolean existsByName(String name);
}
