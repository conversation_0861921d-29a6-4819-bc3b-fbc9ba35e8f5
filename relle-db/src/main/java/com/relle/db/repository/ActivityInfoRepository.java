package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.ActivityInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * ActivityInfo JPA Repository
 * Provides CRUD operations and custom queries for activity information
 */
@Repository
public interface ActivityInfoRepository extends JpaRepository<ActivityInfo, Long>, JpaSpecificationExecutor<ActivityInfo> {

    /**
     * Find activity by activity ID and not deleted
     */
    Optional<ActivityInfo> findByActivityIdAndDeleted(String activityId, Byte deleted);

    /**
     * Find activities by activity name containing text (case insensitive) and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE LOWER(a.activityName) LIKE LOWER(CONCAT('%', :name, '%')) AND a.deleted = :deleted")
    List<ActivityInfo> findByActivityNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Byte deleted);

    /**
     * Find activities by category and not deleted
     */
    List<ActivityInfo> findByActivityCategoryAndDeleted(Integer activityCategory, Byte deleted);

    /**
     * Find activities by status and not deleted
     */
    List<ActivityInfo> findByStatusAndDeleted(Byte status, Byte deleted);

    /**
     * Find all activities that are not deleted
     */
    List<ActivityInfo> findByDeleted(Byte deleted);

    /**
     * Find activities by store IDs containing specific store and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.storeIds LIKE CONCAT('%', :storeId, '%') AND a.deleted = :deleted")
    List<ActivityInfo> findByStoreIdsContainingAndDeleted(@Param("storeId") String storeId, @Param("deleted") Byte deleted);

    /**
     * Find activities active at specific time and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.startTime <= :currentTime AND a.endTime >= :currentTime AND a.deleted = :deleted")
    List<ActivityInfo> findActiveActivitiesAtTime(@Param("currentTime") LocalDateTime currentTime, @Param("deleted") Byte deleted);

    /**
     * Find activities within time range and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.startTime <= :endTime AND a.endTime >= :startTime AND a.deleted = :deleted")
    List<ActivityInfo> findActivitiesWithinTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Byte deleted);

    /**
     * Find activities by category and status and not deleted
     */
    List<ActivityInfo> findByActivityCategoryAndStatusAndDeleted(Integer activityCategory, Byte status, Byte deleted);

    /**
     * Find activities starting between dates and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.startTime BETWEEN :startDate AND :endDate AND a.deleted = :deleted")
    List<ActivityInfo> findByStartTimeBetweenAndDeleted(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Byte deleted);

    /**
     * Find activities ending between dates and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.endTime BETWEEN :startDate AND :endDate AND a.deleted = :deleted")
    List<ActivityInfo> findByEndTimeBetweenAndDeleted(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("deleted") Byte deleted);

    /**
     * Count activities by category and not deleted
     */
    long countByActivityCategoryAndDeleted(Integer activityCategory, Byte deleted);

    /**
     * Count activities by status and not deleted
     */
    long countByStatusAndDeleted(Byte status, Byte deleted);

    /**
     * Check if activity exists by activity ID and not deleted
     */
    boolean existsByActivityIdAndDeleted(String activityId, Byte deleted);

    /**
     * Find active activities (status = 1) and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.status = 1 AND a.deleted = :deleted")
    List<ActivityInfo> findActiveActivities(@Param("deleted") Byte deleted);

    /**
     * Find inactive activities (status = 0) and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.status = 0 AND a.deleted = :deleted")
    List<ActivityInfo> findInactiveActivities(@Param("deleted") Byte deleted);

    /**
     * Find popup activities (category = 1) and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.activityCategory = 1 AND a.deleted = :deleted")
    List<ActivityInfo> findPopupActivities(@Param("deleted") Byte deleted);

    /**
     * Find banner activities (category = 2) and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.activityCategory = 2 AND a.deleted = :deleted")
    List<ActivityInfo> findBannerActivities(@Param("deleted") Byte deleted);

    /**
     * Find activities with coupons and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.couponNum > 0 AND a.deleted = :deleted")
    List<ActivityInfo> findActivitiesWithCoupons(@Param("deleted") Byte deleted);

    /**
     * Find activities ordered by sort code ascending and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.deleted = :deleted ORDER BY a.sortCode ASC")
    List<ActivityInfo> findByDeletedOrderBySortCodeAsc(@Param("deleted") Byte deleted);

    /**
     * Find activities ordered by start time descending and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.deleted = :deleted ORDER BY a.startTime DESC")
    List<ActivityInfo> findByDeletedOrderByStartTimeDesc(@Param("deleted") Byte deleted);

    /**
     * Find activities expiring soon (within specified hours) and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.endTime BETWEEN :now AND :expiryThreshold AND a.status = 1 AND a.deleted = :deleted")
    List<ActivityInfo> findActivitiesExpiringSoon(@Param("now") LocalDateTime now, @Param("expiryThreshold") LocalDateTime expiryThreshold, @Param("deleted") Byte deleted);

    /**
     * Find upcoming activities (starting within specified hours) and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.startTime BETWEEN :now AND :startThreshold AND a.status = 1 AND a.deleted = :deleted")
    List<ActivityInfo> findUpcomingActivities(@Param("now") LocalDateTime now, @Param("startThreshold") LocalDateTime startThreshold, @Param("deleted") Byte deleted);

    /**
     * Find activities by store and category and status and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.storeIds LIKE CONCAT('%', :storeId, '%') AND a.activityCategory = :category AND a.status = :status AND a.deleted = :deleted ORDER BY a.sortCode ASC")
    List<ActivityInfo> findByStoreAndCategoryAndStatusAndDeleted(@Param("storeId") String storeId, @Param("category") Integer category, @Param("status") Byte status, @Param("deleted") Byte deleted);

    /**
     * Find current active activities for specific store and not deleted
     */
    @Query("SELECT a FROM ActivityInfo a WHERE a.storeIds LIKE CONCAT('%', :storeId, '%') AND a.startTime <= :currentTime AND a.endTime >= :currentTime AND a.status = 1 AND a.deleted = :deleted ORDER BY a.sortCode ASC")
    List<ActivityInfo> findCurrentActiveActivitiesForStore(@Param("storeId") String storeId, @Param("currentTime") LocalDateTime currentTime, @Param("deleted") Byte deleted);
}
