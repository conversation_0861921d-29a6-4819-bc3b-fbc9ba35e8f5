package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.DepartmentInfo;

import java.util.List;
import java.util.Optional;

/*
 * DepartmentInfo JPA Repository
 * Provides CRUD operations and custom queries for department information
 */
@Repository
public interface DepartmentInfoRepository extends JpaRepository<DepartmentInfo, Long>, JpaSpecificationExecutor<DepartmentInfo> {

    /**
     * Find department by name
     */
    Optional<DepartmentInfo> findByName(String name);

    /**
     * Find departments by name containing text (case insensitive)
     */
    @Query("SELECT d FROM DepartmentInfo d WHERE LOWER(d.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<DepartmentInfo> findByNameContainingIgnoreCase(@Param("name") String name);

    /**
     * Find all departments ordered by name
     */
    @Query("SELECT d FROM DepartmentInfo d ORDER BY d.name ASC")
    List<DepartmentInfo> findAllOrderByNameAsc();

    /**
     * Check if department exists by name
     */
    boolean existsByName(String name);
}
