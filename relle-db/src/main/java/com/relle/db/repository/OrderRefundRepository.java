package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.OrderRefund;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * AppOrderRefund JPA Repository
 * Provides CRUD operations and custom queries for order refund information
 */
@Repository
public interface OrderRefundRepository extends JpaRepository<OrderRefund, Long>, JpaSpecificationExecutor<OrderRefund> {

    /**
     * Find refunds by order ID and not deleted
     */
    List<OrderRefund> findByOrderIdAndDeleted(String orderId, Integer deleted);

    /**
     * Find refund by refund order ID and not deleted
     */
    Optional<OrderRefund> findByRefundOrderIdAndDeleted(String refundOrderId, Integer deleted);

    /**
     * Find refunds by refund status and not deleted
     */
    List<OrderRefund> findByRefundStatusAndDeleted(Integer refundStatus, Integer deleted);

    /**
     * Find all refunds that are not deleted
     */
    List<OrderRefund> findByDeleted(Integer deleted);

    /**
     * Find refunds by refund time range and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.refundTime BETWEEN :startTime AND :endTime AND r.deleted = :deleted")
    List<OrderRefund> findByRefundTimeBetweenAndDeleted(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find refunds by refund amount range and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.refundAmount BETWEEN :minAmount AND :maxAmount AND r.deleted = :deleted")
    List<OrderRefund> findByRefundAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Integer deleted);

    /**
     * Find refunds by order amount range and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.orderAmount BETWEEN :minAmount AND :maxAmount AND r.deleted = :deleted")
    List<OrderRefund> findByOrderAmountBetweenAndDeleted(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount, @Param("deleted") Integer deleted);

    /**
     * Find pending refunds (status = 1) and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.refundStatus = 1 AND r.deleted = :deleted")
    List<OrderRefund> findPendingRefunds(@Param("deleted") Integer deleted);

    /**
     * Find successful refunds (status = 2) and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.refundStatus = 2 AND r.deleted = :deleted")
    List<OrderRefund> findSuccessfulRefunds(@Param("deleted") Integer deleted);

    /**
     * Find failed refunds (status = 4) and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.refundStatus = 4 AND r.deleted = :deleted")
    List<OrderRefund> findFailedRefunds(@Param("deleted") Integer deleted);

    /**
     * Count refunds by status and not deleted
     */
    long countByRefundStatusAndDeleted(Integer refundStatus, Integer deleted);

    /**
     * Count refunds by order ID and not deleted
     */
    long countByOrderIdAndDeleted(String orderId, Integer deleted);

    /**
     * Check if refund exists by refund order ID and not deleted
     */
    boolean existsByRefundOrderIdAndDeleted(String refundOrderId, Integer deleted);

    /**
     * Check if refund exists by order ID and not deleted
     */
    boolean existsByOrderIdAndDeleted(String orderId, Integer deleted);

    /**
     * Find refunds by refund reason containing text (case insensitive) and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE LOWER(r.refundReason) LIKE LOWER(CONCAT('%', :reason, '%')) AND r.deleted = :deleted")
    List<OrderRefund> findByRefundReasonContainingIgnoreCaseAndDeleted(@Param("reason") String reason, @Param("deleted") Integer deleted);

    /**
     * Find refunds ordered by refund time descending and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.deleted = :deleted ORDER BY r.refundTime DESC")
    List<OrderRefund> findByDeletedOrderByRefundTimeDesc(@Param("deleted") Integer deleted);

    /**
     * Calculate total refund amount by date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(r.refundAmount), 0) FROM OrderRefund r WHERE r.refundTime BETWEEN :startTime AND :endTime AND r.refundStatus = 2 AND r.deleted = :deleted")
    BigDecimal calculateTotalRefundAmount(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Calculate total successful refund amount by date range and not deleted
     */
    @Query("SELECT COALESCE(SUM(r.refundAmount), 0) FROM OrderRefund r WHERE r.refundTime BETWEEN :startTime AND :endTime AND r.refundStatus = 2 AND r.deleted = :deleted")
    BigDecimal calculateTotalSuccessfulRefundAmount(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find refunds by order ID and status and not deleted
     */
    List<OrderRefund> findByOrderIdAndRefundStatusAndDeleted(String orderId, Integer refundStatus, Integer deleted);

    /**
     * Find refunds by status and time range and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.refundStatus = :status AND r.refundTime BETWEEN :startTime AND :endTime AND r.deleted = :deleted ORDER BY r.refundTime DESC")
    List<OrderRefund> findByStatusAndTimeRange(@Param("status") Integer status, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("deleted") Integer deleted);

    /**
     * Find latest refund by order ID and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.orderId = :orderId AND r.deleted = :deleted ORDER BY r.refundTime DESC")
    List<OrderRefund> findLatestRefundByOrderId(@Param("orderId") String orderId, @Param("deleted") Integer deleted);

    /**
     * Find refunds with amount greater than specified value and not deleted
     */
    @Query("SELECT r FROM OrderRefund r WHERE r.refundAmount > :amount AND r.deleted = :deleted")
    List<OrderRefund> findRefundsWithAmountGreaterThan(@Param("amount") BigDecimal amount, @Param("deleted") Integer deleted);
}
