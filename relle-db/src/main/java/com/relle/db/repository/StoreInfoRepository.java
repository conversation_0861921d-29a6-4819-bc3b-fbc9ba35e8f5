package com.relle.db.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.relle.db.entity.StoreInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * AppStoreInfo JPA Repository
 * Provides CRUD operations and custom queries for store information
 */
@Repository
public interface StoreInfoRepository extends JpaRepository<StoreInfo, Long>, JpaSpecificationExecutor<StoreInfo> {

    /**
     * Find store by store ID and not deleted
     */
    Optional<StoreInfo> findByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Find stores by store name containing text (case insensitive) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE LOWER(s.storeName) LIKE LOWER(CONCAT('%', :name, '%')) AND s.deleted = :deleted")
    List<StoreInfo> findByStoreNameContainingIgnoreCaseAndDeleted(@Param("name") String name, @Param("deleted") Integer deleted);

    /**
     * Find stores by store status and not deleted
     */
    List<StoreInfo> findByStoreStatusAndDeleted(Integer storeStatus, Integer deleted);

    /**
     * Find stores by store type and not deleted
     */
    List<StoreInfo> findByStoreTypeAndDeleted(Integer storeType, Integer deleted);

    /**
     * Find stores by phone and not deleted
     */
    Optional<StoreInfo> findByStorePhoneAndDeleted(String storePhone, Integer deleted);

    /**
     * Find all stores that are not deleted
     */
    List<StoreInfo> findByDeleted(Integer deleted);

    /**
     * Find stores by address containing text (case insensitive) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE LOWER(s.storeAddress) LIKE LOWER(CONCAT('%', :address, '%')) AND s.deleted = :deleted")
    List<StoreInfo> findByStoreAddressContainingIgnoreCaseAndDeleted(@Param("address") String address, @Param("deleted") Integer deleted);

    /**
     * Find stores by opening date range and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.storeOpeningDate BETWEEN :startDate AND :endDate AND s.deleted = :deleted")
    List<StoreInfo> findByStoreOpeningDateBetweenAndDeleted(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("deleted") Integer deleted);

    /**
     * Find stores within geographic bounds and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.storeLongitude BETWEEN :minLng AND :maxLng AND s.storeLatitude BETWEEN :minLat AND :maxLat AND s.deleted = :deleted")
    List<StoreInfo> findStoresWithinBounds(@Param("minLng") BigDecimal minLongitude, @Param("maxLng") BigDecimal maxLongitude, 
                                             @Param("minLat") BigDecimal minLatitude, @Param("maxLat") BigDecimal maxLatitude, 
                                             @Param("deleted") Integer deleted);

    /**
     * Find stores by type and status and not deleted
     */
    List<StoreInfo> findByStoreTypeAndStoreStatusAndDeleted(Integer storeType, Integer storeStatus, Integer deleted);

    /**
     * Count stores by status and not deleted
     */
    long countByStoreStatusAndDeleted(Integer storeStatus, Integer deleted);

    /**
     * Count stores by type and not deleted
     */
    long countByStoreTypeAndDeleted(Integer storeType, Integer deleted);

    /**
     * Check if store exists by store ID and not deleted
     */
    boolean existsByStoreIdAndDeleted(String storeId, Integer deleted);

    /**
     * Check if store exists by phone and not deleted
     */
    boolean existsByStorePhoneAndDeleted(String storePhone, Integer deleted);

    /**
     * Find active stores (status = 1) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.storeStatus = 1 AND s.deleted = :deleted")
    List<StoreInfo> findActiveStores(@Param("deleted") Integer deleted);

    /**
     * Find stores opening soon (status = 2) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.storeStatus = 2 AND s.deleted = :deleted")
    List<StoreInfo> findStoresOpeningSoon(@Param("deleted") Integer deleted);

    /**
     * Find closed stores (status = 32) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.storeStatus = 32 AND s.deleted = :deleted")
    List<StoreInfo> findClosedStores(@Param("deleted") Integer deleted);

    /**
     * Find beauty care stores (type = 0) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.storeType = 0 AND s.deleted = :deleted")
    List<StoreInfo> findBeautyCareStores(@Param("deleted") Integer deleted);

    /**
     * Find clinic stores (type = 1) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.storeType = 1 AND s.deleted = :deleted")
    List<StoreInfo> findClinicStores(@Param("deleted") Integer deleted);

    /**
     * Find stores ordered by opening date descending and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.deleted = :deleted ORDER BY s.storeOpeningDate DESC")
    List<StoreInfo> findByDeletedOrderByStoreOpeningDateDesc(@Param("deleted") Integer deleted);

    /**
     * Find stores ordered by store name ascending and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.deleted = :deleted ORDER BY s.storeName ASC")
    List<StoreInfo> findByDeletedOrderByStoreNameAsc(@Param("deleted") Integer deleted);

    /**
     * Find nearest stores by coordinates (simplified distance calculation) and not deleted
     */
    @Query("SELECT s FROM StoreInfo s WHERE s.deleted = :deleted ORDER BY " +
           "SQRT(POWER(s.storeLongitude - :longitude, 2) + POWER(s.storeLatitude - :latitude, 2)) ASC")
    List<StoreInfo> findNearestStores(@Param("longitude") BigDecimal longitude, @Param("latitude") BigDecimal latitude, @Param("deleted") Integer deleted);
}
