package com.relle.db.repository;

import com.relle.db.entity.ServiceItemCoupon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ServiceItemCoupon JPA Repository
 * Provides CRUD operations and custom queries for service item coupon relationships
 */
@Repository
public interface ServiceItemCouponRepository extends JpaRepository<ServiceItemCoupon, Long>, JpaSpecificationExecutor<ServiceItemCoupon> {

    /**
     * Find service item coupons by service item ID and not deleted
     */
    List<ServiceItemCoupon> findByServiceItemIdAndDeleted(String serviceItemId, Integer deleted);

    /**
     * Find service item coupons by coupon ID and not deleted
     */
    List<ServiceItemCoupon> findByCouponIdAndDeleted(String couponId, Integer deleted);

    /**
     * Find all service item coupons that are not deleted
     */
    List<ServiceItemCoupon> findByDeleted(Integer deleted);

    /**
     * Find service item coupons by coupon number and not deleted
     */
    List<ServiceItemCoupon> findByCouponNumAndDeleted(Integer couponNum, Integer deleted);

    /**
     * Count service item coupons by service item ID and not deleted
     */
    long countByServiceItemIdAndDeleted(String serviceItemId, Integer deleted);

    /**
     * Count service item coupons by coupon ID and not deleted
     */
    long countByCouponIdAndDeleted(String couponId, Integer deleted);

    /**
     * Check if service item coupon exists by service item ID and coupon ID and not deleted
     */
    boolean existsByServiceItemIdAndCouponIdAndDeleted(String serviceItemId, String couponId, Integer deleted);

    /**
     * Find service item coupons by service item ID and coupon ID and not deleted
     */
    List<ServiceItemCoupon> findByServiceItemIdAndCouponIdAndDeleted(String serviceItemId, String couponId, Integer deleted);

    /**
     * Find service item coupons ordered by coupon number descending and not deleted
     */
    @Query("SELECT s FROM ServiceItemCoupon s WHERE s.deleted = :deleted ORDER BY s.couponNum DESC")
    List<ServiceItemCoupon> findByDeletedOrderByCouponNumDesc(@Param("deleted") Integer deleted);

    /**
     * Calculate total coupon number by service item ID and not deleted
     */
    @Query("SELECT COALESCE(SUM(s.couponNum), 0) FROM ServiceItemCoupon s WHERE s.serviceItemId = :serviceItemId AND s.deleted = :deleted")
    Long calculateTotalCouponNumByServiceItemId(@Param("serviceItemId") String serviceItemId, @Param("deleted") Integer deleted);
}
