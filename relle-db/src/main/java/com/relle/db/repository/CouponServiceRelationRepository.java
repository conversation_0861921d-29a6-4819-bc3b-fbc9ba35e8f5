package com.relle.db.repository;

import com.relle.db.entity.CouponServiceRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CouponServiceRelation JPA Repository
 * Provides CRUD operations and custom queries for coupon service relationships
 */
@Repository
public interface CouponServiceRelationRepository extends JpaRepository<CouponServiceRelation, Long>, JpaSpecificationExecutor<CouponServiceRelation> {

    /**
     * Find coupon service relations by coupon ID and not deleted
     */
    List<CouponServiceRelation> findByCouponIdAndDeleted(String couponId, Integer deleted);

    /**
     * Find coupon service relations by service item ID and not deleted
     */
    List<CouponServiceRelation> findByServiceItemIdAndDeleted(String serviceItemId, Integer deleted);

    /**
     * Find all coupon service relations that are not deleted
     */
    List<CouponServiceRelation> findByDeleted(Integer deleted);

    /**
     * Count coupon service relations by coupon ID and not deleted
     */
    long countByCouponIdAndDeleted(String couponId, Integer deleted);

    /**
     * Count coupon service relations by service item ID and not deleted
     */
    long countByServiceItemIdAndDeleted(String serviceItemId, Integer deleted);

    /**
     * Check if coupon service relation exists by coupon ID and service item ID and not deleted
     */
    boolean existsByCouponIdAndServiceItemIdAndDeleted(String couponId, String serviceItemId, Integer deleted);

    /**
     * Find coupon service relations by coupon ID and service item ID and not deleted
     */
    List<CouponServiceRelation> findByCouponIdAndServiceItemIdAndDeleted(String couponId, String serviceItemId, Integer deleted);

    /**
     * Find distinct coupon IDs by service item ID and not deleted
     */
    @Query("SELECT DISTINCT c.couponId FROM CouponServiceRelation c WHERE c.serviceItemId = :serviceItemId AND c.deleted = :deleted")
    List<String> findDistinctCouponIdsByServiceItemId(@Param("serviceItemId") String serviceItemId, @Param("deleted") Integer deleted);

    /**
     * Find distinct service item IDs by coupon ID and not deleted
     */
    @Query("SELECT DISTINCT c.serviceItemId FROM CouponServiceRelation c WHERE c.couponId = :couponId AND c.deleted = :deleted")
    List<String> findDistinctServiceItemIdsByCouponId(@Param("couponId") String couponId, @Param("deleted") Integer deleted);
}
