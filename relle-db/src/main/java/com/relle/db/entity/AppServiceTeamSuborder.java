package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Service Team Suborder Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_team_suborder
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_team_suborder")
public class AppServiceTeamSuborder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 团购订单编号
     */
    @Column(name = "team_order_id")
    private String teamOrderId;

    /**
     * 团购子订单编号
     */
    @Column(name = "team_suborder_id", unique = true)
    private String teamSuborderId;

    /**
     * 参团用户unionid
     */
    @Column(name = "participant_unionid")
    private String participantUnionid;

    /**
     * 门店编号
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 房间ID
     */
    @Column(name = "room_id")
    private String roomId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 预约服务开始时间
     */
    @Column(name = "book_time")
    private LocalDateTime bookTime;

    /**
     * 子订单金额
     */
    @Column(name = "suborder_amount", precision = 10, scale = 2)
    private BigDecimal suborderAmount;

    /**
     * 参团时间
     */
    @Column(name = "join_time")
    private LocalDateTime joinTime;

    /**
     * 子订单状态
     */
    @Column(name = "suborder_status")
    private Short suborderStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
