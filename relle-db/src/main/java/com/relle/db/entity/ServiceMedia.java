package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Service Media Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_media
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_media")
public class ServiceMedia extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 媒体ID
     */
    @Column(name = "media_id", unique = true)
    private String mediaId;

    /**
     * 媒体名称
     */
    @Column(name = "media_name")
    private String mediaName;

    /**
     * 媒体类型
     */
    @Column(name = "media_type")
    private String mediaType;

    /**
     * 媒体URL
     */
    @Column(name = "media_url")
    private String mediaUrl;

    /**
     * 媒体大小(字节)
     */
    @Column(name = "media_size")
    private Long mediaSize;

    /**
     * 媒体格式
     */
    @Column(name = "media_format")
    private String mediaFormat;

    /**
     * 媒体描述
     */
    @Column(name = "media_description", columnDefinition = "TEXT")
    private String mediaDescription;

    /**
     * 媒体状态
     */
    @Column(name = "media_status")
    private Integer mediaStatus;

    /**
     * 排序值
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 缩略图URL
     */
    @Column(name = "thumbnail_url")
    private String thumbnailUrl;

    /**
     * 媒体标签
     */
    @Column(name = "media_tags")
    private String mediaTags;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
