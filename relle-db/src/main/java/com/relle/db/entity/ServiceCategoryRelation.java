package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Service Category Relation Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_category_relation
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_category_relation")
public class ServiceCategoryRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 服务分类ID
     */
    @Column(name = "service_category_id")
    private String serviceCategoryId;

    /**
     * 关系类型
     */
    @Column(name = "relation_type")
    private String relationType;

    /**
     * 排序值
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
