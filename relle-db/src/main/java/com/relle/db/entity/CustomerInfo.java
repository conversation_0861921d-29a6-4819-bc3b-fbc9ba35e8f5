package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "customer_info")
public class CustomerInfo extends BaseEntity {

    /**
     * 微信唯一识别码
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 小程序openid
     */
    @Column(name = "mini_openid")
    private String miniOpenid;

    @Column(name = "customer_id")
    private String customerId;

    /**
     * 微信获取用户昵称
     */
    @Column(name = "wechat_nickname")
    private String wechatNickname;

    /**
     * 微信用户手机号
     */
    @Column(name = "wechat_phone")
    private String wechatPhone;

    /**
     * 微信用户头像链接
     */
    @Column(name = "wechat_avatar_src")
    private String wechatAvatarSrc;

    /**
     * 用户姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户性别： 1， 男， 2 女， 0 未知
     */
    @Column(name = "user_gender")
    private Byte userGender;

    /**
     * 用户生日
     */
    @Column(name = "user_birthdate")
    private String userBirthdate;

    /**
     * 用户本地头像，可供用户替换头像；2作为备用
     */
    @Column(name = "user_avatar_src")
    private String userAvatarSrc;

    private static final long serialVersionUID = 1L;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
