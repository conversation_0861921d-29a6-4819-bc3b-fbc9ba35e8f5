package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "service_operation_record")
public class ServiceOperationRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 子订单编号
     */
    @Column(name = "suborder_id")
    private String suborderId;

    /**
     * 用户unionid
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店编号
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 房间ID
     */
    @Column(name = "room_id")
    private String roomId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    private String operationType;

    /**
     * 操作内容
     */
    @Column(name = "operation_content", columnDefinition = "TEXT")
    private String operationContent;

    /**
     * 操作时间
     */
    @Column(name = "operation_time")
    private LocalDateTime operationTime;

    /**
     * 操作状态
     */
    @Column(name = "operation_status")
    private Byte operationStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
