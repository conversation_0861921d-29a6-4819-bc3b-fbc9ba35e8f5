package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Service Suborder Additional Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_suborder_additional
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_suborder_additional")
public class AppServiceSuborderAdditional extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 需加单服务的订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 这次加单的订单编号
     */
    @Column(name = "suborder_id", unique = true)
    private String suborderId;

    /**
     * 用户小程序唯一id
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店编号，如SH0001
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 房间ID
     */
    @Column(name = "room_id")
    private String roomId;

    /**
     * 产品编号，对应产品表自增id（app_service_item表）
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 预约服务开始时间
     */
    @Column(name = "book_time")
    private LocalDateTime bookTime;

    /**
     * 子订单原价
     */
    @Column(name = "suborder_origin_price", precision = 10, scale = 2)
    private BigDecimal suborderOriginPrice;

    /**
     * 子订单减免金额
     */
    @Column(name = "suborder_reduction_amount", precision = 10, scale = 2)
    private BigDecimal suborderReductionAmount;

    /**
     * 子订单金额
     */
    @Column(name = "suborder_amount", precision = 10, scale = 2)
    private BigDecimal suborderAmount;

    /**
     * 子订单状态-无符号int，状态可以为负数，表明异常状态；
     * 1：待支付，2: 待服务，4: 服务中，8: 已完成。 
     * -1：售后中； -2: 已退款； -4: 已取消；
     */
    @Column(name = "suborder_status")
    private Short suborderStatus;

    /**
     * 支付链接
     */
    @Column(name = "pay_url")
    private String payUrl;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
