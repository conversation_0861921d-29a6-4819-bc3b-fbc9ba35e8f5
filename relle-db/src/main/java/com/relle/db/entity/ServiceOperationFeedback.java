package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "service_operation_feedback")
public class ServiceOperationFeedback extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 子订单编号
     */
    @Column(name = "suborder_id")
    private String suborderId;

    /**
     * 用户unionid
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店编号
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 反馈内容
     */
    @Column(name = "feedback_content", columnDefinition = "TEXT")
    private String feedbackContent;

    /**
     * 评分
     */
    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating;

    /**
     * 反馈类型
     */
    @Column(name = "feedback_type")
    private String feedbackType;

    /**
     * 反馈状态
     */
    @Column(name = "feedback_status")
    private Byte feedbackStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
