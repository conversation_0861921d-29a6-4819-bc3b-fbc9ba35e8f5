package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "service_bundle_order")
public class ServiceBundleOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 套餐订单编号
     */
    @Column(name = "bundle_order_id", unique = true)
    private String bundleOrderId;

    /**
     * 用户unionid
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店编号
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 套餐ID
     */
    @Column(name = "bundle_id")
    private String bundleId;

    /**
     * 套餐名称
     */
    @Column(name = "bundle_name")
    private String bundleName;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 套餐原价
     */
    @Column(name = "bundle_origin_price", precision = 10, scale = 2)
    private BigDecimal bundleOriginPrice;

    /**
     * 套餐减免金额
     */
    @Column(name = "bundle_reduction_amount", precision = 10, scale = 2)
    private BigDecimal bundleReductionAmount;

    /**
     * 套餐订单金额
     */
    @Column(name = "bundle_order_amount", precision = 10, scale = 2)
    private BigDecimal bundleOrderAmount;

    /**
     * 预约时间
     */
    @Column(name = "book_time")
    private LocalDateTime bookTime;

    /**
     * 订单状态
     */
    @Column(name = "order_status")
    private Short orderStatus;

    /**
     * 支付链接
     */
    @Column(name = "pay_url")
    private String payUrl;

    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
