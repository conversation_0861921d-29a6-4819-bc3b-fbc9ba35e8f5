package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Order Recommend Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is order_followup
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "order_followup")
public class OrderRecommend extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 用户unionid
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 推荐服务项目ID
     */
    @Column(name = "recommended_service_id")
    private String recommendedServiceId;

    /**
     * 推荐原因
     */
    @Column(name = "recommend_reason", columnDefinition = "TEXT")
    private String recommendReason;

    /**
     * 推荐时间
     */
    @Column(name = "recommend_time")
    private LocalDateTime recommendTime;

    /**
     * 推荐状态
     */
    @Column(name = "recommend_status")
    private Integer recommendStatus;

    /**
     * 推荐优先级
     */
    @Column(name = "recommend_priority")
    private Integer recommendPriority;

    /**
     * 推荐价格
     */
    @Column(name = "recommend_price", precision = 10, scale = 2)
    private BigDecimal recommendPrice;

    /**
     * 推荐有效期
     */
    @Column(name = "recommend_valid_until")
    private LocalDateTime recommendValidUntil;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
