package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * System Task Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is sys_task
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sys_task")
public class SysTask extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 任务名称
     */
    @Column(name = "task_name")
    private String taskName;

    /**
     * 任务类型
     */
    @Column(name = "task_type")
    private String taskType;

    /**
     * 任务描述
     */
    @Column(name = "task_description", columnDefinition = "TEXT")
    private String taskDescription;

    /**
     * 任务状态
     */
    @Column(name = "task_status")
    private Integer taskStatus;

    /**
     * 任务优先级
     */
    @Column(name = "task_priority")
    private Integer taskPriority;

    /**
     * 计划执行时间
     */
    @Column(name = "scheduled_time")
    private LocalDateTime scheduledTime;

    /**
     * 实际开始时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 实际结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 执行结果
     */
    @Column(name = "execution_result", columnDefinition = "TEXT")
    private String executionResult;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @Column(name = "max_retry_count")
    private Integer maxRetryCount;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
