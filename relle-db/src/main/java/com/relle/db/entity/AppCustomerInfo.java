package com.relle.db.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Customer Information Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_customer_info
 * @create 2023-12-07 17:21:51
 */
@Entity
@Table(name = "app_customer_info")
@EntityListeners(AuditingEntityListener.class)
public class AppCustomerInfo implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 微信唯一识别码
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 小程序openid
     */
    @Column(name = "mini_openid")
    private String miniOpenid;

    @Column(name = "customer_id")
    private String customerId;

    /**
     * 微信获取用户昵称
     */
    @Column(name = "wechat_nickname")
    private String wechatNickname;

    /**
     * 微信用户手机号
     */
    @Column(name = "wechat_phone")
    private String wechatPhone;

    /**
     * 微信用户头像链接
     */
    @Column(name = "wechat_avatar_src")
    private String wechatAvatarSrc;

    /**
     * 用户姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户性别： 1， 男， 2 女， 0 未知
     */
    @Column(name = "user_gender")
    private Byte userGender;

    /**
     * 用户生日
     */
    @Column(name = "user_birthdate")
    private String userBirthdate;

    /**
     * 用户本地头像，可供用户替换头像；2作为备用
     */
    @Column(name = "user_avatar_src")
    private String userAvatarSrc;

    /**
     * 系统创建-给相关类名
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 记录创建时间
     */
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建者unionid-可为用户、管理员、系统等
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 记录修改时间
     */
    @LastModifiedDate
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 逻辑删除，默认为0未删除，1已删除
     */
    @Column(name = "deleted")
    private Byte deleted = 0;

    private static final long serialVersionUID = 1L;

    // Default constructor
    public AppCustomerInfo() {}

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getMiniOpenid() {
        return miniOpenid;
    }

    public void setMiniOpenid(String miniOpenid) {
        this.miniOpenid = miniOpenid;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getWechatNickname() {
        return wechatNickname;
    }

    public void setWechatNickname(String wechatNickname) {
        this.wechatNickname = wechatNickname;
    }

    public String getWechatPhone() {
        return wechatPhone;
    }

    public void setWechatPhone(String wechatPhone) {
        this.wechatPhone = wechatPhone;
    }

    public String getWechatAvatarSrc() {
        return wechatAvatarSrc;
    }

    public void setWechatAvatarSrc(String wechatAvatarSrc) {
        this.wechatAvatarSrc = wechatAvatarSrc;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Byte getUserGender() {
        return userGender;
    }

    public void setUserGender(Byte userGender) {
        this.userGender = userGender;
    }

    public String getUserBirthdate() {
        return userBirthdate;
    }

    public void setUserBirthdate(String userBirthdate) {
        this.userBirthdate = userBirthdate;
    }

    public String getUserAvatarSrc() {
        return userAvatarSrc;
    }

    public void setUserAvatarSrc(String userAvatarSrc) {
        this.userAvatarSrc = userAvatarSrc;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", unionid=").append(unionid);
        sb.append(", miniOpenid=").append(miniOpenid);
        sb.append(", customerId=").append(customerId);
        sb.append(", wechatNickname=").append(wechatNickname);
        sb.append(", wechatPhone=").append(wechatPhone);
        sb.append(", wechatAvatarSrc=").append(wechatAvatarSrc);
        sb.append(", userName=").append(userName);
        sb.append(", userGender=").append(userGender);
        sb.append(", userBirthdate=").append(userBirthdate);
        sb.append(", userAvatarSrc=").append(userAvatarSrc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
