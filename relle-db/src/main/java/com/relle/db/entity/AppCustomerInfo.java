package com.relle.db.entity;

import javax.persistence.*;

/**
 * Customer Information Entity
 * <AUTHOR> by sj
 * @version v1.0.1
 * @Database table name is app_customer_info
 * @create 2023-12-07 17:21:51
 */
@Entity
@Table(name = "app_customer_info")
public class AppCustomerInfo extends BaseEntity {

    /**
     * 微信唯一识别码
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 小程序openid
     */
    @Column(name = "mini_openid")
    private String miniOpenid;

    @Column(name = "customer_id")
    private String customerId;

    /**
     * 微信获取用户昵称
     */
    @Column(name = "wechat_nickname")
    private String wechatNickname;

    /**
     * 微信用户手机号
     */
    @Column(name = "wechat_phone")
    private String wechatPhone;

    /**
     * 微信用户头像链接
     */
    @Column(name = "wechat_avatar_src")
    private String wechatAvatarSrc;

    /**
     * 用户姓名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 用户性别： 1， 男， 2 女， 0 未知
     */
    @Column(name = "user_gender")
    private Byte userGender;

    /**
     * 用户生日
     */
    @Column(name = "user_birthdate")
    private String userBirthdate;

    /**
     * 用户本地头像，可供用户替换头像；2作为备用
     */
    @Column(name = "user_avatar_src")
    private String userAvatarSrc;

    private static final long serialVersionUID = 1L;

    // Default constructor
    public AppCustomerInfo() {}

    // Getters and Setters for entity-specific fields

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getMiniOpenid() {
        return miniOpenid;
    }

    public void setMiniOpenid(String miniOpenid) {
        this.miniOpenid = miniOpenid;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getWechatNickname() {
        return wechatNickname;
    }

    public void setWechatNickname(String wechatNickname) {
        this.wechatNickname = wechatNickname;
    }

    public String getWechatPhone() {
        return wechatPhone;
    }

    public void setWechatPhone(String wechatPhone) {
        this.wechatPhone = wechatPhone;
    }

    public String getWechatAvatarSrc() {
        return wechatAvatarSrc;
    }

    public void setWechatAvatarSrc(String wechatAvatarSrc) {
        this.wechatAvatarSrc = wechatAvatarSrc;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Byte getUserGender() {
        return userGender;
    }

    public void setUserGender(Byte userGender) {
        this.userGender = userGender;
    }

    public String getUserBirthdate() {
        return userBirthdate;
    }

    public void setUserBirthdate(String userBirthdate) {
        this.userBirthdate = userBirthdate;
    }

    public String getUserAvatarSrc() {
        return userAvatarSrc;
    }

    public void setUserAvatarSrc(String userAvatarSrc) {
        this.userAvatarSrc = userAvatarSrc;
    }

    // Base entity getters/setters are inherited from BaseEntity

    @Override
    public String toString() {
        return "AppCustomerInfo{" +
                "id=" + id +
                ", unionid='" + unionid + '\'' +
                ", miniOpenid='" + miniOpenid + '\'' +
                ", customerId='" + customerId + '\'' +
                ", wechatNickname='" + wechatNickname + '\'' +
                ", wechatPhone='" + wechatPhone + '\'' +
                ", wechatAvatarSrc='" + wechatAvatarSrc + '\'' +
                ", userName='" + userName + '\'' +
                ", userGender=" + userGender +
                ", userBirthdate='" + userBirthdate + '\'' +
                ", userAvatarSrc='" + userAvatarSrc + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", deleted=" + deleted +
                '}';
    }
}
