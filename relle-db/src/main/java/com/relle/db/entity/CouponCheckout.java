package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Coupon Checkout Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_coupon_checkout
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_coupon_checkout")
public class CouponCheckout extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 适用门店IDs（逗号分隔）
     */
    @Column(name = "store_ids")
    private String storeIds;

    /**
     * 领用优惠券编号
     */
    @Column(name = "coupon_id")
    private String couponId;

    /**
     * 优惠券接收ID
     */
    @Column(name = "coupon_receive_id", unique = true)
    private String couponReceiveId;

    /**
     * 领用人unionid
     */
    @Column(name = "receive_customer_unionid")
    private String receiveCustomerUnionid;

    /**
     * 领用时间
     */
    @Column(name = "receive_time")
    private LocalDateTime receiveTime;

    /**
     * 优惠券有效开始时间
     */
    @Column(name = "coupon_valid_starttime")
    private LocalDateTime couponValidStarttime;

    /**
     * 优惠券有效结束时间
     */
    @Column(name = "coupon_valid_endtime")
    private LocalDateTime couponValidEndtime;

    /**
     * 优惠券状态，默认1未使用；2已使用；4已过期；64已下架；
     */
    @Column(name = "coupon_status")
    private Integer couponStatus;

    /**
     * 优惠券消费的订单编号
     */
    @Column(name = "use_coupon_orderid")
    private String useCouponOrderid;

    /**
     * 优惠券消费时间
     */
    @Column(name = "use_coupon_time")
    private LocalDateTime useCouponTime;

    /**
     * 卡券来源，1用户领取；2系统发放；4用户购买(代金券) 8其他人赠送
     */
    @Column(name = "coupon_source")
    private Integer couponSource;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
