package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Create Pay Order Log Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_create_payorder_log
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_create_payorder_log")
public class CreatePayorderLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 支付系统订单ID
     */
    @Column(name = "pay_system_orderid")
    private String paySystemOrderid;

    /**
     * 用户unionid
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 支付金额
     */
    @Column(name = "pay_amount", precision = 10, scale = 2)
    private BigDecimal payAmount;

    /**
     * 支付渠道
     */
    @Column(name = "pay_channel")
    private String payChannel;

    /**
     * 创建时间
     */
    @Column(name = "create_pay_time")
    private LocalDateTime createPayTime;

    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    private String operationType;

    /**
     * 请求参数
     */
    @Column(name = "request_params", columnDefinition = "TEXT")
    private String requestParams;

    /**
     * 响应参数
     */
    @Column(name = "response_params", columnDefinition = "TEXT")
    private String responseParams;

    /**
     * 创建状态
     */
    @Column(name = "create_status")
    private Integer createStatus;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 处理时长(毫秒)
     */
    @Column(name = "processing_time")
    private Long processingTime;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
