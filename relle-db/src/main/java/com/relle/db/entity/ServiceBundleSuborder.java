package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "service_bundle_suborder")
public class ServiceBundleSuborder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 套餐订单编号
     */
    @Column(name = "bundle_order_id")
    private String bundleOrderId;

    /**
     * 套餐子订单编号
     */
    @Column(name = "bundle_suborder_id", unique = true)
    private String bundleSuborderId;

    /**
     * 用户unionid
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店编号
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 房间ID
     */
    @Column(name = "room_id")
    private String roomId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 预约服务开始时间
     */
    @Column(name = "book_time")
    private LocalDateTime bookTime;

    /**
     * 子订单原价
     */
    @Column(name = "suborder_origin_price", precision = 10, scale = 2)
    private BigDecimal suborderOriginPrice;

    /**
     * 子订单减免金额
     */
    @Column(name = "suborder_reduction_amount", precision = 10, scale = 2)
    private BigDecimal suborderReductionAmount;

    /**
     * 子订单金额
     */
    @Column(name = "suborder_amount", precision = 10, scale = 2)
    private BigDecimal suborderAmount;

    /**
     * 子订单状态
     */
    @Column(name = "suborder_status")
    private Short suborderStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
