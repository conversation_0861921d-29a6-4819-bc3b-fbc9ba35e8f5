package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Coupon Service Relation Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_coupon_service_relation
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_coupon_service_relation")
public class CouponServiceRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠券号
     */
    @Column(name = "coupon_id")
    private String couponId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
