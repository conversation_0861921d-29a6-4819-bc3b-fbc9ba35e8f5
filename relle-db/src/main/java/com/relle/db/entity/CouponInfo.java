package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "coupon_info")
public class CouponInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠券号
     */
    @Column(name = "coupon_id", unique = true)
    private String couponId;

    /**
     * 优惠券名称
     */
    @Column(name = "coupon_name")
    private String couponName;

    /**
     * 卡券分类，1代金券；2抵扣券；3特殊券
     */
    @Column(name = "coupon_category")
    private Integer couponCategory;

    /**
     * 优惠券类型
     */
    @Column(name = "coupon_type")
    private String couponType;

    /**
     * 优惠券封面图
     */
    @Column(name = "coupon_cover_img")
    private String couponCoverImg;

    /**
     * 优惠券发放总数，如果是0，则是无限量
     */
    @Column(name = "coupon_issue_total")
    private Integer couponIssueTotal;

    /**
     * 最低可使用订单金额
     */
    @Column(name = "coupon_min_charge", precision = 10, scale = 2)
    private BigDecimal couponMinCharge;

    /**
     * 优惠金额
     */
    @Column(name = "coupon_value_amount", precision = 10, scale = 2)
    private BigDecimal couponValueAmount;

    /**
     * 开始有效期
     */
    @Column(name = "coupon_valid_datetime_begin")
    private LocalDateTime couponValidDatetimeBegin;

    /**
     * 结束有效期
     */
    @Column(name = "coupon_valid_datetime_end")
    private LocalDateTime couponValidDatetimeEnd;

    /**
     * 叠加规则，0完全不可叠加；1普通券，可叠加通用券；2通用券
     */
    @Column(name = "can_superpose")
    private Integer canSuperpose;

    /**
     * 优惠券领用限制，0不限制，默认是1，限领一张
     */
    @Column(name = "coupon_receive_limit")
    private Integer couponReceiveLimit;

    /**
     * 优惠券领用有效期天数，0,不限制（与优惠券有效期一致），大于1的即为天数
     */
    @Column(name = "coupon_reveive_validday")
    private Integer couponReveiveValidday;

    /**
     * 优惠券状态，1则是正常可用；2则是过期; 64则是下架。预留几个状态
     */
    @Column(name = "coupon_status")
    private Integer couponStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
