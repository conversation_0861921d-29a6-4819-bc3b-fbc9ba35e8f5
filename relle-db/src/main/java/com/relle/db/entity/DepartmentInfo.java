package com.relle.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "department_info")
public class DepartmentInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 部门名称
     */
    @Column(name = "name")
    private String name;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
