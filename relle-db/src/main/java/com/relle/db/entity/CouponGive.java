package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "coupon_give")
public class CouponGive extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 优惠券编号
     */
    @Column(name = "coupon_no")
    private Long couponNo;

    /**
     * 优惠券ID
     */
    @Column(name = "coupon_id")
    private String couponId;

    /**
     * 优惠券接收ID
     */
    @Column(name = "coupon_receive_id")
    private String couponReceiveId;

    /**
     * 优惠券名称
     */
    @Column(name = "coupon_name")
    private String couponName;

    /**
     * 赠送用户ID
     */
    @Column(name = "give_user_id")
    private String giveUserId;

    /**
     * 赠送用户unionid
     */
    @Column(name = "give_user_unionid")
    private String giveUserUnionid;

    /**
     * 赠送时间
     */
    @Column(name = "give_time")
    private LocalDateTime giveTime;

    /**
     * 接收用户unionid
     */
    @Column(name = "receive_user_unionid")
    private String receiveUserUnionid;

    /**
     * 接收用户ID
     */
    @Column(name = "receive_user_id")
    private String receiveUserId;

    /**
     * 接收时间
     */
    @Column(name = "receive_time")
    private LocalDateTime receiveTime;

    /**
     * 赠送状态，1赠送中 2已领取 4已退回
     */
    @Column(name = "give_status")
    private Byte giveStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
