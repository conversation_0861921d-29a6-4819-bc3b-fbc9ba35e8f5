package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "order_pay")
public class OrderPay extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 订单来源
     */
    @Column(name = "order_source")
    private String orderSource;

    /**
     * 支付系统订单ID
     */
    @Column(name = "pay_system_orderid")
    private String paySystemOrderid;

    /**
     * 支付结束时间
     */
    @Column(name = "pay_end_time")
    private LocalDateTime payEndTime;

    /**
     * 支付渠道ID
     */
    @Column(name = "pay_chanel_id")
    private String payChanelId;

    /**
     * 支付时间
     */
    @Column(name = "pay_time")
    private LocalDateTime payTime;

    /**
     * 支付金额
     */
    @Column(name = "pay_amount", precision = 10, scale = 2)
    private BigDecimal payAmount;

    /**
     * 支付参数时间戳
     */
    @Column(name = "pay_params_timestamp")
    private String payParamsTimestamp;

    /**
     * 支付参数包
     */
    @Column(name = "pay_params_package")
    private String payParamsPackage;

    /**
     * 支付参数签名
     */
    @Column(name = "pay_params_paysign")
    private String payParamsPaysign;

    /**
     * 支付参数AppID
     */
    @Column(name = "pay_prams_appid")
    private String payPramsAppid;

    /**
     * 支付参数签名类型
     */
    @Column(name = "pay_params_signtype")
    private String payParamsSigntype;

    /**
     * 支付参数随机字符串
     */
    @Column(name = "pay_prarams_noncestr")
    private String payPraramsNoncestr;

    /**
     * 支付状态，0待支付，1已支付
     */
    @Column(name = "pay_status")
    private Byte payStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
