package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_order")
public class ServiceOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id", unique = true)
    private String orderId;

    /**
     * 用户小程序唯一id
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店编号，如SH0001
     */
    @Column(name = "sotre_id")
    private String sotreId;

    /**
     * 房间ID
     */
    @Column(name = "room_id")
    private String roomId;

    /**
     * 订单金额->子订单汇总金额
     */
    @Column(name = "order_amount", precision = 10, scale = 2)
    private BigDecimal orderAmount;

    /**
     * 订单联系姓名（可以不是用户本人）
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 订单联系人电话（可以不是微信获取电话）
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 订单原价->子订单原价格汇总
     */
    @Column(name = "origin_price", precision = 10, scale = 2)
    private BigDecimal originPrice;

    /**
     * 减免金额->子订单减免金额汇总
     */
    @Column(name = "reduction_amount", precision = 10, scale = 2)
    private BigDecimal reductionAmount;

    /**
     * 订单状态-无符号int，状态可以为负数，表明异常状态；
     * 1：待支付，2: 待服务，4: 服务中，8: 已完成。 
     * -1：售后中； -2: 已退款； -4: 已取消；-8:部分已退款；-16:部分已取消；
     */
    @Column(name = "order_status")
    private Short orderStatus;

    /**
     * 修改次数
     */
    @Column(name = "modify_num")
    private Byte modifyNum;

    /**
     * 客户来源
     */
    @Column(name = "customer_source")
    private String customerSource;

    /**
     * 源订单ID
     */
    @Column(name = "source_order_id")
    private String sourceOrderId;

    /**
     * 核销状态
     */
    @Column(name = "write_off")
    private Byte writeOff;

    /**
     * 核销时间
     */
    @Column(name = "write_off_time")
    private LocalDateTime writeOffTime;

    /**
     * 核销用户
     */
    @Column(name = "write_off_user")
    private String writeOffUser;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
