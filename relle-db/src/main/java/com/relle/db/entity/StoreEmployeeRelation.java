package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Store Employee Relation Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_store_employee_relation
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_store_employee_relation")
public class StoreEmployeeRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 员工在该门店的角色
     */
    @Column(name = "employee_role")
    private String employeeRole;

    /**
     * 关系状态
     */
    @Column(name = "relation_status")
    private Integer relationStatus;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
