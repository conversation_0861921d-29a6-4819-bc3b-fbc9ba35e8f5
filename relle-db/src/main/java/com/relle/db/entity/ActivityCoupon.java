package com.relle.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Activity Coupon Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_activity_coupon
 * @create 2023-12-07 17:21:51
 */
@Data
@Entity
@Table(name = "app_activity_coupon")
public class ActivityCoupon implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动ID
     */
    @Column(name = "activity_id")
    private Long activityId;

    /**
     * 优惠券ID
     */
    @Column(name = "coupon_id")
    private Long couponId;

    /**
     * 是否在规则内
     */
    @Column(name = "in_rule")
    private Integer inRule;

    /**
     * 规则描述
     */
    @Column(name = "rule", columnDefinition = "TEXT")
    private String rule;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
