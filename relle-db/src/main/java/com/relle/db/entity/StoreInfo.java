package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_store_info")
public class StoreInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺id
     */
    @Column(name = "store_id", unique = true)
    private String storeId;

    /**
     * 店铺名称
     */
    @Column(name = "store_name")
    private String storeName;

    /**
     * 店铺地址
     */
    @Column(name = "store_address")
    private String storeAddress;

    /**
     * 店铺电话
     */
    @Column(name = "store_phone")
    private String storePhone;

    /**
     * 店铺封面图
     */
    @Column(name = "store_conver_src")
    private String storeConverSrc;

    /**
     * 店铺坐标经度
     */
    @Column(name = "store_longitude", precision = 10, scale = 7)
    private BigDecimal storeLongitude;

    /**
     * 店铺坐标纬度
     */
    @Column(name = "store_latitude", precision = 10, scale = 7)
    private BigDecimal storeLatitude;

    /**
     * 开店日期
     */
    @Column(name = "store_opening_date")
    private LocalDate storeOpeningDate;

    /**
     * 门店详情
     */
    @Column(name = "store_detail_info", columnDefinition = "TEXT")
    private String storeDetailInfo;

    /**
     * 门店状态：1， 营业中， 2， 即将开业； 32，已关闭；
     */
    @Column(name = "store_status")
    private Byte storeStatus;

    /**
     * 门店类型：0，美容护理；1，诊所；
     */
    @Column(name = "store_type")
    private Byte storeType;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
