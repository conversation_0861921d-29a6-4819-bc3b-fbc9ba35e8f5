package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "employee_info")
public class EmployeeInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 员工工号：unique
     */
    @Column(name = "employee_id", unique = true)
    private String employeeId;

    /**
     * 员工微信openid
     */
    @Column(name = "employee_open_id")
    private String employeeOpenId;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    private String employeeName;

    /**
     * 员工手机号
     */
    @Column(name = "employee_phone")
    private String employeePhone;

    /**
     * 员工身份证号
     */
    @Column(name = "employee_idcard")
    private String employeeIdcard;

    /**
     * 员工备注笔记（含紧急联系人等，页面端限定紧急联系人必填）
     */
    @Column(name = "employee_note", columnDefinition = "TEXT")
    private String employeeNote;

    /**
     * 状态：1， 在职；2，已离职；4，休假中；8，其他
     */
    @Column(name = "employee_status")
    private Integer employeeStatus;

    /**
     * 员工所在部门，以后有多个部门需重新创建部门表
     */
    @Column(name = "department_id")
    private Integer departmentId;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
