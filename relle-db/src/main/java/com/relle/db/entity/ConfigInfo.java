package com.relle.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "config_info")
public class ConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置组编号
     */
    @Column(name = "group_no")
    private String groupNo;

    /**
     * 规则名称
     */
    @Column(name = "rule_name")
    private String ruleName;

    /**
     * 规则值
     */
    @Column(name = "rule_value", columnDefinition = "TEXT")
    private String ruleValue;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
