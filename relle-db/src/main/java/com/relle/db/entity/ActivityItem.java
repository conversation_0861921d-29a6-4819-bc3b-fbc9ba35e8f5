package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Activity Item Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_activity_item
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_activity_item")
public class ActivityItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    @Column(name = "activity_id")
    private String activityId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 活动规则
     */
    @Column(name = "activity_rule", columnDefinition = "TEXT")
    private String activityRule;

    /**
     * 活动状态
     */
    @Column(name = "activity_status")
    private Integer activityStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
