package com.relle.db.entity;

import javax.persistence.*;

/**
 * Employee Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_employee
 * @create 2023-12-07 17:21:51
 */
@Entity
@Table(name = "app_employee")
public class AppEmployee extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 员工工号：unique
     */
    @Column(name = "employee_id", unique = true)
    private String employeeId;

    /**
     * 员工微信openid
     */
    @Column(name = "employee_open_id")
    private String employeeOpenId;

    /**
     * 员工姓名
     */
    @Column(name = "employee_name")
    private String employeeName;

    /**
     * 员工手机号
     */
    @Column(name = "employee_phone")
    private String employeePhone;

    /**
     * 员工身份证号
     */
    @Column(name = "employee_idcard")
    private String employeeIdcard;

    /**
     * 员工备注笔记（含紧急联系人等，页面端限定紧急联系人必填）
     */
    @Column(name = "employee_note", columnDefinition = "TEXT")
    private String employeeNote;

    /**
     * 状态：1， 在职；2，已离职；4，休假中；8，其他
     */
    @Column(name = "employee_status")
    private Byte employeeStatus;

    /**
     * 员工所在部门，以后有多个部门需重新创建部门表
     */
    @Column(name = "department_id")
    private Byte departmentId;

    // Default constructor
    public AppEmployee() {}

    // Getters and Setters for entity-specific fields
    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeOpenId() {
        return employeeOpenId;
    }

    public void setEmployeeOpenId(String employeeOpenId) {
        this.employeeOpenId = employeeOpenId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeePhone() {
        return employeePhone;
    }

    public void setEmployeePhone(String employeePhone) {
        this.employeePhone = employeePhone;
    }

    public String getEmployeeIdcard() {
        return employeeIdcard;
    }

    public void setEmployeeIdcard(String employeeIdcard) {
        this.employeeIdcard = employeeIdcard;
    }

    public String getEmployeeNote() {
        return employeeNote;
    }

    public void setEmployeeNote(String employeeNote) {
        this.employeeNote = employeeNote;
    }

    public Byte getEmployeeStatus() {
        return employeeStatus;
    }

    public void setEmployeeStatus(Byte employeeStatus) {
        this.employeeStatus = employeeStatus;
    }

    public Byte getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Byte departmentId) {
        this.departmentId = departmentId;
    }

    @Override
    public String toString() {
        return "AppEmployee{" +
                "id=" + id +
                ", employeeId='" + employeeId + '\'' +
                ", employeeOpenId='" + employeeOpenId + '\'' +
                ", employeeName='" + employeeName + '\'' +
                ", employeePhone='" + employeePhone + '\'' +
                ", employeeIdcard='" + employeeIdcard + '\'' +
                ", employeeNote='" + employeeNote + '\'' +
                ", employeeStatus=" + employeeStatus +
                ", departmentId=" + departmentId +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", deleted=" + deleted +
                '}';
    }
}
