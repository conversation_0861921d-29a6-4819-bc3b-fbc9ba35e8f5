package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mch_account_config")
public class MchAccountConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商户账户ID
     */
    @Column(name = "mch_account_id")
    private String mchAccountId;

    /**
     * 配置键
     */
    @Column(name = "config_key")
    private String configKey;

    /**
     * 配置值
     */
    @Column(name = "config_value", columnDefinition = "TEXT")
    private String configValue;

    /**
     * 配置描述
     */
    @Column(name = "config_description")
    private String configDescription;

    /**
     * 配置状态
     */
    @Column(name = "config_status")
    private Byte configStatus;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
