package com.relle.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Role Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_role
 * @create 2023-12-07 17:21:51
 */
@Data
@Entity
@Table(name = "app_role")
public class AppRole implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 角色名称
     */
    @Column(name = "name")
    private String name;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
