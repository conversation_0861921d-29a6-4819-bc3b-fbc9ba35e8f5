package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Store Service Relation Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_store_service_relation
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_store_service_relation")
public class StoreServiceRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 服务状态
     */
    @Column(name = "service_status")
    private Integer serviceStatus;

    /**
     * 服务优先级
     */
    @Column(name = "service_priority")
    private Integer servicePriority;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
