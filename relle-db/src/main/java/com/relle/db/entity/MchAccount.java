package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mch_account")
public class MchAccount extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商户账户ID
     */
    @Column(name = "mch_account_id", unique = true)
    private String mchAccountId;

    /**
     * 商户ID
     */
    @Column(name = "mch_id")
    private String mchId;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 账户名称
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 账户类型
     */
    @Column(name = "account_type")
    private String accountType;

    /**
     * 账户余额
     */
    @Column(name = "account_balance", precision = 15, scale = 2)
    private BigDecimal accountBalance;

    /**
     * 冻结金额
     */
    @Column(name = "frozen_amount", precision = 15, scale = 2)
    private BigDecimal frozenAmount;

    /**
     * 账户状态
     */
    @Column(name = "account_status")
    private Integer accountStatus;

    /**
     * 账户描述
     */
    @Column(name = "account_description", columnDefinition = "TEXT")
    private String accountDescription;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
