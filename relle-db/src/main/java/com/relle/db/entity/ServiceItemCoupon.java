package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Service Item Coupon Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_item_coupon
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_item_coupon")
public class ServiceItemCoupon extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 产品服务编号(这里一般都是代金券卡包）
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 卡券id
     */
    @Column(name = "coupon_id")
    private String couponId;

    /**
     * 卡券张数
     */
    @Column(name = "coupon_num")
    private Integer couponNum;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
