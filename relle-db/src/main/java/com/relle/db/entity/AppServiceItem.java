package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * Service Item Entity
 * <AUTHOR> by sj
 * @version v1.0.1
 * @Database table name is app_service_item
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_item")
public class AppServiceItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 服务编号
     */
    @Column(name = "service_id")
    private String serviceId;

    /**
     * 服务名称
     */
    @Column(name = "service_name")
    private String serviceName;

    /**
     * 服务简介
     */
    @Column(name = "service_details", columnDefinition = "TEXT")
    private String serviceDetails;

    /**
     * 服务定价
     */
    @Column(name = "service_price", precision = 10, scale = 2)
    private BigDecimal servicePrice;

    /**
     * 类型（树型结构）
     */
    @Column(name = "service_tagtree")
    private String serviceTagtree;

    /**
     * 服务内容
     */
    @Column(name = "service_content", columnDefinition = "TEXT")
    private String serviceContent;

    /**
     * 服务时长（秒）
     */
    @Column(name = "service_duration_sec")
    private Integer serviceDurationSec;

    /**
     * 准备时长
     */
    @Column(name = "service_preparation_sec")
    private Integer servicePreparationSec;

    /**
     * 结束时长
     */
    @Column(name = "service_closing_sec")
    private Integer serviceClosingSec;

    /**
     * 上架；下架
     */
    @Column(name = "service_status")
    private Byte serviceStatus;

    /**
     * 服务类型
     */
    @Column(name = "service_type")
    private String serviceType;

    /**
     * 渠道类型，区分operator可见性，店长可见性，用户可见性，总店长可见性等
     */
    @Column(name = "channel_type")
    private Byte channelType;

    /**
     * 商品展示排序
     */
    @Column(name = "service_show_sort")
    private Byte serviceShowSort;

    /**
     * 可加单，0不可加单，1可加单
     */
    @Column(name = "can_add_item")
    private Byte canAddItem;

    /**
     * 限制角色
     */
    @Column(name = "restricted_role")
    private Byte restrictedRole;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
