package com.relle.db.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Department Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_department
 * @create 2023-12-07 17:21:51
 */
@Data
@Entity
@Table(name = "app_department")
public class AppDepartment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 部门名称
     */
    @Column(name = "name")
    private String name;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
