package com.relle.db.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Base Entity class containing common fields for all entities
 * All entity classes should extend this base class
 *
 * <AUTHOR>
 * @version v1.0.1
 */
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key - auto-generated ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;

    /**
     * 创建者unionid-可为用户、店员、店长、系统等
     * Creator's unionid - can be user, employee, manager, system, etc.
     */
    @Column(name = "create_by")
    protected String createBy;

    /**
     * 记录创建时间
     * Record creation time
     */
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime createTime;

    /**
     * 更新者unionid-可为用户、店员、店长、系统等
     * Updater's unionid - can be user, employee, manager, system, etc.
     */
    @Column(name = "update_by")
    protected String updateBy;

    /**
     * 记录修改时间
     * Record modification time
     */
    @LastModifiedDate
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected LocalDateTime updateTime;

    /**
     * 逻辑删除，默认为0未删除，1已删除
     * Logical deletion flag, default 0 (not deleted), 1 (deleted)
     */
    @Column(name = "deleted")
    protected Byte deleted = 0;
}
