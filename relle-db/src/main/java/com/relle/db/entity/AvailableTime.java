package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Available Time Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_available_time
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_available_time")
public class AvailableTime extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 房间ID
     */
    @Column(name = "room_id")
    private String roomId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 可用日期
     */
    @Column(name = "available_date")
    private LocalDateTime availableDate;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private LocalTime startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalTime endTime;

    /**
     * 时间段状态
     */
    @Column(name = "time_status")
    private Integer timeStatus;

    /**
     * 最大预约数量
     */
    @Column(name = "max_booking_count")
    private Integer maxBookingCount;

    /**
     * 当前预约数量
     */
    @Column(name = "current_booking_count")
    private Integer currentBookingCount;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
