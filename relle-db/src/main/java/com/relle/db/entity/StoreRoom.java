package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "store_room")
public class StoreRoom extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺id（编号）
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 房间id（编号）
     */
    @Column(name = "room_id", unique = true)
    private String roomId;

    /**
     * 房间名
     */
    @Column(name = "room_name")
    private String roomName;

    /**
     * 房间类型，设置可以哪些服务类型
     */
    @Column(name = "room_type")
    private Integer roomType;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
