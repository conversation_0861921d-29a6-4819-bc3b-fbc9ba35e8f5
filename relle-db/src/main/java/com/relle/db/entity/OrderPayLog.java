package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Order Pay Log Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_order_pay_log
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_order_pay_log")
public class OrderPayLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 支付系统订单ID
     */
    @Column(name = "pay_system_orderid")
    private String paySystemOrderid;

    /**
     * 支付渠道ID
     */
    @Column(name = "pay_channel_id")
    private String payChannelId;

    /**
     * 支付金额
     */
    @Column(name = "pay_amount", precision = 10, scale = 2)
    private BigDecimal payAmount;

    /**
     * 支付状态
     */
    @Column(name = "pay_status")
    private Integer payStatus;

    /**
     * 支付时间
     */
    @Column(name = "pay_time")
    private LocalDateTime payTime;

    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @Column(name = "operation_description", columnDefinition = "TEXT")
    private String operationDescription;

    /**
     * 请求参数
     */
    @Column(name = "request_params", columnDefinition = "TEXT")
    private String requestParams;

    /**
     * 响应参数
     */
    @Column(name = "response_params", columnDefinition = "TEXT")
    private String responseParams;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
