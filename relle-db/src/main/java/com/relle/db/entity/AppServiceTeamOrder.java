package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Service Team Order Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_team_order
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_team_order")
public class AppServiceTeamOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 团购订单编号
     */
    @Column(name = "team_order_id", unique = true)
    private String teamOrderId;

    /**
     * 发起人unionid
     */
    @Column(name = "initiator_unionid")
    private String initiatorUnionid;

    /**
     * 门店编号
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 团购人数要求
     */
    @Column(name = "required_people_count")
    private Integer requiredPeopleCount;

    /**
     * 当前参团人数
     */
    @Column(name = "current_people_count")
    private Integer currentPeopleCount;

    /**
     * 团购价格
     */
    @Column(name = "team_price", precision = 10, scale = 2)
    private BigDecimal teamPrice;

    /**
     * 原价
     */
    @Column(name = "origin_price", precision = 10, scale = 2)
    private BigDecimal originPrice;

    /**
     * 团购开始时间
     */
    @Column(name = "team_start_time")
    private LocalDateTime teamStartTime;

    /**
     * 团购结束时间
     */
    @Column(name = "team_end_time")
    private LocalDateTime teamEndTime;

    /**
     * 团购状态
     */
    @Column(name = "team_status")
    private Short teamStatus;

    /**
     * 团购描述
     */
    @Column(name = "team_description", columnDefinition = "TEXT")
    private String teamDescription;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
