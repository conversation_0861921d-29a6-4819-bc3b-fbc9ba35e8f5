package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * Service Media Relation Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_service_media_relation
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_media_relation")
public class ServiceMediaRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 服务项目ID
     */
    @Column(name = "service_item_id")
    private String serviceItemId;

    /**
     * 媒体ID
     */
    @Column(name = "media_id")
    private String mediaId;

    /**
     * 关系类型
     */
    @Column(name = "relation_type")
    private String relationType;

    /**
     * 媒体用途
     */
    @Column(name = "media_usage")
    private String mediaUsage;

    /**
     * 排序值
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 关系状态
     */
    @Column(name = "relation_status")
    private Integer relationStatus;

    /**
     * 是否主要媒体
     */
    @Column(name = "is_primary")
    private Integer isPrimary;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
