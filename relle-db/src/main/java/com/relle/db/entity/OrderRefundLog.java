package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Order Refund Log Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_order_refund_log
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_order_refund_log")
public class OrderRefundLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 退款单号
     */
    @Column(name = "refund_order_id")
    private String refundOrderId;

    /**
     * 退款金额
     */
    @Column(name = "refund_amount", precision = 10, scale = 2)
    private BigDecimal refundAmount;

    /**
     * 退款状态
     */
    @Column(name = "refund_status")
    private Integer refundStatus;

    /**
     * 退款时间
     */
    @Column(name = "refund_time")
    private LocalDateTime refundTime;

    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @Column(name = "operation_description", columnDefinition = "TEXT")
    private String operationDescription;

    /**
     * 退款原因
     */
    @Column(name = "refund_reason", columnDefinition = "TEXT")
    private String refundReason;

    /**
     * 请求参数
     */
    @Column(name = "request_params", columnDefinition = "TEXT")
    private String requestParams;

    /**
     * 响应参数
     */
    @Column(name = "response_params", columnDefinition = "TEXT")
    private String responseParams;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
