package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_activity")
public class ActivityInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 适用门店IDs（逗号分隔）
     */
    @Column(name = "store_ids")
    private String storeIds;

    /**
     * 活动id
     */
    @Column(name = "activity_id", unique = true)
    private String activityId;

    /**
     * 活动名称
     */
    @Column(name = "activity_name")
    private String activityName;

    /**
     * 活动封面图
     */
    @Column(name = "activity_conver_src")
    private String activityConverSrc;

    /**
     * 活动内容
     */
    @Column(name = "activity_content", columnDefinition = "TEXT")
    private String activityContent;

    /**
     * 活动分类，1弹窗，2banner
     */
    @Column(name = "activity_category")
    private Integer activityCategory;

    /**
     * 链接
     */
    @Column(name = "activity_url")
    private String activityUrl;

    /**
     * 活动开始时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 排序值
     */
    @Column(name = "sort_code")
    private Short sortCode;

    /**
     * 状态，0未上架，1已上架
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 优惠券数量
     */
    @Column(name = "coupon_num")
    private Integer couponNum;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
