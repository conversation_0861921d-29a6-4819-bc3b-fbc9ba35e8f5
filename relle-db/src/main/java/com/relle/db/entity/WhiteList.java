package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * White List Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_white_list
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_white_list")
public class WhiteList extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 白名单类型
     */
    @Column(name = "white_list_type")
    private String whiteListType;

    /**
     * 白名单值
     */
    @Column(name = "white_list_value")
    private String whiteListValue;

    /**
     * 白名单描述
     */
    @Column(name = "white_list_description")
    private String whiteListDescription;

    /**
     * 白名单状态
     */
    @Column(name = "white_list_status")
    private Integer whiteListStatus;

    /**
     * 优先级
     */
    @Column(name = "priority")
    private Integer priority;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
