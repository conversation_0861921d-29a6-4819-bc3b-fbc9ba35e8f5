package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_service_category")
public class ServiceCategory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 服务分类id
     */
    @Column(name = "service_category_id", unique = true)
    private String serviceCategoryId;

    /**
     * 服务分类名称
     */
    @Column(name = "service_category_name")
    private String serviceCategoryName;

    /**
     * 父分类id
     */
    @Column(name = "service_category_parent_id")
    private String serviceCategoryParentId;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
