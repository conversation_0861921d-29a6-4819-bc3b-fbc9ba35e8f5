package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_order_refund")
public class OrderRefund extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 订单金额
     */
    @Column(name = "order_amount", precision = 10, scale = 2)
    private BigDecimal orderAmount;

    /**
     * 退款单号（规则在订单号后加r加两位数字）
     */
    @Column(name = "refund_order_id", unique = true)
    private String refundOrderId;

    /**
     * 申请退款时间
     */
    @Column(name = "refund_time")
    private LocalDateTime refundTime;

    /**
     * 申请退款金额
     */
    @Column(name = "refund_amount", precision = 10, scale = 2)
    private BigDecimal refundAmount;

    /**
     * 退款状态，1退款中，2退款成功，4退款失败。由于微信退款失败的话，还是重新发起时沿用原来的退款单号
     */
    @Column(name = "refund_status")
    private Byte refundStatus;

    /**
     * 退款原因
     */
    @Column(name = "refund_reason", columnDefinition = "TEXT")
    private String refundReason;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
