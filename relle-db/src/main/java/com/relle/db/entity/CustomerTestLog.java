package com.relle.db.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Customer Test Log Entity
 * <AUTHOR> by sj
 * @version v1.0.1 
 * @Database table name is app_customer_test_log
 * @create 2023-12-07 17:21:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "app_customer_test_log")
public class CustomerTestLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户unionid
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 门店ID
     */
    @Column(name = "store_id")
    private String storeId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    private String employeeId;

    /**
     * 测试类型
     */
    @Column(name = "test_type")
    private String testType;

    /**
     * 测试时间
     */
    @Column(name = "test_time")
    private LocalDateTime testTime;

    /**
     * 测试结果
     */
    @Column(name = "test_result", columnDefinition = "TEXT")
    private String testResult;

    /**
     * 测试数据
     */
    @Column(name = "test_data", columnDefinition = "TEXT")
    private String testData;

    /**
     * 测试状态
     */
    @Column(name = "test_status")
    private Integer testStatus;

    /**
     * 设备信息
     */
    @Column(name = "device_info")
    private String deviceInfo;

    /**
     * 测试环境
     */
    @Column(name = "test_environment")
    private String testEnvironment;

    /**
     * 备注
     */
    @Column(name = "remark", columnDefinition = "TEXT")
    private String remark;

    // Lombok @Data annotation will generate getters, setters, toString, equals, and hashCode
}
