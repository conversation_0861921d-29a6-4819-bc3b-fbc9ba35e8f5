spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    username: root
    password: 9a9*5b9*GGl1234gg
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      ddl-auto: validate # Use 'update' during development, 'validate' in production
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true