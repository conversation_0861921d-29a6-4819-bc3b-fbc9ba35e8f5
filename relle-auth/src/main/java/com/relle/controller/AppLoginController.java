package com.relle.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.common.api.CommonResult;
import com.relle.db.entity.AppEmployee;
import com.relle.service.IAppEmployeeService;
import com.relle.service.IAppStoreEmployeeRelationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.HashMap;

@RestController
@RequestMapping(value = "/padLogin")
@PropertySource("classpath:/wxOpenConfig.properties")
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);
    @Resource
    private IAppEmployeeService iAppEmployeeService;

    @Resource
    private IAppStoreEmployeeRelationService iAppStoreEmployeeRelationService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RestTemplate restTemplate;

    @Value("${wx.open.appid}")
    private String appid;
    @Value("${wx.open.appsecret}")
    private String appsecret;

    @RequestMapping(value="/jscode2session/{storeId}/{code}", method = org.springframework.web.bind.annotation.RequestMethod.GET)
    public CommonResult<?> jscode2session (@PathVariable  String storeId,@PathVariable  String code, HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {

        logger.info("pad entry into jscode2session ");

        // We'll create the response object later
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid="+appid+"&secret="+appsecret+"&code="+code+"&grant_type=authorization_code";
        String responseStr = restTemplate.getForObject(url, String.class);

        try {
            JsonNode jsonObject = objectMapper.readTree(responseStr);
            logger.info("jsonObject: {}",jsonObject);
            if(jsonObject.has("errcode")){
                Integer errcode = jsonObject.get("errcode").asInt();
                logger.info("用户登录失败：{}",errcode);
                return CommonResult.failed(1000,errcode+":"+jsonObject.get("errmsg").asText());
            }
                String openid = jsonObject.get("openid").asText();
                String unionid = jsonObject.get("unionid").asText();
                logger.info("用户登录openid："+openid+",unionid："+unionid);
                AppEmployee appEmployee = iAppEmployeeService.getAppEmployeeByOpenId(storeId,openid);
                if(appEmployee == null ){
                    return CommonResult.failed("请先登记");
                }
                AppStoreEmployeeRelation relation = iAppStoreEmployeeRelationService.getStoreEmployeeRelation(appEmployee.getEmployeeId(),storeId);
                if(relation == null ){
                    return CommonResult.failed("您不是这家的店员");
                }
                // objectMapper.read(appEmployee, AppEmployee.class);
                // JSONObject jsonObject1 = (JSONObject)JSONObject.toJSON(appEmployee);
                HashMap<String, Object> appEmployeeMap = objectMapper.convertValue(appEmployee, HashMap.class);
                                // Generate JWT token directly
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_MONTH, 365);
                String token = JWT.create()
                        .withHeader(new HashMap<>())
                        .withClaim("unionid", unionid)
                        .withClaim("openid", openid)
                        .withExpiresAt(calendar.getTime())
                        .sign(Algorithm.HMAC384("com.relle-mall.JWT"));
                appEmployeeMap.put("token", token);
                appEmployeeMap.put("validTime", 780);
                return CommonResult.succeeded(appEmployeeMap);
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("认证服务器连接失败");
            return CommonResult.failed("认证服务器连接失败");
        }

    }
}
