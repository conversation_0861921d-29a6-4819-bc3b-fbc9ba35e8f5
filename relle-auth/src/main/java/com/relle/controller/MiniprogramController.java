package com.relle.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.common.api.CommonResult;

import com.relle.service.IAppCustomerInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

/**
 ** 微信小程序交互
 */

@RestController
@RequestMapping("/miniprogram")
@PropertySource("classpath:/miniprogram.properties")
public class MiniprogramController {
    private static final Logger logger = LoggerFactory.getLogger(MiniprogramController.class);

    @Autowired
    private Environment env;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;

    @RequestMapping(value = "/jscode2session/{miniProgramType}/{code}", method = org.springframework.web.bind.annotation.RequestMethod.GET)
    public CommonResult<?> jscode2session(@PathVariable String miniProgramType, @PathVariable String code,
            HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {

        logger.info("entry into jscode2session ");
        // 智能导览小程序
        String appid = env.getProperty("miniProgram." + miniProgramType + ".appid");
        String secret = env.getProperty("miniProgram." + miniProgramType + ".secret");

        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret + "&js_code="
                + code + "&grant_type=authorization_code";
        String responseStr = restTemplate.getForObject(url, String.class);
        logger.info("responseStr: {}", responseStr);
        try {
            JsonNode jsonObject = objectMapper.readTree(responseStr);
            logger.info("jsonObject: {}", jsonObject);
            if (jsonObject.has("errcode")) {
                Integer errcode = jsonObject.get("errcode").asInt();
                logger.info("小程序认证失败：{}", errcode);
                return CommonResult.failed(1000, errcode + ":" + jsonObject.get("errmsg").asText());
            }
            HashMap<String, Object> retunObj = new HashMap<>();
            String unionid = jsonObject.get("unionid").asText();
            String openid = jsonObject.get("openid").asText();
            int isNew = iAppCustomerInfoService.save(unionid, openid);
            retunObj.put("unionid", unionid);
            retunObj.put("openid", openid);
            retunObj.put("isNew", isNew);
            // Generate JWT token directly
            String token = JWT.create()
                    .withHeader(new HashMap<>())
                    .withClaim("unionid", unionid)
                    .withClaim("openid", openid)
                    .withExpiresAt(getExpirationDate())
                    .sign(Algorithm.HMAC384("com.relle-mall.JWT"));
            retunObj.put("token", token);
            logger.info("小程序认证成功：{}", objectMapper.writeValueAsString(retunObj));
            return CommonResult.succeeded(retunObj);

        } catch (Exception e) {
            logger.info("小程序认证服务器连接失败");
            return CommonResult.failed("小程序认证服务器连接失败");
        }
    }

    @RequestMapping(value = "/verifyToken/{token}", method = org.springframework.web.bind.annotation.RequestMethod.GET)
    public CommonResult<?> verifyToken(@PathVariable String token, HttpServletRequest req, HttpServletResponse res) {
        try {
            // Verify token directly
            JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384("com.relle-mall.JWT")).build();
            // Parse the token - if this doesn't throw an exception, the token is valid
            jwtVerifier.verify(token);
            // Token is valid
            return CommonResult.succeeded(0, "token正常");
        } catch (SignatureVerificationException e) {
            // Invalid signature
            return CommonResult.succeeded(-1, "非法token");
        } catch (TokenExpiredException e) {
            // Token expired
            return CommonResult.succeeded(1, "token已失效");
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * Get expiration date for JWT token (1 year from now)
     */
    private Date getExpirationDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 365);
        return calendar.getTime();
    }

    public Environment getEnv() {
        return env;
    }

    public void setEnv(Environment env) {
        this.env = env;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        // 指定token过期时间为10秒
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 365);

        String token = JWT.create()
                .withHeader(new HashMap<>()) // Header
                .withClaim("unionid", "ojqzL0y-kslTWHDe7XGSKgo88Tc8") // Payload
                .withClaim("openid", "o4GyE5OAhXrvMCbcwsgNs_PlnHRo")
                .withExpiresAt(calendar.getTime()) // 过期时间
                .sign(Algorithm.HMAC384("com.relle-mall.JWT")); // 签名用的secret
        // eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6ImJhb2JhbyIsImV4cCI6MTY2OTQ1MzE0NCwidXNlcklkIjoyMX0.fFUYCtnXrgNItx6hoJd9mxGFr778xwftNkjBiTcxzSw
        System.out.println(token);

        // String token =
        // "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.eyJ1bmlvbmlkIjoib2pxekwweS1rc2xUV0hEZTdYR1NLZ284OFRjOCIsIm9wZW5pZCI6Im80R3lFNUZrT2I2QnQtLVU4aXRmcl9BbDlUTTQiLCJleHAiOjE2OTU0MzkzMTZ9.VPeb845XXbwb0wU_fAJ62BAzW2-o8PMhPBgGAT488EniIQoX-G3V1zOXwYIt5yjn";
        // String token =
        // "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6ImJhb2JhbyIsImV4cCI6MTY2OTQ1NTc0MSwidXNlcklkIjoyMX0.VMGtwfCPpAFSpGf-vyEUsCj9kOXLIaTCIuLv592Zwvk";

        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC384("com.relle-mall.JWT")).build();
        // 解析指定的token
        DecodedJWT decodedJWT = jwtVerifier.verify(token);
        // 获取解析后的token中的payload信息
        Claim userId = decodedJWT.getClaim("unionid");
        Claim userName = decodedJWT.getClaim("openid");
        System.out.println(userId.asString());
        System.out.println(userName.asString());
        // 输出超时时间
        System.out.println(decodedJWT.getExpiresAt());

    }
}
