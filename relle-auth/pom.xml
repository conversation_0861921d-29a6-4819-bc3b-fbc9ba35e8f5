<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>relle-auth</artifactId>
    <packaging>war</packaging>
    <name>Relle Auth Module</name>
    <description>Authentication module for Relle Application</description>

    <parent>
        <groupId>com.relle</groupId>
        <artifactId>relle-parent</artifactId>
        <version>0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <properties>
        <!-- JaCoCo thresholds. Increase gradually as you add tests. -->
        <jacoco.unit-tests.limit.instruction-ratio>0%</jacoco.unit-tests.limit.instruction-ratio>
        <jacoco.unit-tests.limit.branch-ratio>0%</jacoco.unit-tests.limit.branch-ratio>
        <jacoco.unit-tests.limit.class-complexity>20</jacoco.unit-tests.limit.class-complexity>
        <jacoco.unit-tests.limit.method-complexity>5</jacoco.unit-tests.limit.method-complexity>
    </properties>

    <dependencies>
        <!-- ========== Internal Module Dependencies ========== -->
        <dependency>
            <groupId>com.relle</groupId>
            <artifactId>relle-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.relle</groupId>
            <artifactId>relle-core</artifactId>
        </dependency>

        <!-- ========== Spring Boot Dependencies ========== -->
        <!-- NOTE: Parent POM already includes spring-boot-starter-web, spring-boot-starter-aop -->
        <!-- These starters transitively include ALL Spring dependencies we need: -->
        <!-- - spring-boot, spring-boot-autoconfigure -->
        <!-- - spring-web, spring-context, spring-beans, spring-core -->
        <!-- - javax.annotation-api, servlet-api -->
        <!-- NO NEED TO DECLARE INDIVIDUAL SPRING DEPENDENCIES! -->

        <!-- ========== Security Dependencies ========== -->
        <!-- JWT - used in controllers for authentication token generation and verification -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <!-- ========== Build-time Dependencies ========== -->
        <!-- MyBatis Generator - only needed for code generation, not runtime -->
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- ========== Removed Unused Dependencies ========== -->
        <!-- druid-spring-boot-starter - Not used in current code (no database configuration) -->
        <!-- mysql-connector-java - Not used in current code (no direct database operations) -->
        <!-- tk.mybatis:mapper - Not used in current code -->
        <!-- httpclient - Not used (HttpRequestUtil comes from relle-common) -->
        <!-- javaee-api - Not needed (specific annotations come from Spring Boot starters) -->
    </dependencies>

    <build>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>com.github.ngeor</groupId>
                        <artifactId>checkstyle-rules</artifactId>
                        <version>4.9.3</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <configLocation>com/github/ngeor/checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <skip>${skipTests}</skip>
                </configuration>
                <executions>
                    <execution>
                        <?m2e ignore?>
                        <id>checkstyle</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
        </plugins>
    </reporting>
</project>