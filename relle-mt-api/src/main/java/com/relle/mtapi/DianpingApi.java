package com.relle.mtapi;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.service.IMtLogService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Component
@PropertySource("classpath:/mtConfig_${spring.profiles.active}.properties")
public class DianpingApi {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ObjectMapper objectMapper;

    @Value("${mt.appid}")
    private String appid;
    @Value("${mt.appsecret}")
    private String appsecret;
    @Value("${mt.app_shop_account}")
    private String account;
    @Value("${mt.app_shop_accountname}")
    private String accountname;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppsecret() {
        return appsecret;
    }

    public void setAppsecret(String appsecret) {
        this.appsecret = appsecret;
    }

    @Resource
    private IMtLogService iMtLogService;

    public JsonNode getToken(String auth_code, String redirect_url) {
        String url = "https://openapi.dianping.com/router/oauth/token";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("app_secret", appsecret);
        params.put("auth_code", auth_code);
        params.put("grant_type", "authorization_code");
        params.put("redirect_url", redirect_url);
        System.out.println(params);
        // String response = HttpRequestUtil.httpPost(url, params);
        return request(url, params);
        // if (JSONObject.isValidObject(response)) {
        // return JSONObject.parseObject(response);
        // } else {
        // return null;
        // }
    }

    private JsonNode request(String url, Map<String, String> params) {
        String response = restTemplate.getForObject(url, String.class, params);
        try {
            return objectMapper.readTree(response);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public JsonNode refreshSession() {
        String url = "https://openapi.dianping.com/router/oauth/token";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("app_secret", appsecret);
        params.put("grant_type", "refresh_token");
        params.put("refresh_token", SessionCache.getInstance().getRefreshSession());
        return request(url, params);
    }

    public JsonNode receiptPrepare(String receiptCode, String oepnShopUuid) {
        String url = "https://openapi.dianping.com/router/tuangou/receipt/prepare";
        String signMethod = "MD5";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("session", SessionCache.getInstance().getSession());
        params.put("format", "json");
        params.put("v", "1");

        params.put("receipt_code", receiptCode);
        params.put("open_shop_uuid", oepnShopUuid);

        params.put("sign_method", signMethod);
        params.put("sign", SignUtil.generateSign(params, appsecret, signMethod));
        return request(url, params);
    }

    public JsonNode receiptConsume(String receiptCode, String oepnShopUuid) {
        String url = "https://openapi.dianping.com/router/tuangou/receipt/consume";
        String signMethod = "MD5";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("session", SessionCache.getInstance().getSession());
        params.put("format", "json");
        params.put("v", "1");

        params.put("requestid", String.valueOf(System.currentTimeMillis()));
        params.put("receipt_code", receiptCode);
        params.put("count", "1");
        params.put("open_shop_uuid", oepnShopUuid);
        params.put("app_shop_account", account);
        params.put("app_shop_accountname", accountname);

        params.put("sign_method", signMethod);

        params.put("sign", SignUtil.generateSign(params, appsecret, signMethod));

        System.out.println(params);
        return request(url, params);
    }

    public JsonNode receiptGetconsumed(String receiptCode, String oepnShopUuid) {
        String url = "https://openapi.dianping.com/router/tuangou/receipt/getconsumed";
        String signMethod = "MD5";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("session", SessionCache.getInstance().getSession());
        params.put("format", "json");
        params.put("v", "1");

        params.put("receipt_code", receiptCode);
        params.put("open_shop_uuid", oepnShopUuid);

        params.put("sign_method", signMethod);

        params.put("sign", SignUtil.generateSign(params, appsecret, signMethod));

        System.out.println(params);
        return request(url, params);
    }

    public JsonNode reverseconsume(String receiptCode, String oepnShopUuid, long dealId) {
        String url = "https://openapi.dianping.com/router/tuangou/receipt/reverseconsume";
        String signMethod = "MD5";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("session", SessionCache.getInstance().getSession());
        params.put("format", "json");
        params.put("v", "1");

        params.put("receipt_code", receiptCode);
        params.put("open_shop_uuid", oepnShopUuid);
        params.put("app_deal_id", String.valueOf(dealId));
        params.put("app_shop_account", account);
        params.put("app_shop_accountname", accountname);

        params.put("sign_method", signMethod);

        params.put("sign", SignUtil.generateSign(params, appsecret, signMethod));

        System.out.println(params);
        return request(url, params);
    }

    public JsonNode queryshopdeal(String open_shop_uuid) {
        String url = "https://openapi.dianping.com/tuangou/deal/queryshopdeal";
        String signMethod = "MD5";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("session", SessionCache.getInstance().getSession());
        params.put("format", "json");
        params.put("v", "1");

        params.put("open_shop_uuid", open_shop_uuid);

        params.put("sign_method", signMethod);

        params.put("sign", SignUtil.generateSign(params, appsecret, signMethod));

        System.out.println(params);
        return request(url, params);
    }

    public JsonNode queryproduct(String open_shop_uuid) {
        String url = "https://openapi.dianping.com/router/tuangou/product/queryproduct";
        String signMethod = "MD5";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("session", SessionCache.getInstance().getSession());
        params.put("format", "json");
        params.put("v", "1");

        params.put("open_shop_uuid", open_shop_uuid);

        params.put("sign_method", signMethod);

        params.put("sign", SignUtil.generateSign(params, appsecret, signMethod));

        System.out.println(params);
        return request(url, params);
    }

    public JsonNode queryproductbytype(String open_shop_uuid, Long type) {
        String url = "https://openapi.dianping.com/router/tuangou/product/queryproductbytype";
        String signMethod = "MD5";
        Map<String, String> params = new HashMap<>();
        params.put("app_key", appid);
        params.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("session", SessionCache.getInstance().getSession());
        params.put("format", "json");
        params.put("v", "1");

        params.put("open_shop_uuid", open_shop_uuid);
        params.put("type", String.valueOf(type));

        params.put("sign_method", signMethod);

        params.put("sign", SignUtil.generateSign(params, appsecret, signMethod));

        System.out.println(params);
        return request(url, params);
    }

    public static void main(String[] args) {

    }
}