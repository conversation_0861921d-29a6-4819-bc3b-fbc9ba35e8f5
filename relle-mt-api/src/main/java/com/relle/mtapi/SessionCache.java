package com.relle.mtapi;

/**
 * Singleton class to cache Meituan API session information
 */
public class SessionCache {
    private static SessionCache instance;
    private String session;
    private String refreshSession;

    private SessionCache() {
        // Private constructor to enforce singleton pattern
    }

    /**
     * Get the singleton instance
     * @return SessionCache instance
     */
    public static synchronized SessionCache getInstance() {
        if (instance == null) {
            instance = new SessionCache();
        }
        return instance;
    }

    /**
     * Get the current session token
     * @return Session token
     */
    public String getSession() {
        return session;
    }

    /**
     * Set the session token
     * @param session Session token
     */
    public void setSession(String session) {
        this.session = session;
    }

    /**
     * Get the refresh session token
     * @return Refresh session token
     */
    public String getRefreshSession() {
        return refreshSession;
    }

    /**
     * Set the refresh session token
     * @param refreshSession Refresh session token
     */
    public void setRefreshSession(String refreshSession) {
        this.refreshSession = refreshSession;
    }
}
