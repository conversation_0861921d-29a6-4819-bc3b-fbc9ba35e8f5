package com.relle.init;


import com.relle.mbg.model.MtSession;
import com.relle.mtapi.SessionCache;
import com.relle.service.IMtSessionService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Order(value = 1)
public class ApplicationInit implements CommandLineRunner {

    @Resource
    private IMtSessionService iMtSessionService;


    @Override
    public void run(String... arg0)  {
        //获取美团session
        MtSession session = iMtSessionService.get();
        SessionCache.getInstance().setSession(session.getAccessToken());
        SessionCache.getInstance().setRefreshSession(session.getRefreshToken());
    }
}
