package com.relle.task;

import com.fasterxml.jackson.databind.JsonNode;
import com.relle.service.impl.DianpingApiImpl;
import com.relle.mbg.model.MtSession;
import com.relle.mtapi.SessionCache;
import com.relle.service.IMtSessionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.util.Date;

@Profile({ "pro" })
@Configuration // 1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling // 2.开启定时任务
public class RefreshSessionTask {
    private static final Logger logger = LoggerFactory.getLogger(RefreshSessionTask.class);

    @Resource
    private IMtSessionService iMtSessionService;
    @Resource
    private DianpingApiImpl dianpingApi;

    private long fixTime = 24 * 60 * 60 * 1000L;

    // 3.添加定时任务
    @Scheduled(cron = "0 5 0 * * ?")
    private void configureTasks() {
        logger.info("_____session刷新任务start_____");
        MtSession session = iMtSessionService.get();
        Date now = new Date();
        if (session.getExpiresIn().getTime() - now.getTime() < fixTime) {
            logger.info("_____此次需要刷新session_____");
            JsonNode responseObj = dianpingApi.refreshSession();
            if (responseObj.get("code").asInt() == 200) {
                MtSession mtSession = iMtSessionService.get();
                mtSession.setAccessToken(responseObj.get("access_token").asText());
                mtSession.setExpiresIn(new Date(new Date().getTime() + responseObj.get("expires_in").asLong() * 1000));
                mtSession.setRefreshToken(responseObj.get("refresh_token").asText());
                mtSession.setScope(responseObj.get("scope").asText());
                mtSession.setRemainRefreshCount(responseObj.get("remain_refresh_count").asInt());
                mtSession.setBid(responseObj.get("bid").asText());
                mtSession.setTokentype(responseObj.get("tokenType").asText());
                iMtSessionService.update(mtSession);
                SessionCache.getInstance().setSession(mtSession.getAccessToken());
                SessionCache.getInstance().setRefreshSession(mtSession.getRefreshToken());
            }
        }
        logger.info("_____session刷新任务end_____");
    }
}