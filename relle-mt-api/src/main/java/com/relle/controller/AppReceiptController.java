package com.relle.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.common.api.CommonResult;
import com.relle.service.impl.DianpingApiImpl;
import com.relle.dto.ReceiptRequest;
import com.relle.mbg.model.MtDealService;
import com.relle.mbg.model.MtStore;
import com.relle.service.IMtCodeToCouponService;
import com.relle.service.IMtDealService;
import com.relle.service.IMtStoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "券处理接口")
@RestController
@RequestMapping(value = "/receipt")
public class AppReceiptController {
    private static final Logger logger = LoggerFactory.getLogger(AppReceiptController.class);
    @Resource
    private DianpingApiImpl dianpingApi;
    @Resource
    private IMtStoreService iMtStoreService;
    @Resource
    private IMtDealService iMtDealService;
    @Resource
    private IMtCodeToCouponService iMtCodeToCouponService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ObjectMapper objectMapper;

    @ApiOperation(value = "查询已核验的券信息")
    @PostMapping(value = "/query")
    public CommonResult<?> list(@RequestBody ReceiptRequest params, HttpServletRequest req, HttpServletResponse res) {
        String storeId = params.getStoreId();
        MtStore mtStoreByStoreId = iMtStoreService.getMtStoreByStoreId(storeId);
        if (mtStoreByStoreId == null) {
            return CommonResult.failed(storeId + "未配置");
        }

        String code = params.getCode();
        JsonNode object = dianpingApi.receiptGetconsumed(code, mtStoreByStoreId.getOpenShopUuid());
        return CommonResult.succeeded(object);
    }

    @ApiOperation(value = "查询未核销的券所对应的服务")
    @PostMapping(value = "/prepare")
    public CommonResult<?> prepare(@RequestBody ReceiptRequest params, HttpServletRequest req,
            HttpServletResponse res) {
        String storeId = params.getStoreId();
        String unionid = (String) req.getSession().getAttribute("unionid");
        MtStore mtStoreByStoreId = iMtStoreService.getMtStoreByStoreId(storeId);
        if (mtStoreByStoreId == null) {
            return CommonResult.failed(storeId + "未配置");
        }

        String code = params.getCode();
        JsonNode object = dianpingApi.receiptPrepare(code, mtStoreByStoreId.getOpenShopUuid());
        if (object == null) {

            return CommonResult.failed("券无效");
        } else {
            if (object.get("code").asInt() == 200) {
                ObjectNode data = (ObjectNode) object.get("data");
                int biz_type = data.get("biz_type").asInt();
                Long dealId = 0L;
                if (biz_type == 0) { // 0为普通团购
                    dealId = data.get("deal_id").asLong();
                    System.out.println("普通团购:" + dealId + "," + unionid);
                } else if (biz_type == 205) {
                    dealId = data.get("product_item_id").asLong();
                    System.out.println("次卡:" + dealId + "," + unionid);
                } else {
                    return CommonResult.failed("暂不支持此类商品兑换");
                }
                String serviceItemId = iMtDealService.getServiceItemIdByDeal(storeId, dealId);
                if (serviceItemId == null) {
                    return CommonResult.failed("暂不支持该商品兑换");
                }
                Map<String, Object> ret = new HashMap<>();
                ret.put("serviceItemId", serviceItemId);
                ret.put("couponId", "mock_coupon_" + dealId);
                ret.put("title", "Mock Deal " + dealId);
                return CommonResult.succeeded(ret);
            } else {
                return CommonResult.failed(object.get("code").asInt(), object.get("msg").asText());
            }
        }

    }

    @ApiOperation(value = "核销券")
    @PostMapping(value = "/consume")
    public CommonResult<?> consume(@RequestBody ReceiptRequest params, HttpServletRequest req,
            HttpServletResponse res) {
        List<JsonNode> list = new ArrayList<>();
        String storeId = params.getStoreId();
        String unionid = (String) req.getSession().getAttribute("unionid");
        MtStore mtStoreByStoreId = iMtStoreService.getMtStoreByStoreId(storeId);
        if (mtStoreByStoreId == null) {
            return CommonResult.failed(storeId + "未配置");
        }
        String code = params.getCode();
        JsonNode prepareObject = dianpingApi.receiptPrepare(code, mtStoreByStoreId.getOpenShopUuid());
        if (prepareObject == null) {
            return CommonResult.failed(2000, "核销失败,请核实券的状态");
        } else {

            if (prepareObject.get("code").asInt() == 200) {
                ObjectNode prepareData = (ObjectNode) prepareObject.get("data");
                int biz_type = prepareData.get("biz_type").asInt();
                Long dealId = 0L;
                if (biz_type == 0) { // 0为普通团购
                    dealId = prepareData.get("deal_id").asLong();
                    System.out.println("普通团购:" + dealId + "," + unionid);
                } else if (biz_type == 205) {
                    dealId = prepareData.get("product_item_id").asLong();
                    System.out.println("次卡:" + dealId + "," + unionid);
                } else {
                    return CommonResult.failed("暂不支持此类商品兑换");
                }
                String serviceItemId = iMtDealService.getServiceItemIdByDeal(storeId, dealId);
                if (serviceItemId == null) {
                    return CommonResult.failed("暂不支持该商品兑换");
                }
                JsonNode object = dianpingApi.receiptConsume(code, mtStoreByStoreId.getOpenShopUuid());
                if (object == null) {
                    return CommonResult.failed(3000, "核销失败");
                } else {

                    if (object.get("code").asInt() == 200) {
                        String url = "https://" + req.getServerName()
                                + "/relle-om-0.1/coupon/distribute/" + "mock_coupon_" + dealId + "/" + unionid
                                + "?storeIds=" + storeId;
                        String token = req.getHeader("Authorization");
                        // Map<String, String> param = new HashMap<>();

                        // String distributeResponse = HttpRequestUtil.httpPost(url, param, token);
                        // method.addHeader("Authorization", "Bearer " + token);
                        HttpHeaders headers = new HttpHeaders();
                        String bearerToken = "Bearer " + token;
                        headers.set("Authorization", bearerToken);
                        headers.set("Content-Type", "application/json");

                        // 请求体（可以是 String、对象等）
                        // String requestBody = "{\"key\":\"value\"}";

                        // 将请求头和请求体封装到 HttpEntity 中
                        HttpEntity<String> entity = new HttpEntity<>(null, headers);

                        // 发送请求并获取响应
                        ResponseEntity<String> distributeResponse = restTemplate.exchange(
                                "https://api.example.com/resource",
                                HttpMethod.POST,
                                entity,
                                String.class);
                        String response = distributeResponse.getBody();
                        try {
                            JsonNode jsonObject = objectMapper.readTree(response);
                            if (jsonObject.has("errcode")) {
                                Integer errcode = jsonObject.get("errcode").asInt();
                                return CommonResult.failed(1000, errcode + ":" + jsonObject.get("errmsg").asText());
                            }
                            if (jsonObject.get("status").asBoolean()) {
                                ObjectNode couponObj = (ObjectNode) jsonObject.get("data").get("coupon");
                                ObjectNode couponCheckoutObj = (ObjectNode) jsonObject.get("data")
                                        .get("couponCheckout");
                                couponCheckoutObj.put("serviceItemId", serviceItemId);
                                couponCheckoutObj.put("couponName", couponObj.get("couponName").asText());

                                iMtCodeToCouponService.save(storeId, String.valueOf(dealId), code, unionid,
                                        "mock_coupon_" + dealId, serviceItemId,
                                        couponCheckoutObj.get("id").asLong());
                                return CommonResult.succeeded(couponCheckoutObj, "卡券已发放");
                            }
                            dianpingApi.reverseconsume(code, mtStoreByStoreId.getOpenShopUuid(), dealId);
                            return CommonResult.failed(8000, "卡券兑换失败");
                        } catch (Exception e) {
                            dianpingApi.reverseconsume(code, mtStoreByStoreId.getOpenShopUuid(), dealId);
                            return CommonResult.failed(8000, "卡券兑换失败");
                        }
                        // if (JSONObject.isValidObject(response)) {
                        // JSONObject obj = JSONObject.parseObject(response);
                        // if (obj.getBooleanValue("status")) {
                        // JSONObject couponObj = obj.getJSONObject("data").getJSONObject("coupon");
                        // JSONObject couponCheckoutObj =
                        // obj.getJSONObject("data").getJSONObject("couponCheckout");
                        // couponCheckoutObj.put("serviceItemId",mtDealService.getServiceItem());
                        // couponCheckoutObj.put("couponName",couponObj.getString("couponName"));

                        // iMtCodeToCouponService.save(storeId,String.valueOf(dealId),code,unionid,mtDealService.getCouponId(),mtDealService.getServiceItem(),couponCheckoutObj.getLongValue("id"));
                        // return CommonResult.succeeded(couponCheckoutObj,"卡券已发放");
                        // }
                        // }

                    } else {

                        return CommonResult.failed(4000, object.get("msg").asText());
                    }
                }
            } else {
                return CommonResult.failed(2000, prepareObject.get("msg").asText());
            }
        }
    }

    @ApiOperation(value = "撤销已核销券")
    @PostMapping(value = "/reverseconsume")
    public CommonResult<?> reverseconsume(@RequestBody ReceiptRequest params, HttpServletRequest req,
            HttpServletResponse res) {
        MtStore mtStoreByStoreId = iMtStoreService.getMtStoreByStoreId(params.getStoreId());
        JsonNode obj = dianpingApi.reverseconsume(params.getCode(), mtStoreByStoreId.getOpenShopUuid(),
                params.getDealId());
        return CommonResult.succeeded(obj);
    }
}