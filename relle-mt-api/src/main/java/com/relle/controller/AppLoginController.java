package com.relle.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.relle.common.api.CommonResult;
import com.relle.service.impl.DianpingApiImpl;
import com.relle.mbg.model.MtSession;
import com.relle.service.IMtSessionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Date;

@RestController
@RequestMapping(value = "/login")
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);

    @Autowired
    private IMtSessionService iMtSessionService;


    @Resource
    private DianpingApiImpl dianpingApi;

    @RequestMapping(value="/oauth_token")
    public CommonResult<?> oauth_token  (String auth_code, HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {

        logger.info("mt entry into oauth_token "+auth_code);
        String basePath =  "https://" + req.getServerName()
                + req.getContextPath() + "/login/oauth_token";
        JsonNode responseObj = dianpingApi.getToken(auth_code,basePath);

        if(responseObj.get("code").asInt()==200){
            MtSession session = new MtSession();
            session.setAccessToken(responseObj.get("access_token").asText());
            session.setExpiresIn(new Date(new Date().getTime()+responseObj.get("expires_in").asLong()*1000));
            session.setRefreshToken(responseObj.get("refresh_token").asText());
            session.setScope(responseObj.get("scope").asText());
            session.setRemainRefreshCount(responseObj.get("remain_refresh_count").asInt());
            session.setBid(responseObj.get("bid").asText());
            session.setTokentype(responseObj.get("tokenType").asText());
            iMtSessionService.save(session);
            CommonResult.succeeded(session);
        }

        return null;
    }


}
