import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.RelleApplication;
import com.relle.mtapi.DianpingApi;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {RelleApplication.class})
public class DaoTest {
    @Resource
    private DianpingApi dianpingApi;
    @Before
    public void before() {
        System.out.println("单元测试开始");
    }

    @Test
    public void testDaoCanUse() throws JsonProcessingException {
        //JSONObject queryshopdeal = dianpingApi.queryproductbytype("fbcbfdcf73c6d4a457fabc38251c9750",1081L);
        JsonNode queryshopdeal = dianpingApi.queryshopdeal("fbcbfdcf73c6d4a457fabc38251c9750");
        System.out.println(queryshopdeal);
        ObjectNode array = (ObjectNode) queryshopdeal.get("data");
        for (int i = 0; i < array.size(); i++) {
            JsonNode obj = array.get(i);
            System.out.println(obj);
            //System.out.println(obj.getString("deal_id")+","+obj.getString("title")+","+obj.getString("price"));
        }
        //System.out.println(queryshopdeal);
    }

    @After
    public void after() {
        System.out.println("单元测试结束");
    }
}
