

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.RelleApplication;
import com.relle.mbg.model.MtSession;
import com.relle.mtapi.DianpingApi;
import com.relle.mtapi.SessionCache;
import com.relle.service.IMtSessionService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {RelleApplication.class})
public class DianpingApiTest {
    @Resource
    private DianpingApi dianpingApi;

    private RestTemplate restTemplate;
    private ObjectMapper objectMapper;

    @Before
    public void before() {
        System.out.println("单元测试开始");
        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory();
        this.restTemplate = new RestTemplate(factory);
        this.objectMapper = new ObjectMapper();
    }
    @Test
    public void testGetToken(){
        JsonNode token = dianpingApi.getToken("5f587d8d4b259f5fae2a31dcd942bb2dbf6c7f8b", "https://relle.douwifi.cn/test.do");
        System.out.println(token);
    }
    @Test
    public void testDianpingApi() {
        //JSONObject queryshopdeal = dianpingApi.queryproductbytype("fbcbfdcf73c6d4a457fabc38251c9750",1081L);
        JsonNode queryshopdeal = dianpingApi.queryshopdeal("fbcbfdcf73c6d4a457fabc38251c9750");

        JsonNode array = queryshopdeal.get("data");
        for (int i = 0; i < array.size(); i++) {
            JsonNode obj = array.get(i);
            System.out.println(obj);
            //System.out.println(obj.getString("deal_id")+","+obj.getString("title")+","+obj.getString("price"));
        }
        //System.out.println(queryshopdeal);
    }

    @Test
    public void testReceiptDianpingApi() {
        String code = "**********";
        String storeId = "SH0002";
        String dealId = "**********";
        JsonNode prepareObj = prepare(code, storeId);
        System.out.println(prepareObj);
        assert prepareObj!=null&&prepareObj.get("status").asBoolean();

        JsonNode consumeObj = consume(code, storeId);
        System.out.println(consumeObj);
        assert consumeObj!=null&&consumeObj.get("status").asBoolean();

        JsonNode reverseconsumeObj = reverseconsume(code, storeId,dealId); //测试次卡产品id
        System.out.println(reverseconsumeObj);
        assert reverseconsumeObj!=null&&reverseconsumeObj.get("status").asBoolean();
        //System.out.println(queryshopdeal);
    }

    private JsonNode prepare(String code,String storeId){
        // 设置请求Header
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.eyJ1bmlvbmlkIjoib2pxekwweS1rc2xUV0hEZTdYR1NLZ284OFRjOCIsIm9wZW5pZCI6Im80R3lFNU9BaFhydk1DYmN3c2dOc19QbG5IUm8iLCJleHAiOjE3MzIxNzA3OTd9.7D3xwyRBk_R-zhq3RRdrtx0jhdeFwJALjJ1l-ubdWa__b0aBMAJODJfXpL6496lx");

        // 设置请求参数
        Map<String, String> map = new HashMap<>();
        map.put("code", code);
        map.put("storeId", storeId);

// 创建请求实体
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(map, headers);
        // 发送POST请求并获取响应结果
        String url = "https://supec.douwifi.cn/mt-api/receipt/prepare";
        ResponseEntity<String> responseEntity  = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        String result = responseEntity.getBody();
        try {
            return objectMapper.readTree(result);
        } catch (Exception e) {
              return null;
        }
      
    }

    private JsonNode consume(String code,String storeId){
        // 设置请求Header
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.eyJ1bmlvbmlkIjoib2pxekwweS1rc2xUV0hEZTdYR1NLZ284OFRjOCIsIm9wZW5pZCI6Im80R3lFNU9BaFhydk1DYmN3c2dOc19QbG5IUm8iLCJleHAiOjE3MzIxNzA3OTd9.7D3xwyRBk_R-zhq3RRdrtx0jhdeFwJALjJ1l-ubdWa__b0aBMAJODJfXpL6496lx");

        // 设置请求参数
        Map<String, String> map = new HashMap<>();
        map.put("code", code);
        map.put("storeId", storeId);

// 创建请求实体
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(map, headers);
        // 发送POST请求并获取响应结果
        String url = "https://supec.douwifi.cn/mt-api/receipt/consume";
        ResponseEntity<String> responseEntity  = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        String result = responseEntity.getBody();
        try {
            return objectMapper.readTree(result);
        } catch (Exception e) {
              return null;
        }
    }

    private JsonNode reverseconsume(String code,String storeId,String dealId){
        // 设置请求Header
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.eyJ1bmlvbmlkIjoib2pxekwweS1rc2xUV0hEZTdYR1NLZ284OFRjOCIsIm9wZW5pZCI6Im80R3lFNU9BaFhydk1DYmN3c2dOc19QbG5IUm8iLCJleHAiOjE3MzIxNzA3OTd9.7D3xwyRBk_R-zhq3RRdrtx0jhdeFwJALjJ1l-ubdWa__b0aBMAJODJfXpL6496lx");

        // 设置请求参数
        Map<String, String> map = new HashMap<>();
        map.put("code", code);
        map.put("storeId", storeId);
        map.put("dealId", dealId);

// 创建请求实体
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(map, headers);
        // 发送POST请求并获取响应结果
        String url = "https://supec.douwifi.cn/mt-api/receipt/reverseconsume";
        ResponseEntity<String> responseEntity  = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        String result = responseEntity.getBody();
        try {
            return objectMapper.readTree(result);
        } catch (Exception e) {
              return null;
        }
    }


    @Resource
    private IMtSessionService iMtSessionService;

    private long fixTime = 24*60*60*1000L;
    //3.添加定时任务
    private void configureTasks() {
        MtSession session = iMtSessionService.get();
        Date now = new Date();
        if (session.getExpiresIn().getTime() - now.getTime() < fixTime) {
            JsonNode responseObj = dianpingApi.refreshSession();
            if (responseObj.get("code").asInt() == 200) {
                MtSession mtSession = iMtSessionService.get();
                mtSession.setAccessToken(responseObj.get("access_token").asText());
                mtSession.setExpiresIn(new Date(new Date().getTime() + responseObj.get("expires_in").asLong() * 1000));
                mtSession.setRefreshToken(responseObj.get("refresh_token").asText());
                mtSession.setScope(responseObj.get("scope").asText());
                mtSession.setRemainRefreshCount(responseObj.get("remain_refresh_count").asInt());
                mtSession.setBid(responseObj.get("bid").asText());
                mtSession.setTokentype(responseObj.get("tokenType").asText());
                iMtSessionService.update(mtSession);
                SessionCache.getInstance().setSession(mtSession.getAccessToken());
                SessionCache.getInstance().setRefreshSession(mtSession.getRefreshToken());
            }
        }
    }

    @After
    public void after() {
        System.out.println("单元测试结束");
    }
}
