<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>relle-cm</artifactId>
    <packaging>war</packaging>
    <name>relle-cm</name>
    <description>Customer Management module for Relle Application</description>

    <parent>
        <groupId>com.relle</groupId>
        <artifactId>relle-parent</artifactId>
        <version>0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <properties>
        <!-- JaCoCo thresholds. Increase gradually as you add tests. -->
        <jacoco.unit-tests.limit.instruction-ratio>0%</jacoco.unit-tests.limit.instruction-ratio>
        <jacoco.unit-tests.limit.branch-ratio>0%</jacoco.unit-tests.limit.branch-ratio>
        <jacoco.unit-tests.limit.class-complexity>20</jacoco.unit-tests.limit.class-complexity>
        <jacoco.unit-tests.limit.method-complexity>5</jacoco.unit-tests.limit.method-complexity>
    </properties>

    <dependencies>
        <!-- ========== Internal Modules ========== -->
        <!-- Common Module - Used for CommonResult, AccessTokenUtil, JWTUtil, DateTimeUtil, SendComplexEmail -->
        <dependency>
            <groupId>com.relle</groupId>
            <artifactId>relle-common</artifactId>
            <version>0.1</version>
        </dependency>

        <!-- Core Module - Used for MyBatis entities and mappers -->
        <dependency>
            <groupId>com.relle</groupId>
            <artifactId>relle-core</artifactId>
            <version>0.1</version>
        </dependency>

        <!-- ========== Authentication & Security ========== -->
        <!-- JWT - Used in TokenInterceptor for token validation -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <!-- ========== JSON Processing ========== -->
        <!-- FastJSON - Used extensively in controllers and services -->
        <!-- <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency> -->

        <!-- ========== HTTP Utilities ========== -->
        <!-- Hutool - Used for HTTP utilities in WxController -->
        <!-- <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency> -->

        <!-- ========== Utilities ========== -->
        <!-- Commons Collections - Used extensively for CollectionUtils -->
        <!-- <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency> -->

        <!-- ========== API Documentation (Development/Build-time) ========== -->
        <!-- SpringFox Swagger2 - Used in Swagger2Config for API documentation -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Swagger UI - Development/build-time only -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Swagger Annotations - Used in Swagger2Config -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.20</version>
            <scope>provided</scope>
        </dependency>

        <!-- ========== Build-time Dependencies ========== -->
        <!-- MyBatis Generator - Build-time only -->
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- ========== Seldom Used Dependencies (Commented for Future Use) ========== -->
        <!--
        Database Dependencies - Currently not used as this module doesn't perform direct DB operations
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper</artifactId>
        </dependency>
        -->

        <!--
        MyBatis Direct Dependencies - Currently using generated mappers only
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.4.6</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>1.3.2</version>
        </dependency>
        -->

        <!--
        HTTP Client - Currently using Hutool's HttpUtil instead
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        -->

        <!--
        WeChat Pay - Not currently implemented
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-apache-httpclient</artifactId>
            <version>0.4.8</version>
        </dependency>
        -->

        <!--
        Email Dependencies - Using SendComplexEmail from relle-common instead
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>1.1.1</version>
        </dependency>
        -->

        <!--
        Additional Utilities - Not currently used
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>29.0-jre</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
        </dependency>
        -->
    </dependencies>

    <build>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.6.3</version>
                                </requireMavenVersion>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>${checkstyle.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.github.ngeor</groupId>
                        <artifactId>checkstyle-rules</artifactId>
                        <version>4.9.3</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <configLocation>com/github/ngeor/checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <skip>${skipTests}</skip>
                </configuration>
                <executions>
                    <execution>
                        <?m2e ignore?>
                        <id>checkstyle</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
            </plugin>
        </plugins>
    </reporting>
</project>
