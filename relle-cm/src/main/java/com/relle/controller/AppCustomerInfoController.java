package com.relle.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.service.IAppCustomerInfoService;
import com.relle.service.IAppWhiteListService;
// TODO: Re-enable when complex services are consolidated
// import com.relle.service.IMyFeedbackService;
// import com.relle.service.IMyServiceOperationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/customer")
@EnableAsync
public class AppCustomerInfoController {
    private static final Logger logger = LoggerFactory.getLogger(AppCustomerInfoController.class);

    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;
    // TODO: Re-enable when complex services are consolidated
    // @Resource
    // private IMyServiceOperationService iMyServiceOperationService;
    // @Resource
    // private IMyFeedbackService iMyFeedbackService;
    @Resource
    private IAppWhiteListService iAppWhiteListService;
    @Resource
    ObjectMapper objectMapper;


    @PostMapping(value="/update")
    public CommonResult<?> update (@RequestBody AppCustomerInfo info, HttpServletRequest req) throws JsonProcessingException  {
        logger.info("AppCustomerInfoController update 接收参数:"+ objectMapper.writeValueAsString(info));

        return CommonResult.succeeded(iAppCustomerInfoService.update(info));
    }

    @RequestMapping(value="/get/{unionid}")
    public CommonResult<?> get (@PathVariable String unionid, HttpServletRequest req)  {
        logger.info("AppCustomerInfoController get 接收参数:" + unionid);

        AppCustomerInfo info = iAppCustomerInfoService.get(unionid);
        if(info==null){
            return CommonResult.failed("此用户不存在");
        } else {
            return CommonResult.succeeded(info);
        }

    }
    // TODO: Re-enable when complex services are consolidated
    /*
    @RequestMapping(value="/getOperation/{unionid}")
    public CommonResult<?> getOperation (@PathVariable String unionid,HttpServletRequest req)  {
        return CommonResult.succeeded(iMyServiceOperationService.getOrderList(unionid));
    }

    @RequestMapping(value="/getFeedbackList/{unionid}")
    public CommonResult<?> getFeedbackList (@PathVariable String unionid,HttpServletRequest req)  {
        return CommonResult.succeeded(iMyFeedbackService.getMyFeedbackList(unionid));
    }

    @RequestMapping(value="/feedback/{suborderId}")
    public CommonResult<?> feedback (@PathVariable String suborderId,HttpServletRequest req)  {
        return CommonResult.succeeded(iMyFeedbackService.feedback(suborderId));
    }
    */

    @RequestMapping(value="/getWhiteListInfo/{unionid}")
    public CommonResult<?> getWhiteListInfo (@PathVariable String unionid,HttpServletRequest req)  {
        return CommonResult.succeeded(iAppWhiteListService.getWhiteListByUnionid(unionid));
    }

    // TODO: Re-enable when complex services are consolidated
    /*
    @RequestMapping(value="/sendReport")
    public CommonResult<?> sendReport (@RequestBody Map<String,Object> map,HttpServletRequest req)  {
        String operationRecordId = (String)map.get("operationRecordId");
        String email = (String)map.get("email");
        return iMyServiceOperationService.sendReport(operationRecordId,email);
    }
    */


}
