package com.relle.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.common.api.CommonResult;
import com.relle.common.utils.AccessTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("/wx")
@PropertySource("classpath:/miniprogram.properties")
public class WxController {
    @Autowired
    private Environment env;
    @Resource
    ObjectMapper objectMapper;
    @Resource
    RestTemplate restTemplate;
    /**
     * 小程序通过code换取用户手机号。 每个 code 只能使用一次，code的有效期为5min
     * @param code
     * @return
     */
    @RequestMapping(value="/getuserphonenumber", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public CommonResult<?> getuserphonenumber (HttpServletRequest req, HttpServletResponse res,
                                               @RequestParam(value = "code", required = true) String code){
        String miniProgramType = "relle_mall";
        String appid = env.getProperty("miniProgram."+miniProgramType+".appid");
        String secret = env.getProperty("miniProgram."+miniProgramType+".secret");

        String accessToken = AccessTokenUtil.getAccessToken(appid, secret);
        HashMap<String, String> params = new HashMap<>();
        params.put("code", code);
        String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;
        String responseStr = restTemplate.getForObject(url, String.class, params);
        // String responseStr = HttpUtil.post(url, params.toString());
//      String responseStr= "{"errcode":0,"errmsg":"ok","phone_info":{"phoneNumber":"***********","purePhoneNumber":"***********","countryCode":"86","watermark":{"timestamp":1669778651,"appid":"wx7868e71b9b547f08"}}}";
        // JSONObject retunObj = new JSONObject();
        try {       

            ObjectNode jsonObject = (ObjectNode) objectMapper.readTree(responseStr);
            if(jsonObject.has("errcode")){
                Integer errcode = jsonObject.get("errcode").asInt();
                return CommonResult.failed(1000,errcode+":"+jsonObject.get("errmsg").asText());
            }
            ObjectNode phoneInfo = (ObjectNode) jsonObject.get("phone_info");
            phoneInfo.remove("watermark");
            return CommonResult.succeeded(phoneInfo);
        }catch (Exception e){
            // e.printStackTrace();
            // retunObj.put("errcode", -1);
            // retunObj.put("errmsg", "接口调用异常:"+e.getMessage());
            return CommonResult.failed("接口调用异常");
        }
    }
}