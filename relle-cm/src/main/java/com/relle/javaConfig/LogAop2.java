package com.relle.javaConfig;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * 统一日志处理切面
 * Created by sj
 */
@Aspect
@Component
public class LogAop2 {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogAop2.class);


    @Pointcut("execution(public * com.relle.controller.*.*(..))")
    public void webLog() {
    }

   /* @Around("webLog()")
    public Object handleControllerMethod(ProceedingJoinPoint pjp) throws Throwable {    //pjp是一个 包含拦截方法信息的对象

        System.out.println("time aspect start");

        //参数数组
        Object[] args = pjp.getArgs();

        for (Object arg:args){
            System.out.println("arg is :" +arg);
        }

        long start = System.currentTimeMillis();

        //调用被拦截的方法
        Object object = pjp.proceed();

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String openid = (String)request.getSession().getAttribute("openid");

        LOGGER.info("操作人:{}",openid);

        return object;

    }*/

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) {
        // 接收到请求，记录请求内容

        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();
        String methodParams = Arrays.toString(joinPoint.getArgs());

        //获取当前请求对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String urlStr = request.getRequestURL().toString();

        String openid = (String)request.getSession().getAttribute("openid");
        LOGGER.info("{},{},{}",openid,urlStr, methodParams);
    }


    @AfterReturning(returning = "data", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object data) {
        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() ;
        LOGGER.info("{} return: {}", methodName , data);
    }

}



