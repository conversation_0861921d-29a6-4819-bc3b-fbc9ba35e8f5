<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>relle-background</artifactId>
    <packaging>jar</packaging>
    <name>relle-background</name>
    <description>Background module for Relle Application</description>

    <parent>
        <groupId>com.relle</groupId>
        <artifactId>relle-parent</artifactId>
        <version>0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <properties>
        <!-- JaCoCo thresholds. Increase gradually as you add tests. -->
        <jacoco.unit-tests.limit.instruction-ratio>0%</jacoco.unit-tests.limit.instruction-ratio>
        <jacoco.unit-tests.limit.branch-ratio>0%</jacoco.unit-tests.limit.branch-ratio>
        <jacoco.unit-tests.limit.class-complexity>20</jacoco.unit-tests.limit.class-complexity>
        <jacoco.unit-tests.limit.method-complexity>5</jacoco.unit-tests.limit.method-complexity>
    </properties>

    <dependencies>
        <!-- ========== Internal Modules ========== -->
        <!-- Common Module - Used for CommonResult, DateTimeUtil, HttpRequestUtil, etc. -->
        <dependency>
            <groupId>com.relle</groupId>
            <artifactId>relle-common</artifactId>
            <version>0.1</version>
        </dependency>

        <!-- Core Module - Used for MyBatis models and mappers -->
        <dependency>
            <groupId>com.relle</groupId>
            <artifactId>relle-core</artifactId>
            <version>0.1</version>
        </dependency>

        <!-- ========== Spring Boot Starters ========== -->
        <!-- Validation - Used for @Valid, @Constraint, etc. -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- MySQL Driver - Used for database connections -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- ========== Authentication & Security ========== -->
        <!-- JWT - Used in AppLoginController for token generation -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>

        <!-- ========== Workflow Engine ========== -->
        <!-- LiteFlow - Used in component classes for workflow processing -->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <version>2.8.5</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ========== PDF Generation ========== -->
        <!-- iText PDF - Used for PDF generation in services -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

        <!-- ========== API Documentation ========== -->
        <!-- SpringDoc OpenAPI - Primary API documentation -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.6.14</version>
            <scope>provided</scope>
        </dependency>

        <!-- SpringFox Swagger2 - Legacy Swagger support for existing controllers -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- ========== Build-time Dependencies ========== -->
        <!-- MyBatis Generator - Build-time only -->
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.6.3</version>
                                </requireMavenVersion>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>${checkstyle.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.github.ngeor</groupId>
                        <artifactId>checkstyle-rules</artifactId>
                        <version>4.9.3</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <configLocation>com/github/ngeor/checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <skip>${skipTests}</skip>
                </configuration>
                <executions>
                    <execution>
                        <?m2e ignore?>
                        <id>checkstyle</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
            </plugin>
        </plugins>
    </reporting>
</project>
