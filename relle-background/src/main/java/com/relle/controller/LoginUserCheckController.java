package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.EmployeeDetailVO;
import com.relle.mbg.model.AppEmployee;
import com.relle.mbg.model.AppStoreEmployeeRelation;
import com.relle.mbg.model.AppStoreInfo;
import com.relle.service.IAppEmployeeService;
import com.relle.service.StoreInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.OptionalInt;
import java.util.stream.Collectors;

@Tag(name = "获取登录用户信息", description = "获取当前登录用户的详细信息")
@RestController
@RequestMapping(value = "/user/login/check")
public class LoginUserCheckController {
    @Resource
    private IAppEmployeeService iAppEmployeeService;
    @Resource
    private StoreInfoService storeInfoService;

    @Operation(summary = "获取后台管理用户", description = "获取当前登录的后台管理用户信息")
    @PostMapping(value="/backend")
    public CommonResult<?> backend (HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        String openid = (String)request.getSession().getAttribute("openid");
        AppEmployee employee = iAppEmployeeService.getAppEmployeeByOpenId(unionid);
        if(employee == null){
            employee = iAppEmployeeService.getAppEmployeeByOpenId(openid);
        }
        if(employee == null){
            return CommonResult.failed("无权访问，请联系管理员开通！");
        }

        EmployeeDetailVO vo = new EmployeeDetailVO();
        BeanUtils.copyProperties(employee,vo);
        vo.setUnionid(employee.getEmployeeOpenId());
        List<AppStoreEmployeeRelation> relationList = iAppEmployeeService.getEmployeeRelationByEmployeeId(employee.getEmployeeId());
        OptionalInt maxOpt = relationList.stream()
                .mapToInt(AppStoreEmployeeRelation::getRole)
                .max();
        int roleCode = 1;
        if (maxOpt.isPresent()) {
            roleCode = maxOpt.getAsInt();
        }
        vo.setRoleCode(roleCode);

        List<AppStoreInfo> storeList = relationList.stream()
                .filter(relation -> !"all".equals(relation.getStoreId()))
                .map(relation ->
                        storeInfoService.getByStoreId(relation.getStoreId())).collect(Collectors.toList());
        vo.setStoreList(storeList);

        return CommonResult.succeeded(vo);
    }
    /*@RequestMapping(value="/padAdmin/{storeId}")
    public CommonResult<?> padAdmin (@PathVariable String storeId, HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        String openid = (String)request.getSession().getAttribute("openid");
        AppEmployee employee = iAppEmployeeService.getAppEmployeeByOpenId(unionid);
        if(employee == null){
            employee = iAppEmployeeService.getAppEmployeeByOpenId(openid);
        }
        if(employee == null){
            return CommonResult.failed("无权访问，请联系管理员开通！");
        }

        if()

        EmployeeDetailVO vo = new EmployeeDetailVO();
        BeanUtils.copyProperties(employee,vo);
        vo.setUnionid(employee.getEmployeeOpenId());
        List<AppStoreEmployeeRelation> relationList = iAppEmployeeService.getEmployeeRelationByEmployeeId(employee.getEmployeeId());
        OptionalInt maxOpt = relationList.stream()
                .mapToInt(AppStoreEmployeeRelation::getRole)
                .max();
        int roleCode = 1;
        if (maxOpt.isPresent()) {
            roleCode = maxOpt.getAsInt();
        }
        vo.setRoleCode(roleCode);

        List<AppStoreInfo> storeList = relationList.stream()
                .filter(relation -> "all".equals(relation.getStoreId()))
                .map(relation ->
                        storeInfoService.getByStoreId(relation.getStoreId())).collect(Collectors.toList());
        vo.setStoreList(storeList);

        return CommonResult.succeeded(vo);

    }

    @RequestMapping(value="/pad")
    public CommonResult<?> pad (HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {
        logger.info("pad-backend entry into jscode2session,code: {}",code);

        JSONObject returnObj = new JSONObject();

        String responseStr = HttpRequestUtil.httpGet("https://api.weixin.qq.com/sns/oauth2/access_token?appid="+appid+"&secret="+appsecret+"&code="+code+"&grant_type=authorization_code");
        logger.info("getToken: {}",responseStr);
        if(JSONObject.isValidObject(responseStr)){ //请求正常返回
            JSONObject jsonObject = JSON.parseObject(responseStr);
            if(!jsonObject.containsKey("errcode")){

                String openid = jsonObject.getString("openid");
                String unionid = jsonObject.getString("unionid");

                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("openid",openid);
                jsonObject1.put("unionid",unionid);
                if("login".equals(operateType)) {
                    jsonObject1.put("token", jwtUtil.getToken(unionid, openid, 780));
                    jsonObject1.put("validTime", 780);
                }
                return CommonResult.succeeded(jsonObject1);
            } else {
                Integer errcode = jsonObject.getInteger("errcode");
                logger.info("用户登录失败：{}",errcode);
                return CommonResult.failed(1000,errcode+":"+jsonObject.getString("errmsg"));
            }
        } else {
            logger.info("认证服务器连接失败");
            return CommonResult.failed("认证服务器连接失败");
        }

    }*/


}
