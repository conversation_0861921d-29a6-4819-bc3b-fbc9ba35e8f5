package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.AnalysisDTO;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RestController
@RequestMapping(value = "/analysis")
public class AnalysisController {
    @Resource
    private FlowExecutor flowExecutor;
    @RequestMapping(value = "/show/{storeId}/{queryDateStr}", method = org.springframework.web.bind.annotation.RequestMethod.GET)
    public CommonResult<?> show(@PathVariable String storeId, @PathVariable String queryDateStr, HttpServletRequest request) throws ParseException {
        //获取用户信息
        String openid = (String)request.getSession().getAttribute("openid");
        //参数验证
        LocalDate queryDate = null;
        try{
            queryDate = LocalDate.parse(queryDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }catch (Exception e){
            queryDate = LocalDate.now();
        }
        //参数组装
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("storeId",storeId);
        paramMap.put("date",queryDate);
        paramMap.put("openid",openid);

        //LiteFlow，流程管理,具体流程参阅listAllOrder
        AnalysisDTO dto = new AnalysisDTO();
        LiteflowResponse response = flowExecutor.execute2Resp("count", paramMap, dto);
        dto = response.getFirstContextBean();
        if (response.isSuccess()){
            return CommonResult.succeeded(dto);
        }else{
            return CommonResult.failed();
        }
    }
}
