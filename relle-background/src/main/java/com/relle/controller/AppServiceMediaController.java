package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppServiceMedia;
import com.relle.service.ServiceMediaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Api(tags = "产品图片上传接口")
@RestController
@RequestMapping(value = "/service/media")
public class AppServiceMediaController {
    private static final Logger logger = LoggerFactory.getLogger(AppAvailableTimeController.class);
    @Resource
    private ServiceMediaService mediaService;

    @ApiOperation(value = "上传")
    @PostMapping(value = "/upload/{section}")
    public CommonResult<?> upload(@PathVariable String section,
                                  MultipartFile file,
                                  HttpServletRequest request) throws IOException {
        String md5 = DigestUtils.md5DigestAsHex(file.getInputStream());
        AppServiceMedia old_media = mediaService.getMediaByMd5(md5);
        if(old_media!=null){
            return  CommonResult.succeeded(old_media);
        }
        String openid = (String)request.getSession().getAttribute("openid");
        return CommonResult.succeeded(mediaService.saveMedia(md5,file,section,openid));
    }


}
