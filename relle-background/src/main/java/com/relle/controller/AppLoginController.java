package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.service.IAppEmployeeService;
import com.relle.service.IAppStoreEmployeeRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

@Tag(name = "扫码登录接口", description = "微信扫码登录相关接口")
@RestController
@RequestMapping(value = "/login")
@PropertySource("classpath:/wxOpenConfig_${spring.profiles.active}.properties")
/* @ConfigurationProperties(prefix = "wx.open") */
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);
    @Resource
    private IAppEmployeeService iAppEmployeeService;

    @Resource
    private IAppStoreEmployeeRelationService iAppStoreEmployeeRelationService;

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ObjectMapper objectMapper;

    // Using JWT directly instead of JWTUtil

    @Value("${wx.open.appid}")
    private String appid;
    @Value("${wx.open.appsecret}")
    private String appsecret;
    @Value("#{'${wx.open.manager}'.split(',')}")
    private List<String> manager;

    @Operation(summary = "用code换取用户信息", description = "通过微信授权code获取用户信息")
    @GetMapping("/jscode2session/{operateType}/{code}")
    public CommonResult<?> jscode2session(
            @Parameter(description = "操作类型，login表示登录") @PathVariable String operateType,
            @Parameter(description = "微信授权code") @PathVariable String code,
            HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {
        logger.info("pad-backend entry into jscode2session,code: {}", code);

        String requestUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appid + "&secret=" + appsecret
                + "&code=" + code + "&grant_type=authorization_code";
        String responseStr = restTemplate.getForObject(requestUrl, String.class);
        logger.info("getToken: {}", responseStr);

        try {
            JsonNode jsonObject = objectMapper.readTree(responseStr);
            if (jsonObject.has("errcode")) {
                Integer errcode = jsonObject.get("errcode").asInt();
                logger.info("用户登录失败：{}", errcode);
                return CommonResult.failed(1000, errcode + ":" + jsonObject.get("errmsg").asText());
            }
            String openid = jsonObject.get("openid").asText();
            String unionid = jsonObject.get("unionid").asText();
            logger.info("用户登录openid：" + openid + ",unionid：" + unionid);
            ObjectNode resultJsonNode = objectMapper.createObjectNode();
            resultJsonNode.put("openid", openid);
            resultJsonNode.put("unionid", unionid);
            if ("login".equals(operateType)) {
                // Create JWT token directly
                String token = JWT.create()
                        .withClaim("unionid", unionid)
                        .withClaim("openid", openid)
                        .sign(Algorithm.HMAC384("com.relle-mall.JWT"));
                resultJsonNode.put("token", token);
                resultJsonNode.put("validTime", 780);
            }
            return CommonResult.succeeded(resultJsonNode);
        } catch (Exception e) {
            logger.info("认证服务器连接失败");
            return CommonResult.failed("认证服务器连接失败");
        }
    }
}
