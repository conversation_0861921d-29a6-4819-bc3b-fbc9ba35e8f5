package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.EmployeeDTO;
import com.relle.dto.EmployeeDetailDTO;
import com.relle.dto.EmployeeDetailVO;
import com.relle.dto.EmployeeVO;
import com.relle.mbg.model.AppEmployee;
import com.relle.mbg.model.AppStoreEmployeeRelation;
import com.relle.mbg.model.AppStoreInfo;
import com.relle.service.IAppEmployeeService;
import com.relle.service.StoreInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.OptionalInt;
import java.util.stream.Collectors;

@Api(tags = "员工管理")
@RestController
@RequestMapping(value = "/manage/employee")
public class EmployeeManageController {
    @Resource
    private IAppEmployeeService employeeService;
    @Resource
    private StoreInfoService storeInfoService;

    @ApiOperation(value = "员工列表")
    @PostMapping(value = "/list")
    public CommonResult<?> list (@RequestBody EmployeeDTO query) {
        List<AppEmployee> employeeList = employeeService.getAll(query);
        List<EmployeeVO> resultList = employeeList.stream()
                .filter(employee -> employee.getDeleted() == 0)
                .map(employee -> {
                    EmployeeVO vo = new EmployeeVO();
                    BeanUtils.copyProperties(employee, vo);
                    List<AppStoreEmployeeRelation> relationList = employeeService.getEmployeeRelationByEmployeeId(employee.getEmployeeId());
                    OptionalInt maxOpt = relationList.stream()
                            .mapToInt(AppStoreEmployeeRelation::getRole)
                            .max();
                    int roleCode = 1;
                    if (maxOpt.isPresent()) {
                        roleCode = maxOpt.getAsInt();
                    }
                    vo.setRoleCode(roleCode);
                    return vo;
                })
                .sorted(Comparator.comparing(EmployeeVO::getId).reversed())
                .collect(Collectors.toList());

        return CommonResult.succeeded(resultList);
    }

    @ApiOperation(value = "新增员工")
    @PostMapping(value = "/add")
    public CommonResult<?> add (@RequestBody @Valid EmployeeDetailDTO detailDTO,
                                HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppEmployee employee = employeeService.getAppEmployeeByOpenId(detailDTO.getUnionid());

        if(employee != null){
            return CommonResult.failed("员工已存在");
        }
        //保存员工信息
        AppEmployee appEmployee = convertEmployee(detailDTO);
        appEmployee.setCreateBy(unionid);
        appEmployee.setUpdateBy(unionid);
        employeeService.insert(appEmployee);
        //保存门店与员工关系
        List<AppStoreInfo> storeList = detailDTO.getStoreList();
        if(!CollectionUtils.isEmpty(storeList)) {
            storeList.forEach(store -> {
                employeeService.insertRalation(appEmployee.getEmployeeId(), store.getStoreId(), detailDTO.getRoleCode());
            });
        } else {
            if(detailDTO.getRoleCode()==64) { //管理员，而其他美容师和店长都需要配置门店
                employeeService.insertRalation(appEmployee.getEmployeeId(), "all", detailDTO.getRoleCode());
            }
        }
        return CommonResult.succeeded(appEmployee);
    }

    @ApiOperation(value = "修改员工")
    @PostMapping(value = "/modify")
    public CommonResult<?> modify (@RequestBody @Valid EmployeeDetailDTO detailDTO,
                                   HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppEmployee employee = employeeService.getAppEmployeeById(detailDTO.getId());
        if(employee == null){
            return CommonResult.failed("员工不存在");
        }
        //保存员工信息
        AppEmployee appEmployee = convertEmployee(detailDTO);
        appEmployee.setUpdateBy(unionid);
        employeeService.update(appEmployee);
        //保存门店与员工关系
        List<AppStoreInfo> storeList = detailDTO.getStoreList();

        employeeService.deleteRalationByEmployeeId(employee.getEmployeeId());
        if(!CollectionUtils.isEmpty(storeList)) {
            storeList.forEach(store -> {
                employeeService.insertRalation(employee.getEmployeeId(), store.getStoreId(), detailDTO.getRoleCode());
            });
        } else {
            if(detailDTO.getRoleCode()==64) { //管理员，而其他美容师和店长都需要配置门店
                employeeService.insertRalation(employee.getEmployeeId(), "all", detailDTO.getRoleCode());
            }
        }

        return CommonResult.succeeded(appEmployee);
    }

    @ApiOperation(value = "获取员工详情")
    @PostMapping(value = "/detail/{id}")
    public CommonResult<?> detail (@PathVariable Long id) {
        AppEmployee employee = employeeService.getAppEmployeeById(id);
        if(employee == null){
            return CommonResult.failed("员工不存在");
        }

        EmployeeDetailVO vo = new EmployeeDetailVO();
        BeanUtils.copyProperties(employee,vo);
        vo.setUnionid(employee.getEmployeeOpenId());

        List<AppStoreEmployeeRelation> relationList = employeeService.getEmployeeRelationByEmployeeId(employee.getEmployeeId());
        OptionalInt maxOpt = relationList.stream()
                .mapToInt(AppStoreEmployeeRelation::getRole)
                .max();
        int roleCode = 1;
        if (maxOpt.isPresent()) {
            roleCode = maxOpt.getAsInt();
        }
        vo.setRoleCode(roleCode);

        List<AppStoreInfo> storeList = relationList.stream()
                .filter(relation -> !"all".equals(relation.getStoreId()))
                .map(relation ->
                storeInfoService.getByStoreId(relation.getStoreId()))
                .collect(Collectors.toList());
        vo.setStoreList(storeList);

        return CommonResult.succeeded(vo);
    }

    @ApiOperation(value = "员工离职")
    @PostMapping(value = "/delete/{id}")
    public CommonResult<?> delete (@PathVariable Long id,
                                   HttpServletRequest request) {
        String unionid = (String)request.getSession().getAttribute("unionid");

        AppEmployee employee = employeeService.getAppEmployeeById(id);
        if(employee == null){
            return CommonResult.failed("员工不存在");
        }
        if(employee.getDeleted() == 1){
            return CommonResult.failed("员工已离职");
        }
        employee.setUpdateBy(unionid);
        employeeService.update(employee);
        return CommonResult.succeeded(employee);
    }

    private AppEmployee convertEmployee(EmployeeDetailDTO detailDTO){
        AppEmployee employee = new AppEmployee();
        employee.setId(detailDTO.getId());
        employee.setEmployeeName(detailDTO.getEmployeeName());
        employee.setEmployeePhone(detailDTO.getEmployeePhone());
        employee.setEmployeeOpenId(detailDTO.getUnionid());
        // employee.setRemark(detailDTO.getRemark());
        return employee;
    }


}
