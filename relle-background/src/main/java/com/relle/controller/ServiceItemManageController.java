package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppServiceItemVO;
import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppStoreInfo;
import com.relle.service.IAppServiceItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务产品管理接口
 */
@Tag(name = "服务产品管理接口", description = "服务产品的增删改查接口")
@RestController
@RequestMapping(value = "/service/item")
public class ServiceItemManageController {
    @Resource
    private IAppServiceItemService serviceItemService;

    @Operation(summary = "服务项列表", description = "获取所有服务项列表")
    @PostMapping(value = "/list")
    public CommonResult<List<AppServiceItemVO>> list(){
        return CommonResult.succeeded(serviceItemService.listAllItemByCategory((byte) 1));
    }

    @Operation(summary = "服务项详情", description = "根据ID获取服务项详细信息")
    @PostMapping(value = "/detail/{serviceItemId}")
    public CommonResult<AppServiceItemVO> detail(@Parameter(description = "服务项ID") @PathVariable String serviceItemId){
        AppServiceItem item = serviceItemService.getItem(serviceItemId);
        if(item == null){
            return CommonResult.failed("服务项不存在");
        }
        if(!item.getServiceId().startsWith("S")){
            return CommonResult.failed("非服务项编号");
        }
        return CommonResult.succeeded(serviceItemService.getItemVO(serviceItemId));
    }
}
