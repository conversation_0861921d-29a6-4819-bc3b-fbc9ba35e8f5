package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.AccountReconciliationOrderVO;
import com.relle.dto.OrderQueryDTO;
import com.relle.service.IAppServiceOrderService;
import com.relle.service.ServiceBundleOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 对账订单，由于查询逻辑不同，独立出来
 */
@Api(tags = "对账订单", description = "对账订单，由于查询逻辑不同，独立出来")
@RestController
@RequestMapping(value = "/account/reconciliation")
public class AccountReconciliationController {
   @Resource
   private IAppServiceOrderService orderService;
   @Resource
   private ServiceBundleOrderService bundleOrderService;

   @ApiOperation(value = "订单列表", notes = "获取对账订单列表")
   @PostMapping(value = "/order/list")
   public CommonResult<?> orderList (@ApiParam(value = "订单查询参数") @RequestBody @Valid OrderQueryDTO query) {
      List<AccountReconciliationOrderVO> orderListOfAccountReconciliation = new ArrayList<>();
      orderListOfAccountReconciliation.addAll(orderService.getOrderListOfAccountReconciliation(query));
      orderListOfAccountReconciliation.addAll(bundleOrderService.getOrderListOfAccountReconciliation(query));

      orderListOfAccountReconciliation.sort(Comparator.comparing(AccountReconciliationOrderVO::getCreateDateTime));

      return CommonResult.succeeded(orderListOfAccountReconciliation);
   }
}
