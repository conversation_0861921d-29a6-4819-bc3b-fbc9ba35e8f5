package com.relle.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.common.api.CommonResult;
import com.relle.dto.OrderDTO;
import com.relle.dto.OrderDao;
import com.relle.dto.OrderQueryDTO;
import com.relle.dto.SkinDetectorTestVO;
import com.relle.mbg.model.*;
import com.relle.service.*;
import com.relle.enums.ServiceOrderStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.text.ParseException;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * pad端服务订单类
 */
@Api(tags = "订单服务接口")
@RestController
@RequestMapping(value = "/orderAdmin")
public class AppServiceOrderController {
    private static final Logger logger = LoggerFactory.getLogger(AppServiceOrderController.class);
    @Resource
    private IAppServiceOrderService iAppServiceOrderService;
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;
    @Resource
    private IAppServiceOperationRecordService iAppServiceOperationRecordService;

    @Resource
    private ISkinDetectorTestService iSkinDetectorTestService;
    @Resource
    private IAppCustomerTestLogService iAppCustomerTestLogService;
    @Resource
    private ObjectMapper objectMapper;

    @RequestMapping(value = "/listFinished/{storeId}/{queryDate}")
    public CommonResult<?> listFinished(@PathVariable String storeId, @PathVariable String queryDate,
            HttpServletRequest request, HttpServletResponse response) throws ParseException {
        // 获取用户信息
        String openid = (String) request.getSession().getAttribute("openid");

        logger.info("listFinished storeId: {}, user: {}", storeId, openid);
        return CommonResult.succeeded(iAppServiceOrderService.getFinishedOrder(storeId, openid, queryDate));
    }

    @ApiOperation(value = "查询订单")
    @PostMapping(value = "/order/list")
    public CommonResult<?> list(@RequestBody @Valid OrderQueryDTO query, HttpServletRequest request)
            throws ParseException, JsonProcessingException {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        logger.info("query list: user: {} , params:{}", unionid, objectMapper.writeValueAsString(query));

        List<OrderDTO> finishedOrder = iAppServiceOrderService.getFinishedOrder(query.getStoreId(),
                query.getStartDate(),
                query.getEndDate(),
                ServiceOrderStatusEnum.FINISHED.getCode());

        finishedOrder = finishedOrder.stream()
                .filter(order -> query.getCustomerId() == null || query.getCustomerId().equals(order.getCustomerId()))
                .filter(order -> query.getOrderId() == null || query.getOrderId().equals(order.getOrderId()))
                .collect(Collectors.toList());

        return CommonResult.succeeded(finishedOrder);
    }

    @ApiOperation(value = "根据子订单号获取当日客户皮肤检测")
    @GetMapping(value = "/report/getCustomerTestRecord/{suborderId}")
    public CommonResult<?> getCustomerTestRecord(@PathVariable String suborderId, HttpServletRequest request,
            HttpServletResponse response) throws ParseException {
        // 从订单里获取客户信息
        AppServiceSuborder suborder = iAppServiceOrderService.getSuborder(suborderId);
        String customerUnionid = suborder.getUnionid();
        AppCustomerInfo customerInfo = iAppCustomerInfoService.get(customerUnionid);

        // 根据操作记录，获取操作时间
        AppServiceOperationRecord operationRecord = iAppServiceOperationRecordService.getOperationRecord(suborderId);
        // Date serviceStarttime = operationRecord.getServiceStarttime();

        // 根据客户信息和操作时间进行处理
        List<SkinDetectorTestVO> mediaFromClientData = iSkinDetectorTestService.getMediaFromClientData(customerInfo,
                operationRecord);

        return CommonResult.succeeded(mediaFromClientData);
    }

    @PostMapping(value = "/report/save")
    public CommonResult<?> reportSave(@RequestBody @Valid OrderDao dao, HttpServletRequest request,
            HttpServletResponse response) throws Throwable {
        // 获取用户信息
        String openid = (String) request.getSession().getAttribute("openid");
        logger.info("reportSave params:{}", objectMapper.writeValueAsString(dao));
        dao.setCreateBy(openid);
        return CommonResult.succeeded(iAppServiceOrderService.uploadReport(dao));
    }

    @ApiOperation(value = "新上传报告")
    @PostMapping(value = "/report/newSave")
    public CommonResult<?> reportNewSave(@RequestBody @Valid OrderDao dao, HttpServletRequest request,
            HttpServletResponse response) throws IOException {
        // 获取用户信息
        String openid = (String) request.getSession().getAttribute("openid");
        logger.info("reportSave params:{}", objectMapper.writeValueAsString(dao));
        dao.setCreateBy(openid);
        return CommonResult.succeeded(iAppServiceOrderService.uploadReport(dao));
    }

    @PostMapping(value = "/report/createPDF/{operationRecordId}")
    public CommonResult<?> createPDF(@PathVariable String operationRecordId, HttpServletRequest request,
            HttpServletResponse response) {
        int result = iAppServiceOrderService.createPDF(operationRecordId);
        if (result == 1) {
            return CommonResult.succeeded(result);
        } else {
            return CommonResult.failed("生成失败");
        }
    }

    @ApiOperation(value = "获取报告地址")
    @PostMapping(value = "/report/getPDF/{operationRecordId}")
    public CommonResult<?> getPDF(@PathVariable String operationRecordId, HttpServletRequest request,
            HttpServletResponse response) {
        AppCustomerTestLog appCustomerTestLog = iAppCustomerTestLogService.getByOperationId(operationRecordId);
        return CommonResult.succeeded(appCustomerTestLog);
    }

}
