package com.relle.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.common.api.CommonResult;
import com.relle.dto.OrderDTO;
import com.relle.dto.OrderFollowupDTO;
import com.relle.dto.OrderQueryDTO;
import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.mbg.model.*;
import com.relle.service.IAppServiceOrderService;
import com.relle.service.OrderFollowupService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.ParseException;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 随访接口
 */
@Api(tags = "随访接口")
@RestController
@RequestMapping(value = "/followup")
public class FollowUpController {
    private static final Logger logger = LoggerFactory.getLogger(FollowUpController.class);
    @Resource
    private IAppServiceOrderService iAppServiceOrderService;
    @Resource
    private OrderFollowupService followupService;
    @Resource
    ObjectMapper objectMapper;

    @ApiOperation(value = "查询订单")
    @PostMapping(value = "/order/list")
    public CommonResult<?> orderList(@RequestBody @Valid OrderQueryDTO query, HttpServletRequest request)
            throws ParseException, JsonProcessingException {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        logger.info("query list: user: {} , params:{}", unionid, objectMapper.writeValueAsString(query));

        List<OrderDTO> finishedOrder = iAppServiceOrderService.getFinishedOrder(query.getStoreId(),
                query.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                query.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                ServiceOrderStatusEnum.FINISHED.getCode());

        finishedOrder = finishedOrder.stream()
                .filter(order -> query.getCustomerId() == null || query.getCustomerId().equals(order.getCustomerId()))
                .filter(order -> query.getOrderId() == null || query.getOrderId().equals(order.getOrderId()))
                .collect(Collectors.toList());

        finishedOrder.forEach(orderDTO -> {
            List<OrderFollowup> followupList = followupService
                    .findByServiceOperationId(orderDTO.getOperationRecord().getServiceOperationId());
            if (!CollectionUtils.isEmpty(followupList)) {
                orderDTO.setServiceStatus(ServiceOrderStatusEnum.FOLLOWUPED.getCode());
            }
        });
        return CommonResult.succeeded(finishedOrder);
    }

    @ApiOperation(value = "获取随访记录")
    @PostMapping(value = "/list/{orderId}/{serviceOperationId}")
    public CommonResult<?> list(@PathVariable String orderId,
            @PathVariable String serviceOperationId) throws ParseException {
        return CommonResult.succeeded(followupService.findByServiceOperationId(serviceOperationId));
    }

    @ApiOperation(value = "保存随访记录")
    @PostMapping(value = "/save")
    public CommonResult<?> save(@RequestBody @Valid OrderFollowupDTO followupDTO, HttpServletRequest request)
            throws ParseException, JsonProcessingException {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        logger.info("save : user: {} , params:{}", unionid, objectMapper.writeValueAsString(followupDTO));
        OrderFollowup followup = new OrderFollowup();
        BeanUtils.copyProperties(followupDTO, followup);
        return CommonResult.succeeded(followupService.save(followup, unionid));
    }

}
