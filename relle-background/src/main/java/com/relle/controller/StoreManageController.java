package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppStoreInfo;
import com.relle.mbg.model.AppStoreServiceRelation;
import com.relle.service.StoreInfoService;
import com.relle.service.StoreServiceRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店管理接口
 */
@Api(tags = "门店管理接口")
@RestController
@RequestMapping(value = "/store")
public class StoreManageController {
    @Resource
    private StoreInfoService storeInfoService;
    @Resource
    private StoreServiceRelationService storeServiceRelationService;

    @ApiOperation(value = "门店列表")
    @PostMapping(value = "/list")
    public CommonResult<Object> list(){
        return CommonResult.succeeded(storeInfoService.findAll());
    }


    @ApiOperation(value = "产品关联已关联门店列表")
    @PostMapping(value = "/relation/list/{serviceItemId}")
    public CommonResult<List<Object>> relationList(@PathVariable String serviceItemId){
        List<AppStoreServiceRelation> serviceRelationList = storeServiceRelationService.findByServiceItemId(serviceItemId);
        List<Object> storeList = serviceRelationList.stream()
                .map(serviceRelation -> storeInfoService.getByStoreId(serviceRelation.getStoreId()))
                .collect(Collectors.toList());
        return CommonResult.succeeded(storeList);
    }

    @ApiOperation(value = "保存产品关联门店")
    @PostMapping(value = "/relation/save/{serviceItemId}")
    public CommonResult<List<AppStoreInfo>> relationSave(@PathVariable String serviceItemId,
                                                         @RequestBody List<AppStoreInfo> storeInfos,
                                                         HttpServletRequest request){
        String openid = (String)request.getSession().getAttribute("openid");
        List<AppStoreServiceRelation> serviceRelationList = storeServiceRelationService.findByServiceItemId(serviceItemId);
        storeServiceRelationService.deleteAll(serviceRelationList);
        storeInfos.forEach( storeInfo -> {
            AppStoreServiceRelation relation = new AppStoreServiceRelation();
            relation.setStoreId(storeInfo.getStoreId());
            relation.setServiceItemId(serviceItemId);
            relation.setServiceItemCategory((byte)2);
            relation.setCreateBy(openid);
            relation.setUpdateBy(openid);
            storeServiceRelationService.save(relation);
        });

        return CommonResult.succeeded(storeInfos);
    }

}
