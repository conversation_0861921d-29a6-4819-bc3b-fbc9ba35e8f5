package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppTestMedia;
import com.relle.service.IAppTestMediaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@RequestMapping(value = "/media")
public class AppTestMediaController {
    private static final Logger logger = LoggerFactory.getLogger(AppAvailableTimeController.class);
    @Resource
    private IAppTestMediaService iAppTestMediaService;


    @PostMapping(value = "/upload")
    public CommonResult<?> upload(MultipartFile file, HttpServletRequest request) throws IOException {
        String md5 = DigestUtils.md5DigestAsHex(file.getInputStream());
        AppTestMedia old_media = iAppTestMediaService.getMediaByMd5(md5);

        if(old_media!=null){
            return  CommonResult.succeeded(old_media);
        }

        String openid = (String)request.getSession().getAttribute("openid");
        String storeId = request.getParameter("storeId");
        if(StringUtils.isEmpty(storeId)){
            storeId = "SH0001";
        }
        return iAppTestMediaService.saveMedia(md5,file,openid,storeId);
    }


}
