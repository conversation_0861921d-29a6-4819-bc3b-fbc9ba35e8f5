package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppServiceItemDTO;
import com.relle.dto.AppServiceItemDetailDTO;
import com.relle.dto.AppServiceItemVO;
import com.relle.enums.ServiceItemMediaTypeEnum;
import com.relle.mbg.model.AppCoupon;
import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppServiceMedia;
import com.relle.mbg.model.AppServiceMediaRelation;
import com.relle.mbg.model.AppStoreInfo;
import com.relle.service.AppCouponService;
import com.relle.service.IAppServiceItemService;
import com.relle.service.ServiceMediaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 计划包管理接口
 */
@Tag(name = "计划包管理接口", description = "计划包的增删改查接口")
@RestController
@RequestMapping(value = "/service/bundle")
public class ServiceBundleManageController {
    @Resource
    private IAppServiceItemService serviceItemService;
    @Resource
    private ServiceMediaService serviceMediaService;
    @Resource
    private AppCouponService couponService;

    @Operation(summary = "计划包列表", description = "获取所有计划包列表")
    @PostMapping(value = "/list")
    public CommonResult<List<AppServiceItemVO>> list() {
        List<AppServiceItemVO> itemList = serviceItemService.listAllItemByCategory((byte) 2);
        // itemList.sort(Comparator
        //         .comparing(AppServiceItemVO::getAppServiceItem), Comparator.comparing(AppServiceItem::getCreateTime))
        //         .reversed();
        return CommonResult.succeeded(itemList);
    }

    @Operation(summary = "计划包详情", description = "根据ID获取计划包详细信息")
    @PostMapping(value = "/detail/{serviceItemId}")
    public CommonResult<AppServiceItemVO> detail(
            @Parameter(description = "计划包ID") @PathVariable String serviceItemId) {
        AppServiceItem item = serviceItemService.getItem(serviceItemId);
        if (item == null) {
            return CommonResult.failed("计划包不存在");
        }
        if (!item.getServiceId().startsWith("D")) {
            return CommonResult.failed("非计划包编号");
        }
        return CommonResult.succeeded(serviceItemService.getItemVO(serviceItemId));
    }

    @Operation(summary = "计划包保存", description = "保存计划包信息")
    @PostMapping(value = "/save")
    public CommonResult<AppServiceItem> save(
            @Parameter(description = "计划包详细信息") @RequestBody AppServiceItemDetailDTO detailDTO,
            HttpServletRequest request) {
        String openid = (String) request.getSession().getAttribute("openid");

        AppServiceItem item = detailDTO.getAppServiceItem();
        if (item.getId() != null) {
            item.setUpdateBy(openid);
            serviceItemService.update(item);
        } else {
            item.setServiceId(getItemId("D"));
            List<AppServiceItemDTO> serviceItemList = detailDTO.getServiceItemList();
            AtomicReference<String> contentAtomic = new AtomicReference<>("");
            serviceItemList.forEach(serviceItemDTO -> {
                AppServiceItem serviceItem = serviceItemDTO.getAppServiceItem();
                contentAtomic
                        .set(contentAtomic.get() + serviceItem.getServiceName() + "*" + serviceItemDTO.getNum() + "+");
                // 查找产品关联的抵扣券
                AppCoupon coupon = couponService.getDKCouponBySerivceItem(serviceItem.getServiceId());
                if (coupon == null) {
                    try {
                        coupon = couponService.save(serviceItem);
                        couponService.saveRelation(serviceItem, coupon);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
                serviceItemService.addCoupon(item, coupon, serviceItemDTO.getNum());
            });
            String content = contentAtomic.get();
            if (!content.isEmpty()) {
                content = content.substring(0, content.length() - 1);
            }
            item.setServiceDurationSec(0);
            item.setServiceClosingSec(0);
            item.setServicePreparationSec(0);
            item.setChannelType((byte) 2);
            item.setServiceShowSort((byte) 1);
            item.setServiceStatus((byte) 1);
            item.setServiceContent(content);
            item.setCreateBy(openid);
            item.setUpdateBy(openid);
            serviceItemService.insert(item);
        }
        // 多媒体
        AppServiceMedia thumbnail = detailDTO.getThumbnail();
        List<AppServiceMedia> appServiceMediaList = detailDTO.getAppServiceMediaList();
        List<AppServiceMedia> appServiceDetailMediaList = detailDTO.getAppServiceDetailMediaList();

        List<AppServiceMediaRelation> relationList = serviceMediaService.findByServiceId(item.getServiceId());
        if (!CollectionUtils.isEmpty(relationList)) {
            relationList.forEach(relation -> {
                serviceMediaService.deleteRelation(relation.getId());
            });
        }
        serviceMediaService.saveRelation(item.getServiceId(), thumbnail, ServiceItemMediaTypeEnum.THUMBNAIL.getCode());
        appServiceMediaList.forEach(media -> {
            serviceMediaService.saveRelation(item.getServiceId(), media, ServiceItemMediaTypeEnum.PRODUCT.getCode());
        });
        appServiceDetailMediaList.forEach(media -> {
            serviceMediaService.saveRelation(item.getServiceId(), media,
                    ServiceItemMediaTypeEnum.PRODUCT_DETAIL.getCode());
        });
        return CommonResult.succeeded(item);
    }

    private String getItemId(String prefix) {
        LocalDateTime now = LocalDateTime.now();
        return prefix + now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
    }
}
