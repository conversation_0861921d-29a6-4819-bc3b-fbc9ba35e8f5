package com.relle.service;

/**
 * Batch Cache Service Interface
 * Handles batch caching operations
 */
public interface IBatchCacheService {
    
    /**
     * Release room ID
     * @param storeId store ID
     * @param bookTime booking time
     * @param roomId room ID
     */
    void releaseRoomId(String storeId, Object bookTime, String roomId);
    
    /**
     * Cache data
     * @param key cache key
     * @param value cache value
     */
    void cacheData(String key, Object value);
    
    /**
     * Get cached data
     * @param key cache key
     * @return cached value
     */
    Object getCachedData(String key);
    
    /**
     * Clear cache
     * @param key cache key
     */
    void clearCache(String key);
}
