package com.relle.service;

import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppActivity;

/**
 * App Activity Service Interface
 * Handles activity operations
 */
public interface IAppActivityService {
    
    /**
     * Get active activities
     * @param storeId store ID
     * @return active activities
     */
    CommonResult<?> getActiveActivities(String storeId);
    
    /**
     * Get activity details
     * @param activityId activity ID
     * @return activity details
     */
    CommonResult<?> getActivityDetails(String activityId);
    
    /**
     * Participate in activity
     * @param activityId activity ID
     * @param userId user ID
     * @return participation result
     */
    CommonResult<?> participateInActivity(String activityId, String userId);

    List<AppActivity> getActivityListByCategory(String sotreId, Integer code);

    List<AppActivity> getActivityList(String string);

    CommonResult<?> getCouponByActivity(Long activityId, String unionid);

    CommonResult<?> receiveCouponByActivity(Long valueOf, String unionid);

    AppActivity getActivity(String storeId, String activityId);
}
