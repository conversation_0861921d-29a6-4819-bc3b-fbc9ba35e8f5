package com.relle.service.open;

import com.relle.dto.AdditionalOrderRO;
import com.relle.dto.AdditionalOrderVO;
import com.relle.mbg.mapper.AppServiceSuborderAdditionalMapper;
import com.relle.mbg.model.AppOrderPay;
import com.relle.mbg.model.AppServiceSuborderAdditional;
import com.relle.service.IAppServiceSuborderAdditionalService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OrderOpenServiceImpl implements IOrderOpenService{
    @Resource
    private IAppServiceSuborderAdditionalService iAppServiceSuborderAdditionalService;
    @Override
    public AppOrderPay createOrder(AdditionalOrderRO orderRO) {
        AppServiceSuborderAdditional additional = new AppServiceSuborderAdditional();
        additional.setOrderId(orderRO.getOrderId());

        return null;
    }

    @Override
    public AdditionalOrderVO getAdditionalOrder(String orderId) {
        return null;
    }
}
