package com.relle.service;

import com.relle.enums.AppServiceItemCategoryEnum;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AppServiceOrderServiceFactory {
    @Resource
    private ApplicationContext applicationContext;
    public IOrderService getService(Byte serviceItemCategory) throws BeansException{
        return (IOrderService)applicationContext.getBean(AppServiceItemCategoryEnum.getServiceImpClass(serviceItemCategory)  + "class");
    }

    public String getAutoTask(Byte serviceItemCategory){
       return AppServiceItemCategoryEnum.getJobName(serviceItemCategory);
    }
}
