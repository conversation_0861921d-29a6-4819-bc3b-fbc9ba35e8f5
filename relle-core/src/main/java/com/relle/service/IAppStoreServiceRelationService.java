package com.relle.service;

import com.relle.mbg.model.AppStoreServiceRelation;
import java.util.List;

/**
 * App Store Service Relation Service Interface
 * Handles store-service relationship operations
 */
public interface IAppStoreServiceRelationService {
    
    /**
     * Get store IDs by service item
     * @param serviceItemId service item ID
     * @return list of store-service relations
     */
    List<AppStoreServiceRelation> getStoreIdByServiceItem(String serviceItemId);
}
