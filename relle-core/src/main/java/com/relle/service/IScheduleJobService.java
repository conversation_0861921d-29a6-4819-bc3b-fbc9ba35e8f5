package com.relle.service;


import com.relle.mbg.model.SysTask;

public interface IScheduleJobService {
    public void initSchedule();
    public void createJob(String beanClass,String jobgroup,String orderId);
    public void createJob(String beanClass,String jobgroup,String orderId,int minute);
    public void addJob(SysTask task,Boolean needInsert);
    // public void deleteJob(JobKey jobKey) throws SchedulerException;
    public void deleteJob(SysTask sysTask);
}
