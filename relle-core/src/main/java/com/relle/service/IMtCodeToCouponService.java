package com.relle.service;

import com.relle.common.api.CommonResult;

/**
 * Meituan Code to Coupon Service Interface
 * Handles coupon code conversion for Meituan integration
 */
public interface IMtCodeToCouponService {

    /**
     * Convert code to coupon
     * @param code coupon code
     * @param storeId store ID
     * @return conversion result
     */
    CommonResult<?> convertCodeToCoupon(String code, String storeId);

    /**
     * Validate coupon code
     * @param code coupon code
     * @return validation result
     */
    boolean validateCouponCode(String code);

    /**
     * Get coupon details by code
     * @param code coupon code
     * @return coupon details
     */
    CommonResult<?> getCouponByCode(String code);

    // Additional methods needed by controllers
    /**
     * Save coupon conversion record
     * @param param1 parameter 1
     * @param param2 parameter 2
     * @param param3 parameter 3
     * @param param4 parameter 4
     * @param param5 parameter 5
     * @param param6 parameter 6
     * @param param7 parameter 7
     * @return save result
     */
    int save(String param1, String param2, String param3, String param4, String param5, String param6, long param7);
}
