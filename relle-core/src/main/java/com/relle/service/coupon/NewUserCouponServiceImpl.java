package com.relle.service.coupon;

import com.relle.mbg.model.AppCouponCheckout;
import com.relle.service.IReceieveCouponService;
import org.springframework.stereotype.Service;

@Service(value="is_new")
public class NewUserCouponServiceImpl implements IReceieveCouponService {
    @Override
    public boolean check() {
       // XXX 判断
        return false;
    }

    @Override
    public AppCouponCheckout receiveCoupon(String unionid) {
        return null;
    }

}
