package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppServiceOperationRecord;
import com.relle.mbg.model.AppServiceSuborderAdditional;

/**
 * App Service Operation Record Service Interface
 * Handles service operation record operations
 */
public interface IAppServiceOperationRecordService {

    /**
     * Get operation record by ID
     * @param id record ID
     * @return operation record entity
     */
    AppServiceOperationRecord getOperationRecordById(String id);

    /**
     * Create operation record
     * @param record operation record data
     * @return creation result
     */
    CommonResult<?> createOperationRecord(Object record);

    /**
     * Update operation record
     * @param record operation record data
     * @return update result
     */
    CommonResult<?> updateOperationRecord(Object record);

    // Additional methods needed by controllers
    AppServiceOperationRecord getOperationRecord(String id);
    CommonResult<?> stepOneEnd(Long id);
    CommonResult<?> stepTwoStart(Long id);
    CommonResult<?> stepTwoEnd(Long id);
    CommonResult<?> serviceEnd(Long id);

    Object startService(String unionidFromSession, AppServiceSuborderAdditional oneBySuborderId,
            String serviceOperatorId);

    Object endService(Long operationRecordId);
}
