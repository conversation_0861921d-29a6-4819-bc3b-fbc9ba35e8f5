package com.relle.service;

import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.OrderFollowup;

/**
 * Order Followup Service Interface
 */
public interface OrderFollowupService {
    
    /**
     * Get order followup by ID
     * @param followupId followup ID
     * @return order followup data
     */
    CommonResult<?> getOrderFollowupById(String followupId);
    
    /**
     * Get order followups by order ID
     * @param orderId order ID
     * @return order followup list
     */
    CommonResult<?> getOrderFollowupsByOrderId(String orderId);
    
    /**
     * Create order followup
     * @param followupData followup data
     * @return creation result
     */
    CommonResult<?> createOrderFollowup(Object followupData);
    
    /**
     * Update order followup
     * @param followupData followup data
     * @return update result
     */
    CommonResult<?> updateOrderFollowup(Object followupData);
    
    /**
     * Delete order followup
     * @param followupId followup ID
     * @return deletion result
     */
    CommonResult<?> deleteOrderFollowup(String followupId);

    List<OrderFollowup> findByServiceOperationId(String serviceOperationId);

    Object save(OrderFollowup followup, String unionid);
}
