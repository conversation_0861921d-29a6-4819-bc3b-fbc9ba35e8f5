package com.relle.service;

import java.util.List;

import com.relle.dto.EmployeeDTO;
import com.relle.mbg.model.AppEmployee;
import com.relle.mbg.model.AppStoreEmployeeRelation;
public interface IAppEmployeeService {
    public AppEmployee getAppEmployeeByOpenId(String storeId,String openId);

    public AppEmployee getAppEmployeeByOpenId(String openid);

    public List<AppStoreEmployeeRelation> getEmployeeRelationByEmployeeId(String employeeId);

    public void insert(AppEmployee appEmployee);

    public void insertRalation(String employeeId, String string, Object roleCode);

    public AppEmployee getAppEmployeeById(Long id);

    public void update(AppEmployee appEmployee);

    public void deleteRalationByEmployeeId(String employeeId);

    public List<AppEmployee> getAll(EmployeeDTO query);

}
