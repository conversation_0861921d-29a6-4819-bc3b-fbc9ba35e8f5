package com.relle.service;


import java.util.Map;

import com.relle.common.api.CommonResult;
import java.time.LocalDateTime;

public interface IOrderService {
    public CommonResult<?> createOrder(Map<String,Object> orderMap);
    public CommonResult<?> getOrderDetail(String orderId);
    public int paySuccess(String orderId,String successTime,String transaction_id);
    public int refundResult(String orderId,byte refundStatus,String successTime,String transaction_id);

    public int updateOrderStatus(String orderId,short orderStatus,String updateBy);

    public CommonResult<?> cancelOrder(String orderId,String unionid);
    public CommonResult<?> refundOrder(String orderId,String unionid,String refundOrderId,double refundAmount,String refundReason);

    public boolean checkOrder(String orderId, String unionid);
    public boolean checkOrderAdmin(String orderId, String unionid);

    public CommonResult<?> updateBookTime(String orderId, String unionid, LocalDateTime bookTime);
}
