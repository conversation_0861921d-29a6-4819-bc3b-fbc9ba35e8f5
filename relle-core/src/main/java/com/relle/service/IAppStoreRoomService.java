package com.relle.service;

import com.relle.common.api.CommonResult;

/**
 * App Store Room Service Interface
 * Handles store room operations
 */
public interface IAppStoreRoomService {

    /**
     * Get available rooms
     * @param storeId store ID
     * @param date booking date
     * @return available rooms
     */
    CommonResult<?> getAvailableRooms(String storeId, Object date);

    /**
     * Book room
     * @param storeId store ID
     * @param roomId room ID
     * @param bookingData booking data
     * @return booking result
     */
    CommonResult<?> bookRoom(String storeId, String roomId, Object bookingData);

    /**
     * Get room details
     * @param storeId store ID
     * @param roomId room ID
     * @return room details
     */
    CommonResult<?> getRoomDetails(String storeId, String roomId);

    // Additional methods needed by controllers
    CommonResult<?> getListByStore(String storeId);
}
