package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppStoreInfo;
import java.util.List;

/**
 * App Store Info Service Interface
 * Handles store information operations
 */
public interface IAppStoreInfoService {
    
    /**
     * Query all stores by type
     * @param storeType store type (0 for beauty shop, 1 for clinic)
     * @return list of stores
     */
    CommonResult<?> queryAllStoreByType(Byte storeType);
    
    /**
     * Get store by store ID
     * @param storeId store ID
     * @return store information
     */
    CommonResult<?> getByStoreId(String storeId);
    
    /**
     * List all stores by type (returns entity list)
     * @param storeType store type
     * @return list of AppStoreInfo entities
     */
    List<AppStoreInfo> listAllStoreByType(Byte storeType);

    List<AppStoreInfo> getStoreList();

    AppStoreInfo getStoreInfoByStoreId(String storeId);
}
