package com.relle.service;

import java.time.LocalDate;
import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppAvailableTime;

/**
 * App Available Time Service Interface
 */
public interface IAppAvailableTimeService {
    
    /**
     * Get available time list
     * @param storeId store ID
     * @param serviceId service ID
     * @param date date
     * @return available time list
     */
    CommonResult<?> getAvailableTimeList(String storeId, String serviceId, String date);
    
    /**
     * Create available time
     * @param startDate start date
     * @param endDate end date
     * @return creation result
     */
    CommonResult<?> createAvailableTime(String startDate, String endDate);
    
    /**
     * Get available times by store and date
     * @param storeId store ID
     * @param date date
     * @return available times
     */
    CommonResult<?> getAvailableTimesByStoreAndDate(String storeId, String date);
    
    /**
     * Update available time status
     * @param timeId time ID
     * @param status status
     * @return update result
     */
    CommonResult<?> updateAvailableTimeStatus(String timeId, String status);

    List<AppAvailableTime> getAvailableTimeByDate(LocalDate startDate);
}
