package com.relle.service;

import com.relle.mbg.model.AppOrderRefund;

import java.math.BigDecimal;
import java.util.List;

public interface IAppOrderRefundService {
    public int createRefund(String orderId, BigDecimal orderAmount, String refundOrderId, double refundAmount, String refundReason, String unionid);
    public AppOrderRefund getRefund(String orderId);
    public List<AppOrderRefund> getRefundList(String orderId);
    public int updateRefund(AppOrderRefund refund,String refundOrderId);
}
