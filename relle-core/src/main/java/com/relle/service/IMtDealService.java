package com.relle.service;

import com.relle.common.api.CommonResult;

/**
 * Meituan Deal Service Interface
 * Handles deal operations for Meituan integration
 */
public interface IMtDealService {

    /**
     * Get deal information
     * @param dealId deal ID
     * @return deal information
     */
    CommonResult<?> getDealInfo(String dealId);

    /**
     * Search deals by criteria
     * @param storeId store ID
     * @param keyword search keyword
     * @return search results
     */
    CommonResult<?> searchDeals(String storeId, String keyword);

    /**
     * Validate deal
     * @param dealId deal ID
     * @return validation result
     */
    boolean validateDeal(String dealId);

    // Additional methods needed by controllers
    /**
     * Get service item ID by deal
     * @param storeId store ID
     * @param dealId deal ID
     * @return service item ID
     */
    String getServiceItemIdByDeal(String storeId, Long dealId);
}
