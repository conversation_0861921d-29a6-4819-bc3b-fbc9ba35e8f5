package com.relle.service;

import java.math.BigDecimal;

import com.relle.common.api.CommonResult;

/**
 * App Order Recommend Service Interface
 * Handles order recommendation operations
 */
public interface IAppOrderRecommendService {
    
    /**
     * Get order recommendations
     * @param userId user ID
     * @param orderData order data
     * @return recommendations
     */
    CommonResult<?> getOrderRecommendations(String userId, Object orderData);
    
    /**
     * Get recommended items
     * @param serviceItemId service item ID
     * @return recommended items
     */
    CommonResult<?> getRecommendedItems(String serviceItemId);

    void save(String orderId, String unionid, BigDecimal valueOf, String employeeId, byte code);

}
