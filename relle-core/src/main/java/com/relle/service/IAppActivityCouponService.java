package com.relle.service;

import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppActivityCoupon;

/**
 * App Activity Coupon Service Interface
 * Handles activity coupon operations
 */
public interface IAppActivityCouponService {
    
    /**
     * Get activity coupons
     * @param activityId activity ID
     * @return activity coupons
     */
    CommonResult<?> getActivityCoupons(String activityId);
    
    /**
     * Claim activity coupon
     * @param activityId activity ID
     * @param couponId coupon ID
     * @param userId user ID
     * @return claim result
     */
    CommonResult<?> claimActivityCoupon(String activityId, String couponId, String userId);

    List<AppActivityCoupon> getByActivity(Long id);
}
