package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppStoreInfo;

/**
 * Store Info Service Interface
 */
public interface StoreInfoService {
    
    /**
     * Get store info by ID
     * @param storeId store ID
     * @return store info
     */
    CommonResult<?> getStoreInfoById(String storeId);
    
    /**
     * Get all stores
     * @return list of stores
     */
    CommonResult<?> getAllStores();
    
    /**
     * Create store info
     * @param storeData store data
     * @return creation result
     */
    CommonResult<?> createStoreInfo(Object storeData);
    
    /**
     * Update store info
     * @param storeData store data
     * @return update result
     */
    CommonResult<?> updateStoreInfo(Object storeData);
    
    /**
     * Delete store info
     * @param storeId store ID
     * @return deletion result
     */
    CommonResult<?> deleteStoreInfo(String storeId);

    AppStoreInfo getByStoreId(String storeId);

    Object findAll();
}
