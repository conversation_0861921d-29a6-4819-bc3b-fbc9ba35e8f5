package com.relle.service;

import com.relle.common.api.CommonResult;

/**
 * App Service Operation Feedback Service Interface
 * Handles service operation feedback operations
 */
public interface IAppServiceOperationFeedbackService {

    /**
     * Submit feedback
     * @param feedbackData feedback data
     * @return submission result
     */
    CommonResult<?> submitFeedback(Object feedbackData);

    /**
     * Get feedback list
     * @param operationId operation ID
     * @return feedback list
     */
    CommonResult<?> getFeedbackList(String operationId);

    /**
     * Get feedback details
     * @param feedbackId feedback ID
     * @return feedback details
     */
    CommonResult<?> getFeedbackDetails(String feedbackId);

    // Additional methods needed by controllers
    CommonResult<?> save(long operationId, String feedbackContent, String unionid);
}
