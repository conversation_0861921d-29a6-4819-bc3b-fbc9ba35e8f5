package com.relle.service;

import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppCouponDTO;
import com.relle.mbg.model.AppCoupon;
import com.relle.mbg.model.AppCouponCheckout;

/**
 * App Coupon Service Interface
 * Handles coupon operations
 */
public interface IAppCouponService {
    
    /**
     * Get user coupons
     * @param userId user ID
     * @return user coupons
     */
    CommonResult<?> getUserCoupons(String userId);
    
    /**
     * Get coupon details
     * @param couponId coupon ID
     * @return coupon details
     */
    CommonResult<?> getCouponDetails(String couponId);
    
    /**
     * Use coupon
     * @param couponId coupon ID
     * @param orderId order ID
     * @return usage result
     */
    CommonResult<?> useCoupon(String couponId, String orderId);
    
    /**
     * Validate coupon
     * @param couponId coupon ID
     * @return validation result
     */
    CommonResult<?> validateCoupon(String couponId);

    public List<AppCouponDTO> getMyCoupon(String unionid);
    public List<AppCouponDTO> getMyCoupon(String unionid,String serviceItemId);
    public AppCouponCheckout receiveCoupon(String unionid,Long couponId);

    public AppCoupon getCoupon(String couponId);

    public AppCoupon getCouponById(Long couponId);

    int useCoupon(Long couponId,String orderId,String useCouponUserUnionid);
}
