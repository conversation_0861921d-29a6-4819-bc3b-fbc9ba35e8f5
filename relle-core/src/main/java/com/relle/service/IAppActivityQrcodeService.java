package com.relle.service;

import com.relle.mbg.model.AppActivityQrcode;

import java.util.List;

public interface IAppActivityQrcodeService {
    List<AppActivityQrcode> getQrcodeByStatus(String storeId,String activityId, byte status);
    AppActivityQrcode getQrcodeByQrcodeParams(String storeId,String activityId,String qrcodeParams);
    int update(String storeId,String activityId,AppActivityQrcode appActivityQrcode);
    int updateQrcodeStatus(String storeId,String activityId,String unionid,byte qrcodeStatus);
}
