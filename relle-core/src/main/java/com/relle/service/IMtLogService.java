package com.relle.service;

/**
 * Meituan Log Service Interface
 * Handles logging for Meituan API operations
 */
public interface IMtLogService {
    
    /**
     * Log API request
     * @param apiName API name
     * @param request request data
     */
    void logRequest(String apiName, String request);
    
    /**
     * Log API response
     * @param apiName API name
     * @param response response data
     */
    void logResponse(String apiName, String response);
    
    /**
     * Log error
     * @param apiName API name
     * @param error error message
     */
    void logError(String apiName, String error);
}
