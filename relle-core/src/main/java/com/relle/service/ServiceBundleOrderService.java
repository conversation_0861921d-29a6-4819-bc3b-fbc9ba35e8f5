package com.relle.service;

import java.util.Collection;

import javax.validation.Valid;

import com.relle.common.api.CommonResult;
import com.relle.dto.AccountReconciliationOrderVO;
import com.relle.dto.OrderQueryDTO;

/**
 * Service Bundle Order Service Interface
 */
public interface ServiceBundleOrderService {
    
    /**
     * Get service bundle order by ID
     * @param orderId order ID
     * @return service bundle order data
     */
    CommonResult<?> getServiceBundleOrderById(String orderId);
    
    /**
     * Get service bundle orders by user ID
     * @param userId user ID
     * @return service bundle order list
     */
    CommonResult<?> getServiceBundleOrdersByUserId(String userId);
    
    /**
     * Create service bundle order
     * @param orderData order data
     * @return creation result
     */
    CommonResult<?> createServiceBundleOrder(Object orderData);
    
    /**
     * Update service bundle order
     * @param orderData order data
     * @return update result
     */
    CommonResult<?> updateServiceBundleOrder(Object orderData);
    
    /**
     * Cancel service bundle order
     * @param orderId order ID
     * @return cancellation result
     */
    CommonResult<?> cancelServiceBundleOrder(String orderId);

    Collection<? extends AccountReconciliationOrderVO> getOrderListOfAccountReconciliation(OrderQueryDTO query);
}
