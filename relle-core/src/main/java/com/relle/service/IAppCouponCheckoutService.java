package com.relle.service;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppCoupon;
import com.relle.mbg.model.AppCouponCheckout;
import com.relle.mbg.model.AppCustomerInfo;

/**
 * App Coupon Checkout Service Interface
 * Handles coupon checkout operations
 */
public interface IAppCouponCheckoutService {
    
    /**
     * Checkout coupon
     * @param couponId coupon ID
     * @param checkoutData checkout data
     * @return checkout result
     */
    CommonResult<?> checkoutCoupon(String couponId, Object checkoutData);
    
    /**
     * Get checkout history
     * @param userId user ID
     * @return checkout history
     */
    CommonResult<?> getCheckoutHistory(String userId);
    
    /**
     * Validate coupon for checkout
     * @param couponId coupon ID
     * @return validation result
     */
    CommonResult<?> validateCouponForCheckout(String couponId);



    AppCouponCheckout getById(Long id);
    AppCoupon getCouponById(Long checkoutId);

    int sharedCouponById(Long checkoutId,String unionid);
    boolean canShare(AppCouponCheckout couponCheckout,AppCustomerInfo customerInfo);

    int confirmShare(AppCouponCheckout couponCheckout, AppCustomerInfo customerInfo);

    AppCouponCheckout grant(String couponId, String unionid,int couponNum,byte couponStatus,int couponSource,String buyOrderId);
    AppCouponCheckout grant(String storeIds,String couponId, String unionid,int couponNum,byte couponStatus,int couponSource,String buyOrderId);

    List<AppCouponCheckout> getListByUnionid(String unionid);
    AppCouponCheckout getByCouponId(String couponId,String unionid);

    List<AppCouponCheckout> getListByCouponId(String storeId, String couponId, LocalDateTime startDate, LocalDateTime endDate);

    List<AppCouponCheckout> getListByStatus(Byte status);
    int backCoupon(String unionid,String orderNo);
    int backCoupon(Long checkoutId,String orderNo);

    int invalidateCoupon(Long id);

    int deleteCoupon(Long id);
    JsonNode hasReceived(String couponId, String unionid);

    boolean checkCouponValid(AppCouponCheckout couponCheckout);
}
