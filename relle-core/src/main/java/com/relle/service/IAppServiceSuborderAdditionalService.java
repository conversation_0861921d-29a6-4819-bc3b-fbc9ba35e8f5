package com.relle.service;

import com.relle.dto.AdditionalSuborderDTO;
import com.relle.mbg.model.AppServiceSuborderAdditional;

import java.util.List;

public interface IAppServiceSuborderAdditionalService {
    AppServiceSuborderAdditional getOneBySuborderId(String suborderId);
    List<AdditionalSuborderDTO> getListByOrderId(String parentOrderId);
    List<AppServiceSuborderAdditional> getAdditionalListByOrderId(String parentOrderId);
    AppServiceSuborderAdditional save(AppServiceSuborderAdditional serviceSuborderAdditional);
    int update(AppServiceSuborderAdditional serviceSuborderAdditional);

    int paySuccess(String orderId,String successTime,String transaction_id);

    boolean checkOrderCanCancel(AppServiceSuborderAdditional suborderAdditional);
}
