package com.relle.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppServiceMedia;
import com.relle.mbg.model.AppServiceMediaRelation;

/**
 * Service Media Service Interface
 */
public interface ServiceMediaService {
    
    /**
     * Get service media by ID
     * @param mediaId media ID
     * @return service media data
     */
    CommonResult<?> getServiceMediaById(String mediaId);
    
    /**
     * Get service media by service ID
     * @param serviceId service ID
     * @return service media list
     */
    CommonResult<?> getServiceMediaByServiceId(String serviceId);
    
    /**
     * Create service media
     * @param mediaData media data
     * @return creation result
     */
    CommonResult<?> createServiceMedia(Object mediaData);
    
    /**
     * Update service media
     * @param mediaData media data
     * @return update result
     */
    CommonResult<?> updateServiceMedia(Object mediaData);
    
    /**
     * Delete service media
     * @param mediaId media ID
     * @return deletion result
     */
    CommonResult<?> deleteServiceMedia(String mediaId);

    AppServiceMedia getMediaByMd5(String md5);

    Object saveMedia(String md5, MultipartFile file, String section, String openid);

    List<AppServiceMediaRelation> findByServiceId(String serviceId);

    void deleteRelation(Long id);

    void saveRelation(String serviceId, AppServiceMedia thumbnail, String code);
}
