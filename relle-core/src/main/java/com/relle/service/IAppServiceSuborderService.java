package com.relle.service;

import com.relle.mbg.model.AppServiceSuborder;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface IAppServiceSuborderService {
    List<AppServiceSuborder> getValidSuborderByDate(String storeId,LocalDateTime start, LocalDateTime end);
    List<AppServiceSuborder> getValidSuborderByDate(String storeId,String start, String end);
    List<AppServiceSuborder> getValidSuborderByDate(String storeId,LocalDate day);
    List<AppServiceSuborder> getValidSuborderByDate(String storeId,String day);

    AppServiceSuborder getSuborderBySuborderId(String suborderId);
}
