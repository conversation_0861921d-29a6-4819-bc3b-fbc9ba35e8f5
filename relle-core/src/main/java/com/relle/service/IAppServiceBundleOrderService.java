package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppServiceBundleOrderDTO;
import com.relle.mbg.model.*;

import java.util.List;
import java.util.Map;

public interface IAppServiceBundleOrderService extends IOrderService{
    public CommonResult<?> createOrder(Map<String,Object> orderMap);
    public List<AppServiceBundleOrderDTO> getOrderList(String unionid);
    public CommonResult<?> getOrderDetail(String orderId);
    public AppServiceBundleOrderDTO getOrderDetailDTO(String orderId);

    public int paySuccess(String orderId,String successTime,String transaction_id);
    public int refundResult(String orderId,byte refundStatus,String successTime,String transaction_id);

    public int updateOrderStatus(String orderId,short orderStatus,String updateBy);

    public CommonResult<?> cancelOrder(String orderId,String unionid);
    public CommonResult<?> refundOrder(String orderId,String unionid,String refundOrderId,double refundAmount,String refundReason);

    public boolean checkOrder(String orderId, String unionid);

    public AppServiceBundleOrder getOrder(String orderId);
    public List<AppServiceBundleSuborder> getItemIdByOrderId(String orderId);
}
