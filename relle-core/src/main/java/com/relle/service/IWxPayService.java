package com.relle.service;

import com.fasterxml.jackson.databind.JsonNode;
// import com.relle.commons.utils.PayConstants;
import com.relle.mbg.model.AppOrderPay;
// import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
// import com.wechat.pay.contrib.apache.httpclient.auth.AutoUpdateCertificatesVerifier;
// import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
// import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
// import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
// import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
// import org.apache.http.client.methods.CloseableHttpResponse;
// import org.apache.http.client.methods.HttpPost;
// import org.apache.http.entity.StringEntity;
// import org.apache.http.impl.client.CloseableHttpClient;
// import org.apache.http.util.EntityUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface IWxPayService {
   Map toPay(String storeId,Byte serviceItemCategory,String desc, Integer totalAmount, String outTradeNo, String openid,String autoTask,IOrderService service);
   Map createOrderFromJsapi(String storeId, String desc, Integer total, String outTradeNo, String openid, Byte serviceItemCategory) throws Exception;
   boolean closeOrder(String storeId,String outTradeNo,Byte serviceItemCategory) throws Exception;
   String sign(String storeId,Byte serviceItemCategory,byte[] message) throws Exception;
   public String decryptOrder(String storeId,Byte serviceItemCategory,String body);
   public boolean signVerify(String storeId,Byte serviceItemCategory,String serial, String message, String signature) throws Exception;
   public  String createRefundOrder(String storeId,Byte serviceItemCategory,String transactionId, String outTradeNo, String outRefundNo, int total, int refund) throws Exception;
   public String queryRefundOrder(String storeId,Byte serviceItemCategory,String outRefundNo);
   public  String queryOrder(String storeId,Byte serviceItemCategory,String outTradeNo);

   String createRefundOrder(String storeId,Byte serviceItemCategory,String transactionId, String outTradeNo, String outRefundNo, int total, int refund,String notify_url) throws Exception;
   JsonNode decryptNotifyUrlBody(String storeId,Byte serviceItemCategory,HttpServletRequest request);
   AppOrderPay createPrepay(String storeId, Byte serviceItemCategory, String desc, Integer totalAmount, String outTradeNo, String openid);
}
