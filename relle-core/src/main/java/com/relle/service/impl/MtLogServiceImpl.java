package com.relle.service.impl;

import com.relle.service.IMtLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Meituan Log Service Implementation
 */
@Service
public class MtLogServiceImpl implements IMtLogService {
    
    private static final Logger logger = LoggerFactory.getLogger(MtLogServiceImpl.class);
    
    @Override
    public void logRequest(String apiName, String request) {
        logger.info("MT API Request - {}: {}", apiName, request);
    }
    
    @Override
    public void logResponse(String apiName, String response) {
        logger.info("MT API Response - {}: {}", apiName, response);
    }
    
    @Override
    public void logError(String apiName, String error) {
        logger.error("MT API Error - {}: {}", apiName, error);
    }
}
