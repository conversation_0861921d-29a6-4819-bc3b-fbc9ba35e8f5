package com.relle.service.impl;

import com.relle.mbg.mapper.AppOrderPayMapper;
import com.relle.mbg.model.AppOrderPay;
import com.relle.mbg.model.AppOrderPayExample;
import com.relle.service.IAppOrderPayService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service
public class AppOrderPayServiceImpl implements IAppOrderPayService {
    @Resource
    private AppOrderPayMapper appOrderPayMapper;

    @Override
    public AppOrderPay getPay(String orderId) {
        List<AppOrderPay> list = _getPayList(orderId);
        if(list.isEmpty()){
            return null;
        }
        AppOrderPay pay = list.get(0);
        // TODO 15 should not be hard coded here
        pay.setPayEndTime(pay.getCreateTime().plusMinutes(15));
        return pay;
    }

    @Override
    public int updatePay(AppOrderPay pay) {
        AppOrderPayExample example = new AppOrderPayExample();
        example.createCriteria().andOrderIdEqualTo(pay.getOrderId());
        return appOrderPayMapper.updateByExampleSelective(pay,example);
    }

    private List<AppOrderPay> _getPayList(String orderId) {
        AppOrderPayExample example = new AppOrderPayExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        return appOrderPayMapper.selectByExample(example);
    }
}
