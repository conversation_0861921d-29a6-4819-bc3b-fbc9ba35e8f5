package com.relle.service.impl;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppServiceBundleOrderDTO;
import com.relle.dto.AppServiceBundleSuborderDTO;
import com.relle.dto.AppServiceItemDTO;
import com.relle.enums.ActivityCategoryEnum;
import com.relle.enums.CouponCheckoutSourceEnum;
import com.relle.enums.CouponStatusEnum;
import com.relle.enums.JobStatusEnum;
import com.relle.enums.OderRefundStatusEnum;
import com.relle.enums.OrderCategoryEnum;
import com.relle.enums.ServiceItemMediaTypeEnum;
import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.mbg.mapper.*;
import com.relle.mbg.model.*;
import com.relle.service.IAppActivityCouponService;
import com.relle.service.IAppActivityService;
import com.relle.service.IAppCouponCheckoutService;
import com.relle.service.IAppCouponService;
import com.relle.service.IAppOrderPayService;
import com.relle.service.IAppOrderRecommendService;
import com.relle.service.IAppOrderRefundService;
import com.relle.service.IAppServiceBundleOrderService;
import com.relle.service.IAppServiceItemCouponService;
import com.relle.service.IAppWhiteListService;
import com.relle.service.IScheduleJobService;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class AppServiceBundleOrderServiceImpl implements IAppServiceBundleOrderService {
    @Resource
    private AppServiceBundleOrderMapper appServiceBundleOrderMapper;

    @Resource
    private AppServiceBundleSuborderMapper appServiceBundleSuborderMapper;
    @Resource
    private AppCouponCheckoutMapper appCouponCheckoutMapper;

    @Resource
    private AppCouponMapper appCouponMapper;

    @Resource
    private AppOrderPayMapper appOrderPayMapper;

    @Resource
    private AppStoreInfoMapper appStoreInfoMapper;

    @Resource
    private AppServiceItemMapper appServiceItemMapper;
    @Resource
    private AppServiceMediaRelationMapper appServiceMediaRelationMapper;
    @Resource
    private AppServiceMediaMapper appServiceMediaMapper;
    @Resource
    private IScheduleJobService iScheduleJobService;
    @Resource
    private SysTaskMapper sysTaskMapper;

    @Resource
    private IAppOrderRefundService iAppOrderRefundService;
    @Resource
    private IAppOrderPayService iAppOrderPayService;
    @Resource
    private IAppServiceItemCouponService iAppServiceItemCouponService;

    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;

    @Resource
    private AppStoreRoomMapper appStoreRoomMapper;
    @Resource
    private IAppOrderRecommendService iAppOrderRecommendService;
    @Resource
    private IAppActivityService iAppActivityService;
    @Resource
    private IAppActivityCouponService iAppActivityCouponService;
    @Resource
    private IAppCouponService iAppCouponService;
    @Resource
    private IAppWhiteListService whiteListService;

    @Override
    @Transactional
    public CommonResult<?> createOrder(Map<String,Object> orderMap) {
        if(_validParams(orderMap)){
            return _saveOrder(orderMap);
        } else {
            return CommonResult.failed(1000,"参数错误");
        }
    }


    private CommonResult<?> _saveOrder(Map<String,Object> orderMap) {

        String storeId = (String)orderMap.get("storeId");
        String serviceItemId = (String)orderMap.get("serviceItemId");
        Number serviceItemOriginAmount = (Number)orderMap.get("serviceItemOriginAmount");
        Number serviceItemReductionAmount = (Number)orderMap.get("serviceItemReductionAmount");
        Number serviceItemAmount = (Number)orderMap.get("serviceItemAmount");
        String unionid = (String)orderMap.get("unionid");
        String orderId = (String)orderMap.get("orderId");

        String appId = (String)orderMap.get("appId");
        String timeStamp = (String)orderMap.get("timeStamp");
        String nonceStr = (String)orderMap.get("nonceStr");
        String packageStr = (String)orderMap.get("package");
        String signType = (String)orderMap.get("signType");
        String paySign = (String)orderMap.get("paySign");

        Number couponId = (Number)orderMap.get("couponId");

        Short orderStatus = (Short)orderMap.get("orderStatus");
        //代金券的订单直接显示完成
        //orderStatus = ServiceOrderStatusEnum.FINISHED.getCode();

        AppServiceBundleOrder order = new AppServiceBundleOrder();
        order.setOrderId(orderId);
        order.setUnionid(unionid);
        order.setSotreId(storeId);
        //order.setOrderStatus(orderStatus);
        order.setOrderAmount(BigDecimal.valueOf(Double.valueOf(serviceItemAmount.doubleValue())));
        order.setOriginPrice(BigDecimal.valueOf(Double.valueOf(serviceItemOriginAmount.doubleValue())));
        order.setReductionAmount(BigDecimal.valueOf(Double.valueOf(serviceItemReductionAmount.doubleValue())));

        order.setCreateBy(unionid);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateBy(unionid);
        order.setUpdateTime(LocalDateTime.now());

        appServiceBundleOrderMapper.insertSelective(order);

        AppServiceBundleSuborder suborder = new AppServiceBundleSuborder();
        suborder.setOrderId(orderId);
        suborder.setSuborderId(orderId+"01");
        suborder.setUnionid(unionid);
        suborder.setStoreId(storeId);
        suborder.setServiceItemId(serviceItemId);
        suborder.setSuborderStatus(orderStatus);

        suborder.setSuborderAmount(BigDecimal.valueOf(Double.valueOf(serviceItemAmount.doubleValue())));
        suborder.setSuborderOriginPrice(BigDecimal.valueOf(Double.valueOf(serviceItemOriginAmount.doubleValue())));
        suborder.setSuborderReductionAmount(BigDecimal.valueOf(Double.valueOf(serviceItemReductionAmount.doubleValue())));

        suborder.setCreateBy(unionid);
        suborder.setCreateTime(LocalDateTime.now());
        suborder.setUpdateBy(unionid);
        suborder.setUpdateTime(LocalDateTime.now());

        //

        appServiceBundleSuborderMapper.insertSelective(suborder);

        //保存支付情况
       /* if(!StringUtils.isEmpty(packageStr)) {
            AppOrderPay pay = new AppOrderPay();
            pay.setOrderId(orderId);
            pay.setPayChanelId("wechat2");
            pay.setPayAmount(BigDecimal.valueOf(Double.valueOf(serviceItemAmount.doubleValue())));
            pay.setPayTime(new Date());
            pay.setPayStatus((byte) 0);
            pay.setPayEndTime(new Date(new Date().getTime() + 15 * 60 * 1000));
            pay.setPayParamsPackage(packageStr);
            pay.setPayParamsPaysign(paySign);
            pay.setPayParamsSigntype(signType);
            pay.setPayParamsTimestamp(timeStamp);
            pay.setPayPramsAppid(appId);
            pay.setPayPraramsNoncestr(nonceStr);

            pay.setCreateBy(unionid);
            pay.setCreateTime(new Date());
            pay.setUpdateBy(unionid);
            pay.setUpdateTime(new Date());
            appOrderPayMapper.insertSelective(pay);
        }*/

        //消费优惠券
        if(couponId!=null) {
            AppCouponCheckout appCouponCheckout = new AppCouponCheckout();
            appCouponCheckout.setId(couponId.longValue());
            appCouponCheckout.setUseCouponOrderid(orderId);
            appCouponCheckout.setUseCouponTime(LocalDateTime.now());
            appCouponCheckout.setCouponStatus((byte) 2);
            appCouponCheckoutMapper.updateByPrimaryKeySelective(appCouponCheckout);//updateByExampleSelective()
        }

        String employeeId = (String)orderMap.get("employeeId");
        if(!StringUtils.isEmpty(employeeId)){
            iAppOrderRecommendService.save(orderId, unionid,
                    BigDecimal.valueOf(Double.valueOf(serviceItemAmount.doubleValue())),
                    employeeId,
                    OrderCategoryEnum.TEAM.getCode());
        }

        /*Calendar now = Calendar.getInstance();
        now.add(Calendar.MINUTE,15);

        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH)+1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        int hour = now.get(Calendar.HOUR_OF_DAY);
        int minute = now.get(Calendar.MINUTE);
        int second = now.get(Calendar.SECOND);

        StringBuilder cronExpression = new StringBuilder();
        cronExpression.append(second);
        cronExpression.append(" ");
        cronExpression.append(minute);
        cronExpression.append(" ");
        cronExpression.append(hour);
        cronExpression.append(" ");
        cronExpression.append(day);
        cronExpression.append(" ");
        cronExpression.append(month);
        cronExpression.append(" ");
        cronExpression.append(" ? ");
        cronExpression.append(year);

        SysTask task = new SysTask();
        task.setJobName(orderId);
        task.setBeanClass("com.relle.task.AutoCloseBundleOrderJob");
        task.setJobGroup("task-close");
        task.setCronExpression(cronExpression.toString());
        task.setJobParams(orderId);

        iScheduleJobService.addJob(task,true);*/


        return CommonResult.succeeded("");
    }
    @Override
    public AppServiceBundleOrder getOrder(String orderId) {
        AppServiceBundleOrderExample example = new AppServiceBundleOrderExample();
        example.createCriteria()
                .andOrderIdEqualTo(orderId);
        List<AppServiceBundleOrder> orders = appServiceBundleOrderMapper.selectByExample(example);
        if(orders.isEmpty()){
            return null;
        }
        AppServiceBundleOrder order = orders.get(0);
        return order;
    }
    private boolean _validParams(Map<String,Object> orderMap){
        if(orderMap==null || orderMap.isEmpty()){
            return false;
        }
        String storeId = (String)orderMap.get("storeId");
        String serviceItemId = (String)orderMap.get("serviceItemId");
        Number serviceItemOriginAmount = (Number)orderMap.get("serviceItemOriginAmount");
        // Number serviceItemReductionAmount = (Number)orderMap.get("serviceItemReductionAmount");
        Number serviceItemAmount = (Number)orderMap.get("serviceItemAmount");

        if(StringUtils.isEmpty(storeId)){
            return false;
        }

        if(StringUtils.isEmpty(serviceItemId)){
            return false;
        }

        if(StringUtils.isEmpty(serviceItemOriginAmount)){
            return false;
        }

        if(StringUtils.isEmpty(serviceItemAmount)){
            return false;
        }

        return true;
    }
    public AppServiceBundleOrder _getOrder(String orderId){
        AppServiceBundleOrderExample example = new AppServiceBundleOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceBundleOrder> list = appServiceBundleOrderMapper.selectByExample(example);
        if(list.isEmpty()){
            return null;
        }
        return list.get(0);
    }
    @Override
    public  List<AppServiceBundleOrderDTO> getOrderList(String unionid) {
        AppServiceBundleOrderExample example = new AppServiceBundleOrderExample();
        example.createCriteria().andUnionidEqualTo(unionid);
        example.setOrderByClause(" create_time desc ");

        List<AppServiceBundleOrder> list = appServiceBundleOrderMapper.selectByExample(example);
        List<AppServiceBundleOrderDTO> rList = new ArrayList<>();
        for (AppServiceBundleOrder order:list) {
            AppServiceBundleOrderDTO dto = new AppServiceBundleOrderDTO();
            dto.setOrder(order);

            String createUser = order.getCreateBy();
            if(createUser.startsWith("pad-admin")){
                dto.setIsAdminAdd((byte)1);
            } else {
                dto.setIsAdminAdd((byte)0);
            }

            dto.setStoreInfo(_getByStoreId(order.getSotreId()));

            AppServiceBundleSuborderExample suborderExample = new AppServiceBundleSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(order.getOrderId());
            suborderExample.setOrderByClause(" suborder_id asc ");
            List<AppServiceBundleSuborder> suborders = appServiceBundleSuborderMapper.selectByExample(suborderExample);
            List<AppServiceBundleSuborderDTO> serviceSuborderDTOS = new ArrayList<>();
            for (AppServiceBundleSuborder suborder:suborders) {
                AppServiceBundleSuborderDTO serviceSuborderDTO = new AppServiceBundleSuborderDTO();
                serviceSuborderDTO.setSuborder(suborder);
                serviceSuborderDTO.setServiceItem(_getOneDTOByServiceItemId(suborder.getServiceItemId()));
                serviceSuborderDTOS.add(serviceSuborderDTO);
            }
            dto.setSuborders(serviceSuborderDTOS);
            if(order.getOrderStatus()== ServiceOrderStatusEnum.WAIT_PAY.getCode()){
                dto.setPay(iAppOrderPayService.getPay(order.getOrderId()));
            }
            if(order.getOrderStatus()==ServiceOrderStatusEnum.CANCELED.getCode() ||
                    order.getOrderStatus()==ServiceOrderStatusEnum.CANCELING.getCode() ||
                    order.getOrderStatus()==ServiceOrderStatusEnum.PART_CANCELED.getCode() ||
                    order.getOrderStatus()==ServiceOrderStatusEnum.CANCEL_FAIL.getCode()){
                dto.setRefund(iAppOrderRefundService.getRefund(order.getOrderId()));
            }
            AppCouponCheckoutExample appCouponCheckoutExample = new AppCouponCheckoutExample();
            appCouponCheckoutExample.createCriteria()
                    .andUseCouponOrderidEqualTo(order.getOrderId());
                    /*.andCouponStatusEqualTo(CouponStatusEnum.NO_USE.getCode());*/
            List<AppCouponCheckout> appCouponCheckouts = appCouponCheckoutMapper.selectByExample(appCouponCheckoutExample);
            if(!appCouponCheckouts.isEmpty()){
                AppCouponCheckout appCouponCheckout = appCouponCheckouts.get(0);
                AppCouponExample appCouponExample = new AppCouponExample();
                appCouponExample.createCriteria()
                        .andCouponIdEqualTo(appCouponCheckout.getCouponId());
                List<AppCoupon> appCoupons = appCouponMapper.selectByExample(appCouponExample);
                dto.setCoupon(appCoupons.get(0));
            }
            rList.add(dto);
        }
        return rList;
    }

    private AppServiceItemDTO _getOneDTOByServiceItemId(String serviceItemId) {

        AppServiceItemDTO dto = new AppServiceItemDTO();

        AppServiceItem item =  _getOneByServiceItemId(serviceItemId);
        dto.setAppServiceItem(item);

        dto.setThumbnail(_getOneServiceMediaRelation(serviceItemId, ServiceItemMediaTypeEnum.THUMBNAIL.getCode()));

        return dto;
    }

    private List<AppServiceMedia> _getServiceMediaRelation(String serviceId,String typeCode){
        AppServiceMediaRelationExample appServiceMediaRelationExample = new AppServiceMediaRelationExample();
        appServiceMediaRelationExample.createCriteria()
                .andServiceIdEqualTo(serviceId)
                .andMediaShowtypeEqualTo(typeCode);
        List< AppServiceMediaRelation> items = appServiceMediaRelationMapper.selectByExample(appServiceMediaRelationExample);
        List< AppServiceMedia> rList = new ArrayList<>();
        for (AppServiceMediaRelation item : items) {
            rList.add(appServiceMediaMapper.selectByPrimaryKey(item.getMediaNo()));
        }

        return rList;
    }
    private AppServiceMedia _getOneServiceMediaRelation(String serviceId,String typeCode){

        List< AppServiceMedia> items = _getServiceMediaRelation(serviceId,typeCode);
        if(items.isEmpty()){
            return null;
        }
        AppServiceMedia item = items.get(0);

        return item;
    }
    private AppServiceItem _getOneByServiceItemId(String serviceItemId) {
        AppServiceItemExample query = new AppServiceItemExample();
        query.createCriteria().andServiceIdEqualTo(serviceItemId);
        List<AppServiceItem> items = appServiceItemMapper.selectByExample(query);
        return items.get(0);
    }
    private  AppStoreInfo  _getByStoreId(String storeId) {
        AppStoreInfoExample query = new AppStoreInfoExample();
        AppStoreInfoExample.Criteria criteria =  query.createCriteria();
        criteria.andStoreIdEqualTo(storeId);
        return appStoreInfoMapper.selectByExample(query).get(0);
    }

    @Override
    public CommonResult<?> getOrderDetail(String orderId) {
        AppServiceBundleOrderExample example = new AppServiceBundleOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);

        List<AppServiceBundleOrder> appServiceOrders =  appServiceBundleOrderMapper.selectByExample(example);
        if(appServiceOrders.isEmpty()){
            return CommonResult.failed(2000,"订单未找到");
        }
        AppServiceBundleOrder order = appServiceOrders.get(0);

        AppServiceBundleOrderDTO dto = new AppServiceBundleOrderDTO();
        dto.setOrder(order);

        String createUser = order.getCreateBy();
        if(createUser.startsWith("pad-admin")){
            dto.setIsAdminAdd((byte)1);
        } else {
            dto.setIsAdminAdd((byte)0);
        }

        dto.setStoreInfo(_getByStoreId(order.getSotreId()));

        AppServiceBundleSuborderExample suborderExample = new AppServiceBundleSuborderExample();
        suborderExample.createCriteria().andOrderIdEqualTo(order.getOrderId());
        suborderExample.setOrderByClause(" suborder_id asc ");
        List<AppServiceBundleSuborder> suborders = appServiceBundleSuborderMapper.selectByExample(suborderExample);
        List<AppServiceBundleSuborderDTO> serviceSuborderDTOS = new ArrayList<>();
        for (AppServiceBundleSuborder suborder:suborders) {
            AppServiceBundleSuborderDTO serviceSuborderDTO = new AppServiceBundleSuborderDTO();
            serviceSuborderDTO.setSuborder(suborder);
            serviceSuborderDTO.setServiceItem(_getOneDTOByServiceItemId(suborder.getServiceItemId()));
            serviceSuborderDTOS.add(serviceSuborderDTO);
        }
        dto.setSuborders(serviceSuborderDTOS);
        if(order.getOrderStatus()== ServiceOrderStatusEnum.WAIT_PAY.getCode()){
            dto.setPay(iAppOrderPayService.getPay(order.getOrderId()));
        }
        if(order.getOrderStatus()==ServiceOrderStatusEnum.CANCELED.getCode() ||
                order.getOrderStatus()==ServiceOrderStatusEnum.CANCELING.getCode() ||
                order.getOrderStatus()==ServiceOrderStatusEnum.PART_CANCELED.getCode() ||
                order.getOrderStatus()==ServiceOrderStatusEnum.CANCEL_FAIL.getCode()){
            dto.setRefund(iAppOrderRefundService.getRefund(order.getOrderId()));
        }
        AppCouponCheckoutExample appCouponCheckoutExample = new AppCouponCheckoutExample();
        appCouponCheckoutExample.createCriteria()
                .andUseCouponOrderidEqualTo(order.getOrderId());
        /*.andCouponStatusEqualTo(CouponStatusEnum.NO_USE.getCode());*/
        List<AppCouponCheckout> appCouponCheckouts = appCouponCheckoutMapper.selectByExample(appCouponCheckoutExample);
        if(!appCouponCheckouts.isEmpty()){
            AppCouponCheckout appCouponCheckout = appCouponCheckouts.get(0);
            AppCouponExample appCouponExample = new AppCouponExample();
            appCouponExample.createCriteria()
                    .andCouponIdEqualTo(appCouponCheckout.getCouponId());
            List<AppCoupon> appCoupons = appCouponMapper.selectByExample(appCouponExample);
            dto.setCoupon(appCoupons.get(0));
        }

        return CommonResult.succeeded(dto);
    }

    @Override
    public AppServiceBundleOrderDTO getOrderDetailDTO(String orderId) {
        AppServiceBundleOrderExample example = new AppServiceBundleOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);

        List<AppServiceBundleOrder> appServiceOrders =  appServiceBundleOrderMapper.selectByExample(example);
        if(appServiceOrders.isEmpty()){
            return null;
        }
        AppServiceBundleOrder order = appServiceOrders.get(0);

        AppServiceBundleOrderDTO dto = new AppServiceBundleOrderDTO();
        dto.setOrder(order);

        AppServiceBundleSuborderExample suborderExample = new AppServiceBundleSuborderExample();
        suborderExample.createCriteria().andOrderIdEqualTo(order.getOrderId());
        suborderExample.setOrderByClause(" suborder_id asc ");
        List<AppServiceBundleSuborder> suborders = appServiceBundleSuborderMapper.selectByExample(suborderExample);
        List<AppServiceBundleSuborderDTO> serviceSuborderDTOS = new ArrayList<>();
        for (AppServiceBundleSuborder suborder:suborders) {
            AppServiceBundleSuborderDTO serviceSuborderDTO = new AppServiceBundleSuborderDTO();
            serviceSuborderDTO.setSuborder(suborder);
            serviceSuborderDTO.setServiceItem(_getOneDTOByServiceItemId(suborder.getServiceItemId()));
            serviceSuborderDTOS.add(serviceSuborderDTO);
        }
        dto.setSuborders(serviceSuborderDTOS);

        return dto;
    }

    @Override
    public int paySuccess(String orderId,String successTime,String transaction_id) {

        int update = updateOrderStatus(orderId,ServiceOrderStatusEnum.FINISHED.getCode(), "paySuccess");
        if(update==0){
            return update;
        }

        //保存支付情况
        AppOrderPay pay = new AppOrderPay();
        pay.setPayTime(LocalDateTime.parse(successTime));
        pay.setPayStatus((byte)1);
        AppOrderPayExample example = new AppOrderPayExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        appOrderPayMapper.updateByExampleSelective(pay,example);

        //关闭超时关闭订单操作定时任务
        SysTaskExample sysTaskExample =  new SysTaskExample();
        sysTaskExample.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
        // 这里获取任务信息数据
        List<SysTask> jobList = sysTaskMapper.selectByExample(sysTaskExample);
        for(SysTask sysTask : jobList){
            try {
                iScheduleJobService.deleteJob(sysTask);

                sysTask.setJobStatus(JobStatusEnum.CLOSED.getCode());
                SysTaskExample example1 = new SysTaskExample();
                example1.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
                sysTaskMapper.updateByExampleSelective(sysTask,example1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


        //发送卡券
        List<AppServiceBundleSuborder> suborders = getItemIdByOrderId(orderId);
        for (AppServiceBundleSuborder bundleSuborder : suborders) {
            List<AppServiceItemCoupon> listByServiceItem = iAppServiceItemCouponService.getListByServiceItem(bundleSuborder.getServiceItemId());
            AppWhiteList whiteList = whiteListService.getWhiteListByUnionid(bundleSuborder.getUnionid(), bundleSuborder.getStoreId(), (byte) 1);

            for (AppServiceItemCoupon appServiceItemCoupon : listByServiceItem) {

                if(bundleSuborder.getServiceItemId().startsWith("GD")
                        && whiteList!=null
                        && !"*".equals(whiteList.getStoreId())){
                    iAppCouponCheckoutService.grant(bundleSuborder.getStoreId(),appServiceItemCoupon.getCouponId()
                            ,bundleSuborder.getUnionid()
                            ,appServiceItemCoupon.getCouponNum(),CouponStatusEnum.NO_USE.getCode(),CouponCheckoutSourceEnum.USER_BUY.getCode(),bundleSuborder.getOrderId());
                } else {
                    iAppCouponCheckoutService.grant(appServiceItemCoupon.getCouponId()
                            , bundleSuborder.getUnionid()
                            , appServiceItemCoupon.getCouponNum(), CouponStatusEnum.NO_USE.getCode(), CouponCheckoutSourceEnum.USER_BUY.getCode(), bundleSuborder.getOrderId());
                }
            }
        }
        //判断是否有消费返利活动
        AppServiceBundleOrder order = getOrder(orderId);
        AppServiceBundleSuborder suborder = getSuborder(orderId);
        if(!suborder.getServiceItemId().startsWith("GD")) {
            List<AppActivity> appActivities = iAppActivityService.getActivityListByCategory(order.getSotreId(), ActivityCategoryEnum.REBATE_SERVICE.getCode());
            if (!appActivities.isEmpty()) {
                for (int i = 0; i < appActivities.size(); i++) {
                    AppActivity appActivity = appActivities.get(i);
                    List<AppActivityCoupon> coupons = iAppActivityCouponService.getByActivity(appActivity.getId());
                    for (AppActivityCoupon activityCoupon : coupons) {
                        AppCoupon coupon = iAppCouponService.getCouponById(activityCoupon.getCouponId());
                        iAppCouponCheckoutService.grant(
                                coupon.getCouponId(),
                                order.getUnionid(),
                                1,
                                CouponStatusEnum.NO_USE.getCode(),
                                CouponCheckoutSourceEnum.REBATE_SERVICE.getCode(),
                                order.getOrderId());

                    }
                }
            }
        }
        return 1;
    }

    @Override
    public int refundResult(String orderId, byte refundStatus,String successTime, String refundOrderId) {
        //查询退款订单信息
        List<AppOrderRefund> refunds = iAppOrderRefundService.getRefundList(orderId);
        AppOrderRefund refund_this = null;
        for (AppOrderRefund refund: refunds) {
            if(refund.getRefundOrderId().equals(refundOrderId)){
                refund_this = refund;
            }
        }

        short orderStatus = ServiceOrderStatusEnum.CANCELED.getCode();

        if(refund_this.getOrderAmount().doubleValue()<refund_this.getRefundAmount().doubleValue()){
            orderStatus = ServiceOrderStatusEnum.PART_CANCELED.getCode();
        }
        int update = updateOrderStatus(orderId,orderStatus, "refundNotifyUrl");

        AppOrderRefund refund = new AppOrderRefund();
        refund.setRefundStatus(OderRefundStatusEnum.REFUND_FINISHED.getCode());

        return iAppOrderRefundService.updateRefund(refund,refundOrderId);
    }

    @Override
    public int updateOrderStatus(String orderId,short orderStatus,String updateBy){
        AppServiceBundleOrder order = new  AppServiceBundleOrder();
        order.setOrderStatus(orderStatus);
        order.setUpdateBy(updateBy);

        AppServiceBundleOrderExample example = new AppServiceBundleOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        int update = appServiceBundleOrderMapper.updateByExampleSelective(order,example);
        if(update>0){
            AppServiceBundleSuborder suborder = new  AppServiceBundleSuborder();
            suborder.setSuborderStatus(orderStatus);
            suborder.setUpdateBy(updateBy);

            AppServiceBundleSuborderExample suborderExample = new AppServiceBundleSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(orderId);
            return update = appServiceBundleSuborderMapper.updateByExampleSelective(suborder,suborderExample);
        } else {
            return 0;
        }
    }

    @Override
    public CommonResult<?> cancelOrder(String orderId,String unionid) {

        return CommonResult.succeeded(updateOrderStatus(orderId,ServiceOrderStatusEnum.CANCELING.getCode(), unionid));
    }

    @Override
    public CommonResult<?> refundOrder(String orderId,String unionid,String refundOrderId,double refundAmount,String refundReason) {
        AppServiceBundleOrder order = _getOrder(orderId);
        int update = updateOrderStatus(orderId,ServiceOrderStatusEnum.CANCELING.getCode(), unionid);
        iAppCouponCheckoutService.backCoupon(unionid,order.getOrderId());
        iAppOrderRefundService.createRefund(orderId,order.getOrderAmount(),refundOrderId,refundAmount,refundReason,unionid);
        return CommonResult.succeeded("");
    }

    private  AppServiceBundleSuborder getSuborder(String orderId){
        AppServiceBundleSuborderExample example = new AppServiceBundleSuborderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceBundleSuborder> list = appServiceBundleSuborderMapper.selectByExample(example);
        if(list.isEmpty()){
            return null;
        }
        return list.get(0);
    }
    public boolean checkOrder(String orderId, String unionid){
        //第一，校验此订单是否此用户的,todo 暂时不处理此块内容，便于测试期间异常退单
        //核实订单状态，并确认在规定时间内
        AppServiceBundleOrder order = _getOrder(orderId);
        if(order==null){
            return false;
        }
        if(order.getOrderStatus()!=ServiceOrderStatusEnum.FINISHED.getCode()){
            return false;
        }
        AppServiceBundleSuborder suborder = getSuborder(order.getOrderId());

        return true;
    }

    @Override
    public boolean checkOrderAdmin(String orderId, String unionid) {
        return false;
    }

    @Override
    public List<AppServiceBundleSuborder> getItemIdByOrderId(String orderId) {
        AppServiceBundleSuborderExample suborderExample = new AppServiceBundleSuborderExample();
        suborderExample.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceBundleSuborder> suborders = appServiceBundleSuborderMapper.selectByExample(suborderExample);
       return suborders;
    }


    @Override
    public CommonResult<?> updateBookTime(String orderId, String unionid, LocalDateTime bookTime) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'updateBookTime'");
    }

}
