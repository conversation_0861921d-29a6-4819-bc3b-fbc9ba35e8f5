package com.relle.service.impl;

import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.mbg.mapper.AppServiceSuborderMapper;
import com.relle.mbg.model.AppServiceSuborder;
import com.relle.mbg.model.AppServiceSuborderExample;
import com.relle.service.IAppServiceSuborderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class AppServiceSuborderServiceImpl implements IAppServiceSuborderService {
    @Resource
    private AppServiceSuborderMapper appServiceSuborderMapper;
    public List<AppServiceSuborder> getValidSuborderByDate(String storeId,LocalDateTime start, LocalDateTime end){
        List<Short> shortList = new ArrayList<>();
        shortList.add(ServiceOrderStatusEnum.PAY_SUCCESS.getCode());
        shortList.add(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        AppServiceSuborderExample example = new AppServiceSuborderExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andBookTimeBetween(start,end)
                .andSuborderStatusIn(shortList);

        return appServiceSuborderMapper.selectByExample(example);
    }

    @Override
    public List<AppServiceSuborder> getValidSuborderByDate(String storeId,String start, String end) {
        return null;
    }

    @Override
    public List<AppServiceSuborder> getValidSuborderByDate(String storeId, LocalDate date) {
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.atTime(23,59,59);
        return getValidSuborderByDate(storeId,start,end);
    }

    @Override
    public List<AppServiceSuborder> getValidSuborderByDate(String storeId,String day) {
        return null;
    }

    @Override
    public AppServiceSuborder getSuborderBySuborderId(String suborderId) {
        AppServiceSuborderExample example = new AppServiceSuborderExample();
        example.createCriteria()
                .andSuborderIdEqualTo(suborderId);
        List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(example);
        if(suborders.isEmpty()){
            return null;
        }
        return suborders.get(0);
    }
}
