package com.relle.service.impl;

import com.relle.dto.EmployeeDTO;
import com.relle.mbg.mapper.AppEmployeeMapper;
import com.relle.mbg.model.AppEmployee;
import com.relle.mbg.model.AppEmployeeExample;
import com.relle.mbg.model.AppStoreEmployeeRelation;
import com.relle.service.IAppEmployeeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppEmployeeServiceImpl implements IAppEmployeeService {
    @Resource
    private AppEmployeeMapper appEmployeeMapper;
    @Override
    public AppEmployee getAppEmployeeByOpenId(String storeId,String openId) {
        AppEmployeeExample example = new AppEmployeeExample();
        example.createCriteria()
                .andEmployeeOpenIdEqualTo(openId).andDeletedEqualTo((byte)0);
        List<AppEmployee> appEmployees = appEmployeeMapper.selectByExample(example);
        if((appEmployees.isEmpty())){
            return null;
        }
        return appEmployees.get(0);
    }
    @Override
    public AppEmployee getAppEmployeeByOpenId(String openid) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAppEmployeeByOpenId'");
    }
    @Override
    public List<AppStoreEmployeeRelation> getEmployeeRelationByEmployeeId(String employeeId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getEmployeeRelationByEmployeeId'");
    }
    @Override
    public void insert(AppEmployee appEmployee) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'insert'");
    }
    @Override
    public void insertRalation(String employeeId, String string, Object roleCode) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'insertRalation'");
    }
    @Override
    public AppEmployee getAppEmployeeById(Long id) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAppEmployeeById'");
    }
    @Override
    public void update(AppEmployee appEmployee) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'update'");
    }
    @Override
    public void deleteRalationByEmployeeId(String employeeId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'deleteRalationByEmployeeId'");
    }
    @Override
    public List<AppEmployee> getAll(EmployeeDTO query) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAll'");
    }
}
