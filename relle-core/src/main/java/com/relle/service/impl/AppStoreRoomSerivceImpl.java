package com.relle.service.impl;

import com.relle.mbg.mapper.AppStoreRoomMapper;
import com.relle.mbg.model.AppStoreRoom;
import com.relle.mbg.model.AppStoreRoomExample;
import com.relle.service.IAppStoreRoomSerivce;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppStoreRoomSerivceImpl implements IAppStoreRoomSerivce {
    @Resource
    private AppStoreRoomMapper appStoreRoomMapper;
    @Override
    public List<AppStoreRoom> getRoomListByStore(String storeId) {
        AppStoreRoomExample example = new AppStoreRoomExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andDeletedEqualTo((byte)0);

        return appStoreRoomMapper.selectByExample(example);
    }
}
