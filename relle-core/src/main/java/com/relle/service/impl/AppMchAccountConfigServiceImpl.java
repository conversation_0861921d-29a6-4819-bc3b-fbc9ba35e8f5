package com.relle.service.impl;

import com.relle.mbg.mapper.AppMchAccountConfigMapper;
import com.relle.mbg.model.AppMchAccountConfig;
import com.relle.mbg.model.AppMchAccountConfigExample;
import com.relle.service.IAppMchAccountConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppMchAccountConfigServiceImpl implements IAppMchAccountConfigService {
    @Resource
    private AppMchAccountConfigMapper appMchAccountConfigMapper;
    public List<AppMchAccountConfig> getAll(){
        AppMchAccountConfigExample example = new AppMchAccountConfigExample();
        return appMchAccountConfigMapper.selectByExample(example);
    }
}
