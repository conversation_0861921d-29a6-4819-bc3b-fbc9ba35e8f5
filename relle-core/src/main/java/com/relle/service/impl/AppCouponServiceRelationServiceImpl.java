package com.relle.service.impl;

import com.relle.mbg.mapper.AppCouponMapper;
import com.relle.mbg.mapper.AppCouponServiceRelationMapper;
import com.relle.mbg.model.*;
import com.relle.service.IAppCouponServiceRelationService;
import com.relle.service.IAppServiceItemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class AppCouponServiceRelationServiceImpl implements IAppCouponServiceRelationService {
    @Resource
    private AppCouponServiceRelationMapper appCouponServiceRelationMapper;
    @Resource
    private AppCouponMapper appCouponMapper;
    @Resource
    private IAppServiceItemService iAppServiceItemService;
    @Override
    public List<AppCoupon> getCouponByServiceItemId(String serviceItemId) {
        AppCouponServiceRelationExample example = new AppCouponServiceRelationExample();
        example.createCriteria()
                .andServiceItemIdEqualTo(serviceItemId)
                .andDeletedEqualTo((byte)0);
        List<AppCouponServiceRelation> appCouponServiceRelations = appCouponServiceRelationMapper.selectByExample(example);
        List<AppCoupon> appCoupons = new ArrayList<>();
        if(appCouponServiceRelations.isEmpty()){
            return appCoupons;
        }
        for (AppCouponServiceRelation r :  appCouponServiceRelations ) {
            AppCouponExample appCouponExample = new AppCouponExample();
            appCouponExample.createCriteria()
                    .andCouponIdEqualTo(r.getCouponId());

            List<AppCoupon> appCouponList = appCouponMapper.selectByExample(appCouponExample);
            if(!appCouponList.isEmpty()){
                appCoupons.add(appCouponList.get(0));
            }
        }

        return appCoupons;
    }

    @Override
    public List<AppServiceItem> getServiceItemByCouponId(String couponId) {
        AppCouponServiceRelationExample example = new AppCouponServiceRelationExample();
        example.createCriteria()
                .andCouponIdEqualTo(couponId)
                .andDeletedEqualTo((byte)0);
        List<AppCouponServiceRelation> appCouponServiceRelations = appCouponServiceRelationMapper.selectByExample(example);
        List<AppServiceItem> appServiceItems = new ArrayList<>();
        if(appCouponServiceRelations.isEmpty()){
            return null;
        }
        for (AppCouponServiceRelation r : appCouponServiceRelations ) {

            String serviceItemId = r.getServiceItemId();
            if("all".equals(serviceItemId)){
                AppServiceItem item = new AppServiceItem();
                item.setServiceName("所有项目");
                appServiceItems.clear();
                appServiceItems.add(item);
                return appServiceItems;
            } else {
                AppServiceItem item = iAppServiceItemService.getItem(serviceItemId);
                if(item==null){
                    continue;
                }
                appServiceItems.add(item);
            }
        }
        return appServiceItems;
    }

    @Override
    public boolean canUseThisServiceItem(String serviceItemId, String couponId) {
        AppCouponServiceRelationExample example = new AppCouponServiceRelationExample();
        example.createCriteria()
                .andServiceItemIdEqualTo(serviceItemId)
                .andCouponIdEqualTo(couponId)
                .andDeletedEqualTo((byte)0);

        AppCouponServiceRelationExample.Criteria criteria2 = example.createCriteria();
        criteria2.andServiceItemIdEqualTo("all")
                .andCouponIdEqualTo(couponId)
                .andDeletedEqualTo((byte)0);

        example.or(criteria2);

        List<AppCouponServiceRelation> appCouponServiceRelations = appCouponServiceRelationMapper.selectByExample(example);

        if(appCouponServiceRelations.isEmpty()){
            return false;
        }
        return true;
    }
}
