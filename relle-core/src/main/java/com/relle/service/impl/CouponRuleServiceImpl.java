package com.relle.service.impl;

import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.mbg.mapper.*;
import com.relle.mbg.model.*;
import com.relle.service.IAppCouponCheckoutService;
import com.relle.service.IAppCustomerInfoService;
import com.relle.service.ICouponRuleService;

import org.apache.tomcat.jni.Local;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class CouponRuleServiceImpl implements ICouponRuleService {
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;
    @Resource
    private IAppCouponCheckoutService IAppCouponCheckoutService;
    @Resource
    private AppCouponMapper appCouponMapper;
    @Resource
    private AppServiceOrderMapper appServiceOrderMapper;
    @Resource
    private AppServiceBundleOrderMapper appServiceBundleOrderMapper;
    @Resource
    private AppServiceTeamOrderMapper appServiceTeamOrderMapper;

    @Resource
    private AppActivityCouponMapper appActivityCouponMapper;

    @Override
    public boolean isNew(String unionid) {
        //判断是否已经下过单,未支付的不算
        AppServiceOrderExample example = new AppServiceOrderExample();
        example.createCriteria()
                .andUnionidEqualTo(unionid)
                .andOrderStatusGreaterThanOrEqualTo(ServiceOrderStatusEnum.WAIT_PAY.getCode())
                .andOrderStatusLessThan(ServiceOrderStatusEnum.CANCELED.getCode());
        List<AppServiceOrder> list = appServiceOrderMapper.selectByExample(example);

        AppServiceBundleOrderExample example2 = new AppServiceBundleOrderExample();
        example2.createCriteria()
                .andUnionidEqualTo(unionid)
                .andOrderStatusGreaterThanOrEqualTo(ServiceOrderStatusEnum.WAIT_PAY.getCode())
                .andOrderStatusLessThan(ServiceOrderStatusEnum.CANCELED.getCode());
        List<AppServiceBundleOrder> list2 = appServiceBundleOrderMapper.selectByExample(example2);

        AppServiceTeamOrderExample example3 = new AppServiceTeamOrderExample();
        example3.createCriteria()
                .andUnionidEqualTo(unionid)
                .andOrderStatusGreaterThanOrEqualTo(ServiceOrderStatusEnum.WAIT_PAY.getCode())
                .andOrderStatusLessThan(ServiceOrderStatusEnum.CANCELED.getCode());
        List<AppServiceTeamOrder> list3 = appServiceTeamOrderMapper.selectByExample(example3);

        return list.isEmpty()&&list2.isEmpty()&&list3.isEmpty();
    }

   @Override
    public boolean isCX(String birthdayStr, LocalDate startDate,LocalDate endDate) {
        //判断用户生日是否在星座范围内
        LocalDate birthDate = LocalDate.parse(birthdayStr);
        if (birthDate.isAfter(startDate) && birthDate.isBefore(endDate)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isOnlyOne(String unionid,Long couponId) {
        List<AppCouponCheckout> listByUnionid = IAppCouponCheckoutService.getListByUnionid(unionid);
        AppCoupon coupon =  appCouponMapper.selectByPrimaryKey(couponId);

        for (AppCouponCheckout checkout:listByUnionid) {
            if(checkout.getCouponId().equals(coupon.getCouponId())){
                return false;
            }
        }
        return true;
    }

    @Override
    public List<AppActivityCoupon> getActivityCoupon(Long activityId) {
        AppActivityCouponExample example = new AppActivityCouponExample();
        example.createCriteria()
                .andActivityIdEqualTo(activityId);

        List<AppActivityCoupon> coupons = appActivityCouponMapper.selectByExample(example);
        return coupons;
    }
}
