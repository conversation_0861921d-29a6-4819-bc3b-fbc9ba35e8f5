package com.relle.service.impl;

import com.relle.mbg.mapper.AppActivityQrcodeMapper;
import com.relle.mbg.model.AppActivityQrcode;
import com.relle.mbg.model.AppActivityQrcodeExample;
import com.relle.service.IAppActivityQrcodeService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppActivityQrcodeServiceImpl implements IAppActivityQrcodeService {
    @Resource
    private AppActivityQrcodeMapper appActivityQrcodeMapper;
    @Override
    public List<AppActivityQrcode> getQrcodeByStatus(String storeId,String activityId, byte status) {
        AppActivityQrcodeExample example = new AppActivityQrcodeExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andActivityIdEqualTo(activityId)
                .andQrcodeStatusEqualTo(status);
        return appActivityQrcodeMapper.selectByExample(example);
    }

    @Override
    public AppActivityQrcode getQrcodeByQrcodeParams(String storeId,String activityId,String qrcodeParams) {
        AppActivityQrcodeExample example = new AppActivityQrcodeExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andActivityIdEqualTo(activityId)
                .andQrcodeParamsEqualTo(qrcodeParams);
        List<AppActivityQrcode> appActivityQrcodes = appActivityQrcodeMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(appActivityQrcodes)){
            return null;
        }
        return appActivityQrcodes.get(0);
    }

    @Override
    public int update(String storeId,String activityId,AppActivityQrcode appActivityQrcode) {
        AppActivityQrcodeExample example = new AppActivityQrcodeExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andActivityIdEqualTo(activityId)
                .andQrcodeParamsEqualTo(appActivityQrcode.getQrcodeParams());

        return appActivityQrcodeMapper.updateByExampleSelective(appActivityQrcode,example);
    }

    @Override
    public int updateQrcodeStatus(String storeId, String activityId, String unionid,byte qrcodeStatus) {
        AppActivityQrcodeExample example = new AppActivityQrcodeExample();
        example.createCriteria()
                .andUpdateByEqualTo(unionid)
                .andStoreIdEqualTo(storeId)
                .andActivityIdEqualTo(activityId);
        AppActivityQrcode qrcode = new AppActivityQrcode();
        qrcode.setQrcodeStatus(qrcodeStatus);
        return appActivityQrcodeMapper.updateByExampleSelective(qrcode,example);
    }
}
