package com.relle.service.impl;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppActivityCoupon;
import com.relle.service.IAppActivityCouponService;

import java.util.List;

import org.springframework.stereotype.Service;

@Service
public class AppActivityCouponServiceImpl implements IAppActivityCouponService {
    
    @Override
    public CommonResult<?> getActivityCoupons(String activityId) {
        return CommonResult.succeeded("Activity coupons for: " + activityId);
    }
    
    @Override
    public CommonResult<?> claimActivityCoupon(String activityId, String couponId, String userId) {
        return CommonResult.succeeded("User " + userId + " claimed coupon " + couponId + " from activity " + activityId);
    }

    @Override
    public List<AppActivityCoupon> getByActivity(Long id) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getByActivity'");
    }
}
