package com.relle.service.impl;

import com.relle.enums.JobStatusEnum;
import com.relle.mbg.mapper.SysTaskMapper;
import com.relle.mbg.model.SysTask;
import com.relle.mbg.model.SysTaskExample;
import com.relle.service.IScheduleJobService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ScheduleJobServiceImpl implements IScheduleJobService {

    @Resource
    private SysTaskMapper sysTaskMapper;

    @Resource
    private Scheduler scheduler;

   // @Override
    public void initSchedule()  {
        SysTaskExample sysTaskExample =  new SysTaskExample();
        sysTaskExample.createCriteria().andJobStatusEqualTo(JobStatusEnum.WAITING.getCode());
        // 这里获取任务信息数据
        List<SysTask> jobList = sysTaskMapper.selectByExample(sysTaskExample);
        for (SysTask task : jobList) {
            if (JobStatusEnum.WAITING.getCode().equals(task.getJobStatus())) {
                addJob(task,false);
            }
        }
    }

    @Override
    public void createJob(String beanClass,String jobgroup,String orderId) {
        createJob(beanClass,jobgroup,orderId,15);
    }

    @Override
    public void createJob(String beanClass, String jobgroup, String orderId, int offsetMinute) {
        java.util.Calendar now = java.util.Calendar.getInstance();
        now.add(java.util.Calendar.MINUTE,offsetMinute);

        int year = now.get(java.util.Calendar.YEAR);
        int month = now.get(java.util.Calendar.MONTH)+1;
        int day = now.get(java.util.Calendar.DAY_OF_MONTH);
        int hour = now.get(java.util.Calendar.HOUR_OF_DAY);
        int minute = now.get(java.util.Calendar.MINUTE);
        int second = now.get(Calendar.SECOND);

        StringBuilder cronExpression = new StringBuilder();
        cronExpression.append(second);
        cronExpression.append(" ");
        cronExpression.append(minute);
        cronExpression.append(" ");
        cronExpression.append(hour);
        cronExpression.append(" ");
        cronExpression.append(day);
        cronExpression.append(" ");
        cronExpression.append(month);
        cronExpression.append(" ");
        cronExpression.append(" ? ");
        cronExpression.append(year);

        SysTask task = new SysTask();
        task.setJobName(orderId);
        task.setBeanClass(beanClass);
        task.setJobGroup(jobgroup);
        task.setCronExpression(cronExpression.toString());
        task.setJobParams(orderId);

        addJob(task,true);
    }

    @SuppressWarnings("unchecked")
    public void addJob(SysTask task,Boolean needInsert ) {
        try {
            // 创建jobDetail实例，绑定Job实现类
            // 指明job的名称，所在组的名称，以及绑定job类
            Class<? extends Job> jobClass = (Class<? extends Job>) (Class.forName(task.getBeanClass()).newInstance()
                    .getClass());
            JobDetail jobDetail = JobBuilder.newJob(jobClass)
                    .withIdentity(task.getJobName(), task.getJobGroup())// 任务名称和组构成任务key
                    .usingJobData("orderId",task.getJobParams())
                    .build();
            // 定义调度触发规则
            // 使用cornTrigger规则
            Trigger trigger = TriggerBuilder.newTrigger().withIdentity(task.getJobName(), task.getJobGroup())// 触发器key
                    .startAt(DateBuilder.futureDate(1, DateBuilder.IntervalUnit.SECOND))
                    .withSchedule(CronScheduleBuilder.cronSchedule(task.getCronExpression())).startNow().build();
            // 把作业和触发器注册到任务调度中
            scheduler.scheduleJob(jobDetail, trigger);
            // 启动
            if (!scheduler.isShutdown()) {
                scheduler.start();
            }

            task.setJobStatus(JobStatusEnum.WAITING.getCode());
            if(needInsert) {
                sysTaskMapper.insertSelective(task);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void deleteJob(SysTask task) throws SchedulerException {
        JobKey jobKey = JobKey.jobKey(task.getJobName(), task.getJobGroup());
        deleteJob(jobKey);
    }

    public void deleteJob(JobKey jobKey) throws SchedulerException {
        scheduler.deleteJob(jobKey);
    }
}
