package com.relle.service.impl;

import com.relle.enums.OderRefundStatusEnum;
import com.relle.mbg.mapper.AppOrderRefundMapper;
import com.relle.mbg.model.AppOrderRefund;
import com.relle.mbg.model.AppOrderRefundExample;
import com.relle.service.IAppOrderRefundService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class AppOrderRefundServiceImpl implements IAppOrderRefundService {
    @Resource
    private AppOrderRefundMapper appOrderRefundMapper;
    @Override
    public int createRefund(String orderId,BigDecimal orderAmount,String refundOrderId,double refundAmount,String refundReason,String unionid) {
        AppOrderRefund refund = new AppOrderRefund();
        LocalDateTime now = LocalDateTime.now();
        refund.setOrderId(orderId);
        refund.setRefundOrderId(refundOrderId);
        refund.setRefundTime(now);
        refund.setOrderAmount(orderAmount);

        refund.setRefundStatus(OderRefundStatusEnum.REFUNDING.getCode());
        refund.setRefundAmount(BigDecimal.valueOf(Double.valueOf(refundAmount)));
        refund.setRefundReason(refundReason);
        refund.setCreateBy(unionid);
        refund.setUpdateBy(unionid);
        refund.setCreateTime(now);
        refund.setUpdateTime(now);
        return appOrderRefundMapper.insertSelective(refund);
    }

    @Override
    public AppOrderRefund getRefund(String orderId) {
        List<AppOrderRefund> list = getRefundList(orderId);
        if(list.isEmpty()){
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<AppOrderRefund> getRefundList(String orderId) {
        AppOrderRefundExample example = new AppOrderRefundExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        return appOrderRefundMapper.selectByExample(example);
    }

    @Override
    public int updateRefund(AppOrderRefund refund, String refundOrderId) {
        AppOrderRefundExample example = new AppOrderRefundExample();
        example.createCriteria().andRefundOrderIdEqualTo(refundOrderId);
        return appOrderRefundMapper.updateByExampleSelective(refund,example);
    }
}
