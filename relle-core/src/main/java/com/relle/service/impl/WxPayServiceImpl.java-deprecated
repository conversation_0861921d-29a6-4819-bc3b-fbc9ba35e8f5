package com.relle.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.mbg.mapper.AppOrderPayMapper;
import com.relle.mbg.model.AppOrderPay;
import com.relle.service.IOrderService;
import com.relle.service.IScheduleJobService;
import com.relle.service.IWxPayService;
import com.relle.service.PayConstantsService;
// import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
// import com.wechat.pay.contrib.apache.httpclient.auth.AutoUpdateCertificatesVerifier;
// import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
// import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
// import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
// import com.wechat.pay.contrib.apache.httpclient.util.AesUtil;
// import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
// import com.wechat.pay.java.core.WechatPayValidator;
import com.wechat.pay.java.core.auth.WechatPay2Validator;

import org.apache.commons.lang3.RandomUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
// import org.apache.http.client.methods.CloseableHttpResponse;
// import org.apache.http.client.methods.HttpGet;
// import org.apache.http.client.methods.HttpPost;
// import org.apache.http.client.utils.URIBuilder;
// import org.apache.http.entity.StringEntity;
// import org.apache.http.impl.client.CloseableHttpClient;
// import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service
public class WxPayServiceImpl implements IWxPayService {
    @Resource
    private PayConstantsService payConstants;
    @Resource
    private AppOrderPayMapper appOrderPayMapper;
    @Resource
    private IScheduleJobService iScheduleJobService;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public Map<String, Object> toPay(String storeId, Byte serviceItemCategory, String desc, Integer totalAmount,
            String outTradeNo, String openid, String autoTask, IOrderService service) {
        Map<String, Object> map = new HashMap<>();

        // 金额为0，直接支付成功
        if (totalAmount == 0) {
            String successTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+08:00"));
            int paySuccess = service.paySuccess(outTradeNo, successTime, "");
            map.put("status", 0);
            map.put("notLaunch", 1);
            return map;
        }

        try {
            map = createOrderFromJsapi(storeId, desc, totalAmount, outTradeNo, openid, serviceItemCategory);
        } catch (Exception e) {
            e.printStackTrace();
            map.put("status", 2000);
            map.put("message", "下单失败");
            return map;
        }

        String appId = (String) map.get("appId");
        String timeStamp = (String) map.get("timeStamp");
        String nonceStr = (String) map.get("nonceStr");
        String packageStr = (String) map.get("package");
        String signType = (String) map.get("signType");
        String paySign = (String) map.get("paySign");

        AppOrderPay pay = new AppOrderPay();
        LocalDateTime now = LocalDateTime.now();
        pay.setOrderId(outTradeNo);
        pay.setPayChanelId("wechat2");
        pay.setPayAmount(new BigDecimal(totalAmount * 1.0 / 100));
        pay.setPayTime(now);
        pay.setPayStatus((byte) 0);
        pay.setPayEndTime(now.plusMinutes(15));
        pay.setPayParamsPackage(packageStr);
        pay.setPayParamsPaysign(paySign);
        pay.setPayParamsSigntype(signType);
        pay.setPayParamsTimestamp(timeStamp);
        pay.setPayPramsAppid(appId);
        pay.setPayPraramsNoncestr(nonceStr);

        pay.setCreateBy(openid);
        pay.setCreateTime(now);
        pay.setUpdateBy(openid);
        pay.setUpdateTime(now);
        appOrderPayMapper.insertSelective(pay);

        iScheduleJobService.createJob(autoTask, "task-close", outTradeNo);

        return map;

    }

    public AppOrderPay createPrepay(String storeId, Byte serviceItemCategory, String desc, Integer totalAmount,
            String outTradeNo, String openid) {
        Map map = new HashMap();
        String notifyUrl = payConstants.getOthers("additionalorder_pay_notify_url").trim() + storeId + "/"
                + serviceItemCategory;
        try {
            map = createOrderFromJsapi(storeId, desc, totalAmount, outTradeNo, openid, serviceItemCategory, notifyUrl);
        } catch (Exception e) {
            e.printStackTrace();
            map.put("status", 2000);
            map.put("message", "下单失败");
            return null;
        }
        String appId = (String) map.get("appId");
        String timeStamp = (String) map.get("timeStamp");
        String nonceStr = (String) map.get("nonceStr");
        String packageStr = (String) map.get("package");
        String signType = (String) map.get("signType");
        String paySign = (String) map.get("paySign");

        AppOrderPay pay = new AppOrderPay();
        LocalDateTime now = LocalDateTime.now();
        pay.setOrderId(outTradeNo);
        pay.setPayChanelId("wechat2");
        pay.setPayAmount(new BigDecimal(totalAmount * 1.0 / 100));
        pay.setPayTime(now);
        pay.setPayStatus((byte) 0);
        pay.setPayEndTime(now.plusMinutes(15));
        pay.setPayParamsPackage(packageStr);
        pay.setPayParamsPaysign(paySign);
        pay.setPayParamsSigntype(signType);
        pay.setPayParamsTimestamp(timeStamp);
        pay.setPayPramsAppid(appId);
        pay.setPayPraramsNoncestr(nonceStr);

        pay.setCreateBy(openid);
        pay.setCreateTime(now);
        pay.setUpdateBy(openid);
        pay.setUpdateTime(now);
        appOrderPayMapper.insertSelective(pay);
        return pay;
    }

    public Map createOrderFromJsapi(String storeId, String desc, Integer totalAmount, String outTradeNo, String openid,
            Byte serviceItemCategory) throws Exception {
        return createOrderFromJsapi(storeId, desc, totalAmount, outTradeNo, openid, serviceItemCategory,
                payConstants.getPayNotifyUrl(storeId, serviceItemCategory));
    }

    /**
     *
     * @param desc        商品描述
     * @param totalAmount 总金额
     * @param outTradeNo  订单号
     * @param openid      操作人，下单人和
     * @return
     * @throws Exception
     */
    public Map createOrderFromJsapi(String storeId, String desc, Integer totalAmount, String outTradeNo, String openid,
            Byte serviceItemCategory, String notifyUrl) throws Exception {
        // 初始化
        // System.out.println("MCH_ID:"+payConstants.getMchId(storeId,serviceItemCategory));
        // System.out.println("MCH_SERIAL_NO:"+payConstants.getMchSerialNo(storeId,serviceItemCategory));
        // System.out.println("API_V3KEY:"+payConstants.getApiV3key(storeId,serviceItemCategory));
        // System.out.println("APP_ID:"+payConstants.getAppId());
        // System.out.println("notifyUrl:"+notifyUrl);
        // System.out.println("key_path:"+payConstants.getPrivateKey(storeId,serviceItemCategory));

        CloseableHttpClient httpClient = getPayClient(storeId, serviceItemCategory);
        
        HttpPost httpPost = new HttpPost("https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi");
        httpPost.addHeader("Accept", "application/json");
        httpPost.addHeader("Content-type", "application/json; charset=utf-8");
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.put("mchid", payConstants.getMchId(storeId, serviceItemCategory))
                .put("appid", payConstants.getAppId())
                .put("notify_url", notifyUrl)
                .put("description", desc)
                .put("attach", desc)
                .put("out_trade_no", outTradeNo); // 需要我们自己动态生成
        rootNode.putObject("amount")
                .put("total", totalAmount);
        rootNode.putObject("payer")
                .put("openid", openid);

        objectMapper.writeValue(bos, rootNode);
        System.out.println(bos.toString("UTF-8"));
        httpPost.setEntity(new StringEntity(bos.toString("UTF-8"), "UTF-8"));
        CloseableHttpResponse response = httpClient.execute(httpPost);

        String bodyAsString = EntityUtils.toString(response.getEntity());
        System.out.println(bodyAsString);

        String nonce = RandomUtils.randomString(32);
        String timestamp = System.currentTimeMillis() + "";
        StringBuilder builder = new StringBuilder();
        // 应用id
        builder.append(payConstants.getAppId()).append("\n");
        // 时间戳
        builder.append(timestamp).append("\n");
        // 随机字符串
        builder.append(nonce).append("\n");
        // 预支付交易会话ID
        JsonNode node = objectMapper.readTree(bodyAsString);
        String prepay_id = node.get("prepay_id").asText();
        String packageStr = "prepay_id=" + prepay_id;
        builder.append(packageStr).append("\n");
        // 签名
        String ciphertext = sign(storeId, serviceItemCategory, builder.toString().getBytes("utf-8"));

        Map map = new HashMap();
        map.put("appId", payConstants.getAppId());
        map.put("timeStamp", timestamp);
        map.put("nonceStr", nonce);
        map.put("package", packageStr);
        map.put("signType", "RSA");
        map.put("paySign", ciphertext);

        return map;
    }

    /*
     *
     * 只能关闭没有支付的订单
     * 
     * @param outTradeNo 我们自己生成的订单号
     */

    public boolean closeOrder(String storeId, String outTradeNo, Byte serviceItemCategory) throws Exception {
        // 初始化
        /*
         * ClassPathResource classPathResource = new
         * ClassPathResource(payConstants.getPrivateKey(storeId));
         * InputStream certStream = classPathResource.getInputStream();
         */
        CloseableHttpClient httpClient = getPayClient(storeId, serviceItemCategory);

        HttpPost httpPost = new HttpPost(
                "https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/" + outTradeNo + "/close");
        httpPost.addHeader("Accept", "application/json");
        httpPost.addHeader("Content-type", "application/json; charset=utf-8");

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectMapper objectMapper = new ObjectMapper();

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.put("mchid", payConstants.getMchId(storeId, serviceItemCategory));

        objectMapper.writeValue(bos, rootNode);

        httpPost.setEntity(new StringEntity(bos.toString("UTF-8"), "UTF-8"));
        CloseableHttpResponse response = httpClient.execute(httpPost);
        return response.getStatusLine().getStatusCode() == 204;
    }

    /**
     * 签名
     * 
     * @param message
     * @return
     * @throws Exception
     */
    public String sign(String storeId, Byte serviceItemCategory, byte[] message) throws Exception {
        Signature sign = Signature.getInstance("SHA256withRSA");

        PrivateKey merchantPrivateKey = loadPrivateKey(storeId, serviceItemCategory);

        sign.initSign(merchantPrivateKey);
        sign.update(message);

        return Base64.getEncoder().encodeToString(sign.sign());
    }

    /**
     * 验签
     * 
     * @param message
     * @return
     * @throws Exception
     */
    public boolean signVerify(String storeId, Byte serviceItemCategory, String serial, String message, String signature)
            throws Exception {

        /*
         * ClassPathResource classPathResource = new
         * ClassPathResource(payConstants.getPrivateKey(storeId,serviceItemCategory));
         * InputStream certStream = classPathResource.getInputStream();
         */
        PrivateKey merchantPrivateKey = loadPrivateKey(storeId, serviceItemCategory);

        // 使用自动更新的签名验证器，不需要传入证书
        AutoUpdateCertificatesVerifier verifier = new AutoUpdateCertificatesVerifier(
                new WechatPay2Credentials(payConstants.getMchId(storeId, serviceItemCategory),
                        new PrivateKeySigner(payConstants.getMchSerialNo(storeId, serviceItemCategory),
                                merchantPrivateKey)),
                payConstants.getApiV3key(storeId, serviceItemCategory).getBytes(StandardCharsets.UTF_8));
        return verifier.verify(serial, message.getBytes(StandardCharsets.UTF_8), signature);
    }

    /**
     * 解密订单支付信息
     * 
     * @param body 原始数据
     * @return
     */
    public String decryptOrder(String storeId, Byte serviceItemCategory, String body) {
        try {
            AesUtil util = new AesUtil(
                    payConstants.getApiV3key(storeId, serviceItemCategory).getBytes(StandardCharsets.UTF_8));
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode node = objectMapper.readTree(body);
            JsonNode resource = node.get("resource");
            String ciphertext = resource.get("ciphertext").textValue();
            String associatedData = resource.get("associated_data").textValue();
            String nonce = resource.get("nonce").textValue();
            return util.decryptToString(associatedData.getBytes(StandardCharsets.UTF_8),
                    nonce.getBytes(StandardCharsets.UTF_8), ciphertext);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 商户订单号查询订单
     * 
     * @param outTradeNo
     * @return
     */
    public String queryOrder(String storeId, Byte serviceItemCategory, String outTradeNo) {
        try {
            URIBuilder uriBuilder = new URIBuilder("https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/"
                    + outTradeNo + "?mchid=" + payConstants.getMchId(storeId, serviceItemCategory));
            HttpGet httpGet = new HttpGet(uriBuilder.build());
            httpGet.addHeader("Accept", "application/json");

            CloseableHttpClient httpClient = getPayClient(storeId, serviceItemCategory);

            CloseableHttpResponse response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public JSONObject decryptNotifyUrlBody(String storeId, Byte serviceItemCategory, HttpServletRequest request) {
        JSONObject result = new JSONObject();
        result.put("code", "FAIL");
        try {
            BufferedReader br = request.getReader();
            String str = null;
            StringBuilder builder = new StringBuilder();
            while ((str = br.readLine()) != null) {
                builder.append(str);
            }
            System.out.println("body:" + builder.toString());
            // 验证签名
            StringBuffer signStr = new StringBuffer();
            signStr.append(request.getHeader("Wechatpay-Timestamp")).append("\n");
            signStr.append(request.getHeader("Wechatpay-Nonce")).append("\n");
            signStr.append(builder.toString()).append("\n");
            if (!signVerify(storeId, serviceItemCategory, request.getHeader("Wechatpay-Serial"), signStr.toString(),
                    request.getHeader("Wechatpay-Signature"))) {
                result.put("message", "sign error");
                return result;
            }

            // 解密密文
            String goodBody = decryptOrder(storeId, serviceItemCategory, builder.toString());
            JSONObject goodJson = JSON.parseObject(goodBody);
            if ("SUCCESS".equalsIgnoreCase(goodJson.getString("trade_state"))) {
                String serviceItemId = goodJson.getString("attach");
                // 获取产品信息
                // 根据产品获取不同的service
                result.put("goodBody", goodJson);
            }
            // 一切正常后返回code=SUCCESS,
            result.put("code", "SUCCESS");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("message", "exception:" + e.getMessage());
        }
        return result;
    }

    /**
     * 申请退款：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_9.shtml
     * 
     * @param transactionId 微信支付订单号
     * @param outTradeNo    商户订单号 订单号可以二选一
     * @param outRefundNo   商户退款单号
     * @param total         原订单金额
     * @param refund        退款金额
     * @return
     */
    public String createRefundOrder(String storeId, Byte serviceItemCategory, String transactionId, String outTradeNo,
            String outRefundNo, int total, int refund) throws Exception {

        String notifyUrl = payConstants.getRefundNotifyUrl(storeId, serviceItemCategory);

        return createRefundOrder(storeId, serviceItemCategory, transactionId, outTradeNo, outRefundNo, total, refund,
                notifyUrl);
    }

    public String createRefundOrder(String storeId, Byte serviceItemCategory, String transactionId, String outTradeNo,
            String outRefundNo, int total, int refund, String notify_url) throws Exception {

        CloseableHttpClient httpClient = getPayClient(storeId, serviceItemCategory);

        HttpPost httpPost = new HttpPost("https://api.mch.weixin.qq.com/v3/refund/domestic/refunds");
        httpPost.addHeader("Accept", "application/json");
        httpPost.addHeader("Content-type", "application/json; charset=utf-8");

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectMapper objectMapper = new ObjectMapper();

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.put("out_trade_no", outTradeNo)
                .put("out_refund_no", outRefundNo)
                .put("notify_url", notify_url);// 需要我们自己动态生成
        rootNode.putObject("amount")
                .put("total", total)
                .put("refund", refund)
                .put("currency", "CNY");

        objectMapper.writeValue(bos, rootNode);

        httpPost.setEntity(new StringEntity(bos.toString("UTF-8"), "UTF-8"));
        CloseableHttpResponse response = httpClient.execute(httpPost);

        return EntityUtils.toString(response.getEntity());
    }

    /**
     * 通过商户退款单号查询退款单
     * 
     * @param outRefundNo
     * @return
     */

    public String queryRefundOrder(String storeId, Byte serviceItemCategory, String outRefundNo) {
        try {
            URIBuilder uriBuilder = new URIBuilder(
                    "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds/" + outRefundNo);
            HttpGet httpGet = new HttpGet(uriBuilder.build());
            httpGet.addHeader("Accept", "application/json");

            CloseableHttpClient httpClient = getPayClient(storeId, serviceItemCategory);

            CloseableHttpResponse response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private CloseableHttpClient getPayClient(String storeId, Byte serviceItemCategory) {
        // 读取文件形式加载商户私钥
        PrivateKey merchantPrivateKey = loadPrivateKey(storeId, serviceItemCategory);
        // Use RSAAutoCertificateConfig for auto-updating certificates
        RSAAutoCertificateConfig config = new RSAAutoCertificateConfig.Builder()
                .merchantId(payConstants.getMchId(storeId, serviceItemCategory))
                .privateKey(merchantPrivateKey)
                .merchantSerialNumber(payConstants.getMchSerialNo(storeId, serviceItemCategory))
                .apiV3Key(payConstants.getApiV3key(storeId, serviceItemCategory))
                .build();

        WechatPay2Validator validator = new WechatPay2Validator(config.createVerifier()); // use auto update certificates

        // use other client that httpclient, for example resttemplate


        CloseableHttpClient httpClient = WechatPayHttpClientBuilder.create()
                .withMerchant(payConstants.getMchId(storeId, serviceItemCategory),
                        payConstants.getMchSerialNo(storeId, serviceItemCategory), merchantPrivateKey)
                .withValidator(validator)
                .build();

        return httpClient;
    }

    private PrivateKey loadPrivateKey(String storeId, Byte serviceItemCategory) {
        // InputStream certStream = new ByteArrayInputStream(
        //         payConstants.getPrivateKey(storeId, serviceItemCategory).getBytes());
        String certString = payConstants.getPrivateKey(storeId, serviceItemCategory);
        // 读取文件形式加载商户私钥
        // PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(certStream);
        PrivateKey merchantPrivateKey = loadPrivateKey(certString);
        return merchantPrivateKey;
    }

    public static PrivateKey loadPrivateKey(String privateKey) {
        privateKey = privateKey
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");

        try {
            KeyFactory kf = KeyFactory.getInstance("RSA");
            return kf.generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey)));

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("当前Java环境不支持RSA", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("无效的密钥格式");
        }
    }

    public static void main(String[] args) {

    }
}
