package com.relle.service.impl;

import com.relle.dto.AppCouponShareDTO;
import com.relle.enums.CouponShareStatusEnum;
import com.relle.mbg.mapper.AppCouponGiveMapper;
import com.relle.mbg.model.*;
import com.relle.service.IAppCouponCheckoutService;
import com.relle.service.IAppCouponGiveService;
import com.relle.service.IAppCouponService;
import com.relle.service.IAppCouponServiceRelationService;
import com.relle.service.IAppServiceItemService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AppCouponGiveServiceImpl implements IAppCouponGiveService {
    @Resource
    private AppCouponGiveMapper appCouponGiveMapper;

    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;

    @Resource
    private IAppCouponService iAppCouponService;

    @Resource
    private IAppServiceItemService iAppServiceItemService;

    @Resource
    private IAppCouponServiceRelationService iAppCouponServiceRelationService;

    @Override
    public AppCouponGive share(AppCouponCheckout checkout, AppCustomerInfo customerInfo) {
        AppCouponGiveExample example = new AppCouponGiveExample();
        example.createCriteria()
                .andCouponNoEqualTo(checkout.getId())
                .andGiveStatusEqualTo(CouponShareStatusEnum.SHAREING.getCode());

        List<AppCouponGive> gives = appCouponGiveMapper.selectByExample(example);
        if (!gives.isEmpty()) {
            return gives.get(0);
        }

        AppCouponGive give = new AppCouponGive();

        AppCoupon coupon = iAppCouponService.getCoupon(checkout.getCouponId());
        LocalDateTime now = LocalDateTime.now();
        give.setCouponNo(checkout.getId());
        give.setCouponId(checkout.getCouponId());
        give.setCouponReceiveId("");
        give.setCouponName(coupon != null ? coupon.getCouponName() : "该券已下架");
        give.setGiveUserId(customerInfo.getCustomerId());
        give.setGiveUserUnionid(customerInfo.getUnionid());
        give.setGiveStatus(CouponShareStatusEnum.SHAREING.getCode());
        give.setGiveTime(now);

        give.setCreateBy(customerInfo.getUnionid());
        give.setCreateTime(now);
        give.setUpdateBy(customerInfo.getUnionid());
        give.setUpdateTime(now);
        appCouponGiveMapper.insertSelective(give);
        return give;
    }

    @Override
    public int receive(Long giveId, AppCustomerInfo customerInfo) {
        AppCouponGive give = new AppCouponGive();
        give.setId(giveId);

        LocalDateTime now = LocalDateTime.now();
        give.setReceiveTime(now);
        give.setReceiveUserId(customerInfo.getCustomerId());
        give.setReceiveUserUnionid(customerInfo.getUnionid());

        give.setGiveStatus(CouponShareStatusEnum.SHARED.getCode());

        give.setUpdateTime(now);
        give.setUpdateBy(customerInfo.getCustomerId());

        return appCouponGiveMapper.updateByPrimaryKeySelective(give);
    }

    @Override
    public int back(Long giveId) {
        AppCouponGive give = new AppCouponGive();
        give.setGiveStatus(CouponShareStatusEnum.RETURNED.getCode());
        LocalDateTime now = LocalDateTime.now();
        give.setUpdateTime(now);
        give.setUpdateBy("AppCouponGiveServiceImpl.back");

        AppCouponGiveExample example = new AppCouponGiveExample();
        example.createCriteria()
                .andIdEqualTo(giveId)
                .andGiveStatusEqualTo(CouponShareStatusEnum.SHAREING.getCode());

        return appCouponGiveMapper.updateByExampleSelective(give, example);
    }

    @Override
    public AppCouponGive getShareRecordById(Long shareId) {
        return appCouponGiveMapper.selectByPrimaryKey(shareId);
    }

    @Override
    public AppCouponGive getShareRecordByCouponNo(Long couponNo) {
        AppCouponGiveExample example = new AppCouponGiveExample();
        example.createCriteria()
                .andCouponNoEqualTo(couponNo);
        example.setOrderByClause(" id desc ");
        List<AppCouponGive> gives = appCouponGiveMapper.selectByExample(example);
        if (gives.isEmpty()) {
            return null;
        }
        return gives.get(0);
    }

    @Override
    public List<AppCouponGive> getShareRecord(String unionid) {
        AppCouponGiveExample example = new AppCouponGiveExample();
        example.createCriteria()
                .andGiveUserUnionidEqualTo(unionid)
                .andGiveStatusEqualTo(CouponShareStatusEnum.SHARED.getCode());

        List<AppCouponGive> gives = appCouponGiveMapper.selectByExample(example);
        return gives;
    }

    @Override
    public int canReceive(AppCouponGive appCouponGive) {
        if (appCouponGive == null || appCouponGive.getId() == null) {
            return 0;
        }
        Byte status = appCouponGive.getGiveStatus();
        if (status.byteValue() == CouponShareStatusEnum.SHARED.getCode()) {
            return 1;
        }
        if (status.byteValue() == CouponShareStatusEnum.RETURNED.getCode()) {
            return 2;
        }
        return 64;
    }

    @Override
    public AppCouponShareDTO getShareRecordDetailById(Long giveId) {
        AppCouponShareDTO shareDTO = new AppCouponShareDTO();
        AppCouponGive give = getShareRecordById(giveId);
        AppCouponCheckout couponCheckout = iAppCouponCheckoutService.getById(give.getCouponNo());
        AppCoupon coupon = iAppCouponCheckoutService.getCouponById(give.getCouponNo());

        List<AppServiceItem> serviceItems = iAppCouponServiceRelationService
                .getServiceItemByCouponId(coupon.getCouponId());
        if (give == null) {
            return null;
        }
        if (couponCheckout == null) {
            return null;
        }
        if (coupon == null) {
            return null;
        }

        String serviceItemNames = "";

        for (AppServiceItem item : serviceItems) {
            serviceItemNames += item.getServiceName() + "；";
        }
        if (serviceItemNames.length() > 0) {
            serviceItemNames = serviceItemNames.substring(0, serviceItemNames.length() - 1);
        }

        shareDTO.setShareId(give.getId());
        shareDTO.setShareTime(give.getGiveTime());
        shareDTO.setShareCustomerId(give.getGiveUserId());
        shareDTO.setShareCustomerUnionid(give.getGiveUserUnionid());

        shareDTO.setCouponId(coupon.getCouponId());
        shareDTO.setCouponName(coupon.getCouponName());
        shareDTO.setCouponType(coupon.getCouponType());
        shareDTO.setCouponCoverImg(coupon.getCouponCoverImg());

        shareDTO.setCouponStatus(give.getGiveStatus());
        shareDTO.setCouponNo(couponCheckout.getId());
        shareDTO.setCouponValidDatetimeBegin(couponCheckout.getCouponValidStarttime());
        shareDTO.setCouponValidDatetimeEnd(couponCheckout.getCouponValidEndtime());

        shareDTO.setServiceItemId("");
        shareDTO.setServiceItemName(serviceItemNames);

        return shareDTO;
    }

    @Override
    public List<AppCouponGive> getListByStatus(Byte status) {
        AppCouponGiveExample example = new AppCouponGiveExample();
        example.createCriteria()
                .andGiveStatusEqualTo(status);

        List<AppCouponGive> gives = appCouponGiveMapper.selectByExample(example);
        return gives;
    }
}
