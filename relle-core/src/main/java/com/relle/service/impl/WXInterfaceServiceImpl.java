package com.relle.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.service.IWXInterfaceService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import javax.annotation.Resource;

@Service("wxInterfaceService")
public class WXInterfaceServiceImpl implements IWXInterfaceService {
    String appid = "wx56b81e1a0a4cc40a";
    String secret = "c9d39576c9e3aa6500db449ff7929a3f";
    private AccessToken accessToken;
    @Resource
    ObjectMapper objectMapper;
    @Resource
    RestTemplate restTemplate;

    @Override
    public String getToken() {

        if (accessToken == null || isTokenExpired()) {
            accessToken = getAccessToken(appid, secret);
        }
        return accessToken.getToken();
    }

    private Boolean isTokenExpired() {
        LocalDateTime now = LocalDateTime.now();
        return accessToken == null || now.isAfter(accessToken.getValidDateTime().plusMinutes(5));
    }

    public String getURLLink(String path, String env_version, String query) {
        String urlLinkUrl = "https://api.weixin.qq.com/wxa/generate_urllink?access_token=" + getToken();
        if (StringUtils.hasLength(env_version)) {
            urlLinkUrl += "&env_version=" + env_version;
        }
        ObjectNode ObjectNode = objectMapper.createObjectNode();
        ObjectNode.put("path", path);
        ObjectNode.put("query", query);
        ObjectNode.put("expire_type", 1);
        ObjectNode.put("expire_interval", 1);
        // ObjectNode.put("env_version", env_version);

        String responseStr = restTemplate.postForObject(urlLinkUrl, ObjectNode, String.class);
        JsonNode responseNode;
        try {
            responseNode = objectMapper.readTree(responseStr);
            return responseNode.get("url_link").asText();

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            throw new RuntimeException("parse response of url link error");
        }

    }

    public AccessToken getAccessToken(String appid, String secret) {
        try {
            String statleTakenUrl = "https://api.weixin.qq.com/cgi-bin/stable_token";
            // RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000 *
            // 5)
            // .setConnectTimeout(60000 * 5).build();// 设置请求和传输超时时间
            // JSONObject obj = new JSONObject();
            ObjectNode paramNode = objectMapper.createObjectNode();
            paramNode.put("grant_type", "client_credential");
            paramNode.put("appid", appid);
            paramNode.put("secret", secret);
            paramNode.put("force_refresh", false);

            String responseStr = restTemplate.postForObject(statleTakenUrl, paramNode, String.class);
            JsonNode responseNode = objectMapper.readTree(responseStr);
            AccessToken token = new AccessToken();
            token.setToken(responseNode.get("access_token").asText());
            token.setRefreshToken(responseNode.get("refresh_token").asText());
            token.setValidDateTime(LocalDateTime.now());
            return token;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        WXInterfaceServiceImpl impl = new WXInterfaceServiceImpl();
        String urlLink = impl.getURLLink("/home/<USER>/index", "", "oid=SH000122121517471934623");
        System.out.println(urlLink);
    }
}
