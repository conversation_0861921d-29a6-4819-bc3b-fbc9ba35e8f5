package com.relle.service.impl;

import com.relle.mbg.mapper.AppWhiteListMapper;
import com.relle.mbg.model.AppWhiteList;
import com.relle.mbg.model.AppWhiteListExample;
import com.relle.service.IAppWhiteListService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppWhiteListServiceImpl implements IAppWhiteListService {
    @Resource
    private AppWhiteListMapper appWhiteListMapper;
    public AppWhiteList getWhiteListByUnionid(String Unionid){
        AppWhiteListExample example = new AppWhiteListExample();
        example.createCriteria()
                .andDeletedEqualTo((byte)0)
                .andUnionidEqualTo(Unionid);
        List<AppWhiteList> appWhiteLists = appWhiteListMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(appWhiteLists)){
            return null;
        }
        return appWhiteLists.get(0);
    }
    @Override
    public AppWhiteList getWhiteListByUnionid(String Unionid, String string, byte b) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getWhiteListByUnionid'");
    }
}
