package com.relle.service.impl;

import com.relle.mbg.mapper.AppMchAccountMapper;
import com.relle.mbg.model.AppMchAccount;
import com.relle.mbg.model.AppMchAccountExample;
import com.relle.service.IAppMchAccountService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppMchAccountServiceImpl implements IAppMchAccountService {
    @Resource
    private AppMchAccountMapper appMchAccountMapper;
    public List<AppMchAccount> getAll(){
        AppMchAccountExample example = new AppMchAccountExample();
        return appMchAccountMapper.selectByExampleWithBLOBs(example);
    }
}
