package com.relle.service.impl;

import com.relle.mbg.mapper.AppCustomerInfoMapper;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.mbg.model.AppCustomerInfoExample;
import com.relle.service.IAppCustomerInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;

@Service
public class AppCustomerInfoServiceImpl implements IAppCustomerInfoService {
    private static final Logger logger = LoggerFactory.getLogger(AppCustomerInfoServiceImpl.class);

    @Resource
    private AppCustomerInfoMapper appCustomerInfoMapper;

    @Override
    public int save(String unionid, String openid) {
        AppCustomerInfo appCustomerInfo = selectByUnionid(unionid);
        if(appCustomerInfo==null){//首次登录
            appCustomerInfo = new AppCustomerInfo();
            appCustomerInfo.setUnionid(unionid);
            appCustomerInfo.setMiniOpenid(openid);

            // Generate customer ID
            String customerId = generateCustomerId();
            appCustomerInfo.setCustomerId(customerId);

            Date now = new Date();
            appCustomerInfo.setCreateBy(unionid);
            appCustomerInfo.setCreateTime(now);
            appCustomerInfo.setUpdateBy(unionid);
            appCustomerInfo.setUpdateTime(now);

            logger.info("Creating new customer with unionid: {}, customerId: {}", unionid, customerId);
            return appCustomerInfoMapper.insertSelective(appCustomerInfo);
        }
        return 0;
    }

    @Override
    public AppCustomerInfo selectByUnionid(String unionid) {
        AppCustomerInfoExample example = new AppCustomerInfoExample();
        example.createCriteria()
                .andUnionidEqualTo(unionid)
                .andDeletedEqualTo((byte)0);
        List<AppCustomerInfo> customerInfos = appCustomerInfoMapper.selectByExample(example);
        if(customerInfos.isEmpty()){
            return null;
        }
        return customerInfos.get(0);
    }

    @Override
    public int update(AppCustomerInfo customerInfo) {
        if (customerInfo == null || customerInfo.getUnionid() == null) {
            return 0;
        }

        customerInfo.setUpdateTime(new Date());

        AppCustomerInfoExample example = new AppCustomerInfoExample();
        example.createCriteria()
                .andUnionidEqualTo(customerInfo.getUnionid())
                .andDeletedEqualTo((byte)0);

        return appCustomerInfoMapper.updateByExampleSelective(customerInfo, example);
    }

    @Override
    public AppCustomerInfo get(String unionid) {
        // This is an alias for selectByUnionid for backward compatibility
        return selectByUnionid(unionid);
    }

    private String generateCustomerId() {
        try {
            AppCustomerInfoExample example = new AppCustomerInfoExample();
            example.createCriteria().andDeletedEqualTo((byte) 0);

            long count = appCustomerInfoMapper.countByExample(example);

            DecimalFormat df = new DecimalFormat("000000");
            return df.format(count + 1);
        } catch (Exception e) {
            logger.error("Error generating customer ID", e);
            return String.valueOf(System.currentTimeMillis() % 1000000);
        }
    }
}
