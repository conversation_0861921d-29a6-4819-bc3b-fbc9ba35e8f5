package com.relle.service.impl;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppActivity;
import com.relle.service.IAppActivityService;

import java.util.List;

import org.springframework.stereotype.Service;

@Service
public class AppActivityServiceImpl implements IAppActivityService {
    
    @Override
    public CommonResult<?> getActiveActivities(String storeId) {
        return CommonResult.succeeded("Active activities for store: " + storeId);
    }
    
    @Override
    public CommonResult<?> getActivityDetails(String activityId) {
        return CommonResult.succeeded("Activity details for: " + activityId);
    }
    
    @Override
    public CommonResult<?> participateInActivity(String activityId, String userId) {
        return CommonResult.succeeded("User " + userId + " participated in activity: " + activityId);
    }

    @Override
    public List<AppActivity> getActivityListByCategory(String sotreId, Integer code) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getActivityListByCategory'");
    }

    @Override
    public List<AppActivity> getActivityList(String string) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getActivityList'");
    }

    @Override
    public CommonResult<?> getCouponByActivity(Long activityId, String unionid) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getCouponByActivity'");
    }

    @Override
    public CommonResult<?> receiveCouponByActivity(Long valueOf, String unionid) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'receiveCouponByActivity'");
    }

    @Override
    public AppActivity getActivity(String storeId, String activityId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getActivity'");
    }
}
