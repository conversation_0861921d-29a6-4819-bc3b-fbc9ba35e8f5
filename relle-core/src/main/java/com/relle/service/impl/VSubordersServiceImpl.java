package com.relle.service.impl;

import com.relle.mbg.mapper.VSubordersMapper;
import com.relle.mbg.model.VSuborders;
import com.relle.mbg.model.VSubordersExample;
import com.relle.service.IVSubordersService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class VSubordersServiceImpl implements IVSubordersService {
    @Resource
    private VSubordersMapper vSubordersMapper;

    @Override
    public String getServiceItemIdFromOrderId(String orderId) {
        VSubordersExample example = new VSubordersExample();
        example.createCriteria()
                .andOrderIdEqualTo(orderId);

        List<VSuborders> vSuborders = vSubordersMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(vSuborders)){
            return null;
        }
        VSuborders sub = vSuborders.get(0);
        return sub.getServiceItemId();
    }
}
