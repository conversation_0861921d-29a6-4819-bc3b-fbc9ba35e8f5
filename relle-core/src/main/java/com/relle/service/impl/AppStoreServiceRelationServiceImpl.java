package com.relle.service.impl;

import com.relle.mbg.model.AppStoreServiceRelation;
import com.relle.service.IAppStoreServiceRelationService;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

/**
 * App Store Service Relation Service Implementation
 */
@Service
public class AppStoreServiceRelationServiceImpl implements IAppStoreServiceRelationService {
    
    @Override
    public List<AppStoreServiceRelation> getStoreIdByServiceItem(String serviceItemId) {
        // TODO: Implement actual store-service relation retrieval
        List<AppStoreServiceRelation> relations = new ArrayList<>();
        
        // Create mock relations
        for (int i = 1; i <= 2; i++) {
            AppStoreServiceRelation relation = new AppStoreServiceRelation();
            relation.setStoreId("STORE00" + i);
            relation.setServiceItemId(serviceItemId);
            relations.add(relation);
        }
        
        return relations;
    }
}
