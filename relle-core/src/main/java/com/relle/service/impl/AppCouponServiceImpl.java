package com.relle.service.impl;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppCouponDTO;
import com.relle.mbg.model.AppCoupon;
import com.relle.mbg.model.AppCouponCheckout;
import com.relle.service.IAppCouponService;

import java.util.List;

import org.springframework.stereotype.Service;

@Service
public class AppCouponServiceImpl implements IAppCouponService {
    
    @Override
    public CommonResult<?> getUserCoupons(String userId) {
        return CommonResult.succeeded("User coupons for: " + userId);
    }
    
    @Override
    public CommonResult<?> getCouponDetails(String couponId) {
        return CommonResult.succeeded("Coupon details for: " + couponId);
    }
    
    @Override
    public CommonResult<?> useCoupon(String couponId, String orderId) {
        return CommonResult.succeeded("Coupon " + couponId + " used for order: " + orderId);
    }
    
    @Override
    public CommonResult<?> validateCoupon(String couponId) {
        return CommonResult.succeeded("Coupon validated: " + couponId);
    }

    @Override
    public List<AppCouponDTO> getMyCoupon(String unionid) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getMyCoupon'");
    }

    @Override
    public List<AppCouponDTO> getMyCoupon(String unionid, String serviceItemId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getMyCoupon'");
    }

    @Override
    public AppCouponCheckout receiveCoupon(String unionid, Long couponId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'receiveCoupon'");
    }

    @Override
    public AppCoupon getCoupon(String couponId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getCoupon'");
    }

    @Override
    public AppCoupon getCouponById(Long couponId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getCouponById'");
    }

    @Override
    public int useCoupon(Long couponId, String orderId, String useCouponUserUnionid) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'useCoupon'");
    }
}
