package com.relle.service.impl;

import com.relle.dto.AppServiceTeamOrderDTO;
import com.relle.dto.AppServiceTeamSuborderDTO;
import com.relle.common.api.CommonResult;
import com.relle.common.utils.DateTimeUtil;
import com.relle.dto.AppServiceItemDTO;
import com.relle.dto.ShareTeamOrderDTO;
import com.relle.enums.CouponCheckoutSourceEnum;
import com.relle.enums.CouponStatusEnum;
import com.relle.enums.JobStatusEnum;
import com.relle.enums.OderRefundStatusEnum;
import com.relle.enums.OrderCategoryEnum;
import com.relle.enums.ServiceItemMediaTypeEnum;
import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.enums.ShareTeamOrderStatusEnum;
import com.relle.mbg.mapper.*;
import com.relle.mbg.model.*;
import com.relle.service.IAppCouponCheckoutService;
import com.relle.service.IAppOrderPayService;
import com.relle.service.IAppOrderRecommendService;
import com.relle.service.IAppOrderRefundService;
import com.relle.service.IAppServiceItemCouponService;
import com.relle.service.IAppServiceTeamOrderService;
import com.relle.service.ICouponRuleService;
import com.relle.service.IScheduleJobService;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class AppServiceTeamOrderServiceImpl implements IAppServiceTeamOrderService {
    @Resource
    private AppServiceTeamOrderMapper appServiceTeamOrderMapper;

    @Resource
    private AppServiceTeamSuborderMapper appServiceTeamSuborderMapper;
    @Resource
    private AppCouponCheckoutMapper appCouponCheckoutMapper;

    @Resource
    private AppCouponMapper appCouponMapper;

    @Resource
    private AppOrderPayMapper appOrderPayMapper;

    @Resource
    private AppStoreInfoMapper appStoreInfoMapper;

    @Resource
    private AppServiceItemMapper appServiceItemMapper;
    @Resource
    private AppServiceMediaRelationMapper appServiceMediaRelationMapper;
    @Resource
    private AppServiceMediaMapper appServiceMediaMapper;
    @Resource
    private IScheduleJobService iScheduleJobService;
    @Resource
    private SysTaskMapper sysTaskMapper;

    @Resource
    private IAppOrderRefundService iAppOrderRefundService;
    @Resource
    private IAppOrderPayService iAppOrderPayService;
    @Resource
    private IAppServiceItemCouponService iAppServiceItemCouponService;

    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;

    @Resource
    private AppStoreRoomMapper appStoreRoomMapper;
    @Resource
    private IAppOrderRecommendService iAppOrderRecommendService;

    @Resource
    private ICouponRuleService iCouponRuleService;

    @Override
    @Transactional
    public CommonResult<?> createOrder(Map<String,Object> orderMap) {
        if(_validParams(orderMap)){
            return _saveOrder(orderMap);
        } else {
            return CommonResult.failed(1000,"参数错误");
        }
    }

    private CommonResult<?> _saveOrder(Map<String,Object> orderMap) {

        String storeId = (String)orderMap.get("storeId");
        String serviceItemId = (String)orderMap.get("serviceItemId");
        Number serviceItemOriginAmount = (Number)orderMap.get("serviceItemOriginAmount");
        Number serviceItemReductionAmount = (Number)orderMap.get("serviceItemReductionAmount");
        Number serviceItemAmount = (Number)orderMap.get("serviceItemAmount");
        String unionid = (String)orderMap.get("unionid");
        String orderId = (String)orderMap.get("orderId");

        String appId = (String)orderMap.get("appId");

        String teamNo = (String)orderMap.get("teamNo");
        if(StringUtils.isEmpty(teamNo)){
            teamNo = "T"+orderId;

        }
        String timeStamp = (String)orderMap.get("timeStamp");
        String nonceStr = (String)orderMap.get("nonceStr");
        String packageStr = (String)orderMap.get("package");
        String signType = (String)orderMap.get("signType");
        String paySign = (String)orderMap.get("paySign");

        Number couponId = (Number)orderMap.get("couponId");

        Short orderStatus = (Short)orderMap.get("orderStatus");
        //代金券的订单直接显示完成
        //orderStatus = ServiceOrderStatusEnum.FINISHED.getCode();

        AppServiceTeamOrder order = new AppServiceTeamOrder();
        order.setTeamNo(teamNo);
        order.setOrderId(orderId);
        order.setUnionid(unionid);
        order.setSotreId(storeId);
        //order.setOrderStatus(orderStatus);
        order.setOrderAmount(BigDecimal.valueOf(Double.valueOf(serviceItemAmount.doubleValue())));
        order.setOriginPrice(BigDecimal.valueOf(Double.valueOf(serviceItemOriginAmount.doubleValue())));
        order.setReductionAmount(BigDecimal.valueOf(Double.valueOf(serviceItemReductionAmount.doubleValue())));

        order.setCreateBy(unionid);
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateBy(unionid);
        order.setUpdateTime(LocalDateTime.now());

        appServiceTeamOrderMapper.insertSelective(order);

        AppServiceTeamSuborder suborder = new AppServiceTeamSuborder();
        suborder.setOrderId(orderId);
        suborder.setSuborderId(orderId+"01");
        suborder.setUnionid(unionid);
        suborder.setStoreId(storeId);
        suborder.setServiceItemId(serviceItemId);
        suborder.setSuborderStatus(orderStatus);

        suborder.setSuborderAmount(BigDecimal.valueOf(Double.valueOf(serviceItemAmount.doubleValue())));
        suborder.setSuborderOriginPrice(BigDecimal.valueOf(Double.valueOf(serviceItemOriginAmount.doubleValue())));
        suborder.setSuborderReductionAmount(BigDecimal.valueOf(Double.valueOf(serviceItemReductionAmount.doubleValue())));

        suborder.setCreateBy(unionid);
        suborder.setCreateTime(LocalDateTime.now());
        suborder.setUpdateBy(unionid);
        suborder.setUpdateTime(LocalDateTime.now());

        //

        appServiceTeamSuborderMapper.insertSelective(suborder);

        if( teamNo.equals("T"+orderId)){
            //发起人
            iScheduleJobService.createJob("com.relle.task.AutoCloseTeamOrderFailedJob","team-fail-close",teamNo,120);
        }

        //消费优惠券
        if(couponId!=null) {
            AppCouponCheckout appCouponCheckout = new AppCouponCheckout();
            appCouponCheckout.setId(couponId.longValue());
            appCouponCheckout.setUseCouponOrderid(orderId);
            appCouponCheckout.setUseCouponTime(LocalDateTime.now());
            appCouponCheckout.setCouponStatus((byte) 2);
            appCouponCheckoutMapper.updateByPrimaryKeySelective(appCouponCheckout);//updateByExampleSelective()
        }

        String employeeId = (String)orderMap.get("employeeId");
        if(!StringUtils.isEmpty(employeeId)){
            iAppOrderRecommendService.save(orderId, unionid,
                    BigDecimal.valueOf(Double.valueOf(serviceItemAmount.doubleValue())),
                    employeeId,
                    OrderCategoryEnum.COUPON.getCode());
        }

        /*Calendar now = Calendar.getInstance();
        now.add(Calendar.MINUTE,15);

        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH)+1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        int hour = now.get(Calendar.HOUR_OF_DAY);
        int minute = now.get(Calendar.MINUTE);
        int second = now.get(Calendar.SECOND);

        StringBuilder cronExpression = new StringBuilder();
        cronExpression.append(second);
        cronExpression.append(" ");
        cronExpression.append(minute);
        cronExpression.append(" ");
        cronExpression.append(hour);
        cronExpression.append(" ");
        cronExpression.append(day);
        cronExpression.append(" ");
        cronExpression.append(month);
        cronExpression.append(" ");
        cronExpression.append(" ? ");
        cronExpression.append(year);

        SysTask task = new SysTask();
        task.setJobName(orderId);
        task.setBeanClass("com.relle.task.AutoCloseTeamOrderJob");
        task.setJobGroup("task-close");
        task.setCronExpression(cronExpression.toString());
        task.setJobParams(orderId);

        iScheduleJobService.addJob(task,true);*/


        return CommonResult.succeeded("");
    }

    private boolean _validParams(Map<String,Object> orderMap){
        if(orderMap==null || orderMap.isEmpty()){
            return false;
        }
        String storeId = (String)orderMap.get("storeId");
        String serviceItemId = (String)orderMap.get("serviceItemId");
        Number serviceItemOriginAmount = (Number)orderMap.get("serviceItemOriginAmount");
        // Number serviceItemReductionAmount = (Number)orderMap.get("serviceItemReductionAmount");
        Number serviceItemAmount = (Number)orderMap.get("serviceItemAmount");

        if(StringUtils.isEmpty(storeId)){
            return false;
        }

        if(StringUtils.isEmpty(serviceItemId)){
            return false;
        }

        if(StringUtils.isEmpty(serviceItemOriginAmount)){
            return false;
        }

        if(StringUtils.isEmpty(serviceItemAmount)){
            return false;
        }

        return true;
    }
    public AppServiceTeamOrder _getOrder(String orderId){
        AppServiceTeamOrderExample example = new AppServiceTeamOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceTeamOrder> list = appServiceTeamOrderMapper.selectByExample(example);
        if(list.isEmpty()){
            return null;
        }
        return list.get(0);
    }
    @Override
    public  List<AppServiceTeamOrderDTO> getOrderList(String unionid) {
        AppServiceTeamOrderExample example = new AppServiceTeamOrderExample();
        example.createCriteria().andUnionidEqualTo(unionid);
        example.setOrderByClause(" create_time desc ");

        List<AppServiceTeamOrder> list = appServiceTeamOrderMapper.selectByExample(example);
        List<AppServiceTeamOrderDTO> rList = new ArrayList<>();
        for (AppServiceTeamOrder order:list) {
            AppServiceTeamOrderDTO dto = new AppServiceTeamOrderDTO();
            dto.setOrder(order);

            String createUser = order.getCreateBy();
            if(createUser.startsWith("pad-admin")){
                dto.setIsAdminAdd((byte)1);
            } else {
                dto.setIsAdminAdd((byte)0);
            }

            dto.setStoreInfo(_getByStoreId(order.getSotreId()));

            AppServiceTeamSuborderExample suborderExample = new AppServiceTeamSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(order.getOrderId());
            suborderExample.setOrderByClause(" suborder_id asc ");
            List<AppServiceTeamSuborder> suborders = appServiceTeamSuborderMapper.selectByExample(suborderExample);
            List<AppServiceTeamSuborderDTO> serviceSuborderDTOS = new ArrayList<>();
            for (AppServiceTeamSuborder suborder:suborders) {
                AppServiceTeamSuborderDTO serviceSuborderDTO = new AppServiceTeamSuborderDTO();
                serviceSuborderDTO.setSuborder(suborder);
                serviceSuborderDTO.setServiceItem(_getOneDTOByServiceItemId(suborder.getServiceItemId()));
                serviceSuborderDTOS.add(serviceSuborderDTO);
            }
            dto.setSuborders(serviceSuborderDTOS);
            if(order.getOrderStatus()== ServiceOrderStatusEnum.WAIT_PAY.getCode()){
                dto.setPay(iAppOrderPayService.getPay(order.getOrderId()));
            }
            if(order.getOrderStatus()==ServiceOrderStatusEnum.CANCELED.getCode() ||
                    order.getOrderStatus()==ServiceOrderStatusEnum.CANCELING.getCode() ||
                    order.getOrderStatus()==ServiceOrderStatusEnum.PART_CANCELED.getCode() ||
                    order.getOrderStatus()==ServiceOrderStatusEnum.CANCEL_FAIL.getCode()){
                dto.setRefund(iAppOrderRefundService.getRefund(order.getOrderId()));
            }
            AppCouponCheckoutExample appCouponCheckoutExample = new AppCouponCheckoutExample();
            appCouponCheckoutExample.createCriteria()
                    .andUseCouponOrderidEqualTo(order.getOrderId());
                    /*.andCouponStatusEqualTo(CouponStatusEnum.NO_USE.getCode());*/
            List<AppCouponCheckout> appCouponCheckouts = appCouponCheckoutMapper.selectByExample(appCouponCheckoutExample);
            if(!appCouponCheckouts.isEmpty()){
                AppCouponCheckout appCouponCheckout = appCouponCheckouts.get(0);
                AppCouponExample appCouponExample = new AppCouponExample();
                appCouponExample.createCriteria()
                        .andCouponIdEqualTo(appCouponCheckout.getCouponId());
                List<AppCoupon> appCoupons = appCouponMapper.selectByExample(appCouponExample);
                dto.setCoupon(appCoupons.get(0));
            }
            rList.add(dto);
        }
        return rList;
    }

    private AppServiceItemDTO _getOneDTOByServiceItemId(String serviceItemId) {

        AppServiceItemDTO dto = new AppServiceItemDTO();

        AppServiceItem item =  _getOneByServiceItemId(serviceItemId);
        dto.setAppServiceItem(item);

        dto.setThumbnail(_getOneServiceMediaRelation(serviceItemId, ServiceItemMediaTypeEnum.THUMBNAIL.getCode()));

        return dto;
    }

    private List<AppServiceMedia> _getServiceMediaRelation(String serviceId,String typeCode){
        AppServiceMediaRelationExample appServiceMediaRelationExample = new AppServiceMediaRelationExample();
        appServiceMediaRelationExample.createCriteria()
                .andServiceIdEqualTo(serviceId)
                .andMediaShowtypeEqualTo(typeCode);
        List< AppServiceMediaRelation> items = appServiceMediaRelationMapper.selectByExample(appServiceMediaRelationExample);
        List< AppServiceMedia> rList = new ArrayList<>();
        for (AppServiceMediaRelation item : items) {
            rList.add(appServiceMediaMapper.selectByPrimaryKey(item.getMediaNo()));
        }

        return rList;
    }
    private AppServiceMedia _getOneServiceMediaRelation(String serviceId,String typeCode){

        List< AppServiceMedia> items = _getServiceMediaRelation(serviceId,typeCode);
        if(items.isEmpty()){
            return null;
        }
        AppServiceMedia item = items.get(0);

        return item;
    }
    private AppServiceItem _getOneByServiceItemId(String serviceItemId) {
        AppServiceItemExample query = new AppServiceItemExample();
        query.createCriteria().andServiceIdEqualTo(serviceItemId);
        List<AppServiceItem> items = appServiceItemMapper.selectByExample(query);
        return items.get(0);
    }
    private  AppStoreInfo  _getByStoreId(String storeId) {
        AppStoreInfoExample query = new AppStoreInfoExample();
        AppStoreInfoExample.Criteria criteria =  query.createCriteria();
        criteria.andStoreIdEqualTo(storeId);
        return appStoreInfoMapper.selectByExample(query).get(0);
    }

    @Override
    public CommonResult<?> getOrderDetail(String orderId) {
        AppServiceTeamOrderExample example = new AppServiceTeamOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);

        List<AppServiceTeamOrder> appServiceOrders =  appServiceTeamOrderMapper.selectByExample(example);
        if(appServiceOrders.isEmpty()){
            return CommonResult.failed(2000,"订单未找到");
        }
        AppServiceTeamOrder order = appServiceOrders.get(0);

        AppServiceTeamOrderDTO dto = new AppServiceTeamOrderDTO();
        dto.setOrder(order);

        String createUser = order.getCreateBy();
        if(createUser.startsWith("pad-admin")){
            dto.setIsAdminAdd((byte)1);
        } else {
            dto.setIsAdminAdd((byte)0);
        }

        dto.setStoreInfo(_getByStoreId(order.getSotreId()));

        AppServiceTeamSuborderExample suborderExample = new AppServiceTeamSuborderExample();
        suborderExample.createCriteria().andOrderIdEqualTo(order.getOrderId());
        suborderExample.setOrderByClause(" suborder_id asc ");
        List<AppServiceTeamSuborder> suborders = appServiceTeamSuborderMapper.selectByExample(suborderExample);
        List<AppServiceTeamSuborderDTO> serviceSuborderDTOS = new ArrayList<>();
        for (AppServiceTeamSuborder suborder:suborders) {
            AppServiceTeamSuborderDTO serviceSuborderDTO = new AppServiceTeamSuborderDTO();
            serviceSuborderDTO.setSuborder(suborder);
            serviceSuborderDTO.setServiceItem(_getOneDTOByServiceItemId(suborder.getServiceItemId()));
            serviceSuborderDTOS.add(serviceSuborderDTO);
        }
        dto.setSuborders(serviceSuborderDTOS);
        if(order.getOrderStatus()== ServiceOrderStatusEnum.WAIT_PAY.getCode()){
            dto.setPay(iAppOrderPayService.getPay(order.getOrderId()));
        }
        if(order.getOrderStatus()==ServiceOrderStatusEnum.CANCELED.getCode() ||
                order.getOrderStatus()==ServiceOrderStatusEnum.CANCELING.getCode() ||
                order.getOrderStatus()==ServiceOrderStatusEnum.PART_CANCELED.getCode() ||
                order.getOrderStatus()==ServiceOrderStatusEnum.CANCEL_FAIL.getCode()){
            dto.setRefund(iAppOrderRefundService.getRefund(order.getOrderId()));
        }
        AppCouponCheckoutExample appCouponCheckoutExample = new AppCouponCheckoutExample();
        appCouponCheckoutExample.createCriteria()
                .andUseCouponOrderidEqualTo(order.getOrderId());
        /*.andCouponStatusEqualTo(CouponStatusEnum.NO_USE.getCode());*/
        List<AppCouponCheckout> appCouponCheckouts = appCouponCheckoutMapper.selectByExample(appCouponCheckoutExample);
        if(!appCouponCheckouts.isEmpty()){
            AppCouponCheckout appCouponCheckout = appCouponCheckouts.get(0);
            AppCouponExample appCouponExample = new AppCouponExample();
            appCouponExample.createCriteria()
                    .andCouponIdEqualTo(appCouponCheckout.getCouponId());
            List<AppCoupon> appCoupons = appCouponMapper.selectByExample(appCouponExample);
            dto.setCoupon(appCoupons.get(0));
        }
        return CommonResult.succeeded(dto);
    }

    @Override
    public int paySuccess(String orderId,String successTime,String transaction_id) {

        updateOrderStatus(orderId,ServiceOrderStatusEnum.PAY_SUCCESS.getCode(), "paySuccess");

        //保存支付情况
        AppOrderPay pay = new AppOrderPay();
        pay.setPayTime(LocalDateTime.parse(successTime));
        pay.setPayStatus((byte)1);
        AppOrderPayExample example = new AppOrderPayExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        appOrderPayMapper.updateByExampleSelective(pay,example);

        //关闭超时关闭订单操作定时任务
        SysTaskExample sysTaskExample =  new SysTaskExample();
        sysTaskExample.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
        // 这里获取任务信息数据
        List<SysTask> jobList = sysTaskMapper.selectByExample(sysTaskExample);
        for(SysTask sysTask : jobList){
            try {
                iScheduleJobService.deleteJob(sysTask);

                sysTask.setJobStatus(JobStatusEnum.CLOSED.getCode());
                SysTaskExample example1 = new SysTaskExample();
                example1.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
                sysTaskMapper.updateByExampleSelective(sysTask,example1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //这里需要识别订单号所在的团购情况
        int teamMin = 2;
        String teamNo =  _getTeamNo(orderId);
        List<AppServiceTeamOrder> orderList = _getOrdersByTeamNo(teamNo);
        int payNum = 0;
        for (AppServiceTeamOrder teamOrder : orderList) {
            if(teamOrder.getOrderStatus().shortValue() == ServiceOrderStatusEnum.PAY_SUCCESS.getCode()){
                payNum++;
            }
        }
        if(payNum>=teamMin){  //如果组团完成，发送卡券，并更新订单

            for (AppServiceTeamOrder teamOrder : orderList) {
                if(teamOrder.getOrderStatus().shortValue() == ServiceOrderStatusEnum.PAY_SUCCESS.getCode()){
                    List<AppServiceTeamSuborder> suborders = getItemIdByOrderId(teamOrder.getOrderId());
                    for (AppServiceTeamSuborder teamSuborder : suborders) {
                        List<AppServiceItemCoupon> listByServiceItem = iAppServiceItemCouponService.getListByServiceItem(teamSuborder.getServiceItemId());
                        for (AppServiceItemCoupon appServiceItemCoupon : listByServiceItem) {
                            iAppCouponCheckoutService.grant(appServiceItemCoupon.getCouponId()
                                    ,teamOrder.getUnionid()
                                    ,appServiceItemCoupon.getCouponNum(),CouponStatusEnum.NO_USE.getCode(),CouponCheckoutSourceEnum.TEAM_BUY.getCode(),teamSuborder.getOrderId());
                        }
                    }

                    updateOrderStatus(teamOrder.getOrderId(),ServiceOrderStatusEnum.FINISHED.getCode(), "paySuccess");
                }
            }

        }
        return 1;
    }

    private String _getTeamNo(String orderId) {
        AppServiceTeamOrderExample example = new AppServiceTeamOrderExample();
        example.createCriteria()
                .andOrderIdEqualTo(orderId);

        List<AppServiceTeamOrder> appServiceTeamOrders = appServiceTeamOrderMapper.selectByExample(example);

        if(appServiceTeamOrders.isEmpty()){
            return null;
        }
        AppServiceTeamOrder order = appServiceTeamOrders.get(0);
        return order.getTeamNo();
    }

    private List<AppServiceTeamOrder> _getOrdersByTeamNo(String teamNo){

        List<Short> statusList = new ArrayList<>();
        statusList.add(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        statusList.add(ServiceOrderStatusEnum.PAY_SUCCESS.getCode());
        statusList.add(ServiceOrderStatusEnum.FINISHED.getCode());
        statusList.add(ServiceOrderStatusEnum.CANCELING.getCode());
        statusList.add(ServiceOrderStatusEnum.CANCELED.getCode());
        AppServiceTeamOrderExample example = new AppServiceTeamOrderExample();
        example.createCriteria()
                .andTeamNoEqualTo(teamNo)
                .andOrderStatusIn(statusList);

        example.setOrderByClause(" create_time asc");
        List<AppServiceTeamOrder> appServiceTeamOrders = appServiceTeamOrderMapper.selectByExample(example);

        return appServiceTeamOrders;
    }

    @Override
    public int refundResult(String orderId, byte refundStatus,String successTime, String refundOrderId) {
        //查询退款订单信息
        List<AppOrderRefund> refunds = iAppOrderRefundService.getRefundList(orderId);
        AppOrderRefund refund_this = null;
        for (AppOrderRefund refund: refunds) {
            if(refund.getRefundOrderId().equals(refundOrderId)){
                refund_this = refund;
            }
        }

        short orderStatus = ServiceOrderStatusEnum.CANCELED.getCode();

        if(refund_this.getOrderAmount().doubleValue()<refund_this.getRefundAmount().doubleValue()){
            orderStatus = ServiceOrderStatusEnum.PART_CANCELED.getCode();
        }
        int update = updateOrderStatus(orderId,orderStatus, "refundNotifyUrl");

        AppOrderRefund refund = new AppOrderRefund();
        refund.setRefundStatus(OderRefundStatusEnum.REFUND_FINISHED.getCode());

        return iAppOrderRefundService.updateRefund(refund,refundOrderId);
    }

    @Override
    public int updateOrderStatus(String orderId,short orderStatus,String updateBy){
        AppServiceTeamOrder order = new  AppServiceTeamOrder();
        order.setOrderStatus(orderStatus);
        order.setUpdateBy(updateBy);

        AppServiceTeamOrderExample example = new AppServiceTeamOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        int update = appServiceTeamOrderMapper.updateByExampleSelective(order,example);
        if(update>0){
            AppServiceTeamSuborder suborder = new  AppServiceTeamSuborder();
            suborder.setSuborderStatus(orderStatus);
            suborder.setUpdateBy(updateBy);

            AppServiceTeamSuborderExample suborderExample = new AppServiceTeamSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(orderId);
            return update = appServiceTeamSuborderMapper.updateByExampleSelective(suborder,suborderExample);
        } else {
            return 0;
        }
    }

    @Override
    public CommonResult<?> cancelOrder(String orderId,String unionid) {

        return CommonResult.succeeded(updateOrderStatus(orderId,ServiceOrderStatusEnum.CANCELING.getCode(), unionid));
    }

    @Override
    public CommonResult<?> refundOrder(String orderId,String unionid,String refundOrderId,double refundAmount,String refundReason) {
        AppServiceTeamOrder order = _getOrder(orderId);
        int update = updateOrderStatus(orderId,ServiceOrderStatusEnum.CANCELING.getCode(), unionid);
        iAppOrderRefundService.createRefund(orderId,order.getOrderAmount(),refundOrderId,refundAmount,refundReason,unionid);
        return CommonResult.succeeded("");
    }

    private  AppServiceTeamSuborder getSuborder(String orderId){
        AppServiceTeamSuborderExample example = new AppServiceTeamSuborderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceTeamSuborder> list = appServiceTeamSuborderMapper.selectByExample(example);
        if(list.isEmpty()){
            return null;
        }
        return list.get(0);
    }
    public boolean checkOrder(String orderId, String unionid){
        //第一，校验此订单是否此用户的,todo 暂时不处理此块内容，便于测试期间异常退单
        //核实订单状态，并确认在规定时间内
        AppServiceTeamOrder order = _getOrder(orderId);
        if(order==null){
            return false;
        }
        if(order.getOrderStatus()!=ServiceOrderStatusEnum.PAY_SUCCESS.getCode()){
            return false;
        }

        return true;
    }

    @Override
    public boolean checkOrderAdmin(String orderId, String unionid) {
        return false;
    }

    @Override
    public CommonResult<?> updateBookTime(String orderId, String unionid, LocalDateTime bookTime) {
        return null;
    }

    @Override
    public List<AppServiceTeamSuborder> getItemIdByOrderId(String orderId) {
        AppServiceTeamSuborderExample suborderExample = new AppServiceTeamSuborderExample();
        suborderExample.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceTeamSuborder> suborders = appServiceTeamSuborderMapper.selectByExample(suborderExample);
       return suborders;
    }

    @Override
    public CommonResult<?> getOrderListByTeamNo(String teamNo,String unionid) {
        //1、获取用户信息
        //2、判断用户是新用户还是老用户，老用户重新跳转发起分享页面
        //以下是新用户发起流程
        //3、获取团购订单的信息，供页面端显示
        //以下是老用户
        //3、先判断是否是参团人，获取团购订单的信息，供页面端显示

        List<AppServiceTeamOrder> appServiceTeamOrders = getOrderListByTeamNo(teamNo);

        if(appServiceTeamOrders.isEmpty()){
            return CommonResult.failed(2000,"未找到组团信息");
        }
        Set<String> join_unionids = new HashSet<>();
        for (AppServiceTeamOrder order : appServiceTeamOrders ) {
            join_unionids.add(order.getUnionid());
        }
        AppServiceTeamOrder order = appServiceTeamOrders.get(0);

        AppServiceTeamSuborder suborder = getSuborder(order.getOrderId());

        boolean isNew = iCouponRuleService.isNew(unionid);

        byte status = ShareTeamOrderStatusEnum.SHAREING_WAITBUY.getCode();
        int teamMin = 2;

            if(order.getOrderStatus().shortValue() == ServiceOrderStatusEnum.CANCELED.getCode()
                    || order.getOrderStatus().shortValue() == ServiceOrderStatusEnum.CANCELING.getCode()){ //团购失败

                status = ShareTeamOrderStatusEnum.SHAREING_FAILED.getCode();
            } else if(order.getOrderStatus().shortValue() == ServiceOrderStatusEnum.PAY_SUCCESS.getCode()){ //团购中
                if(join_unionids.contains(unionid)){
                    status = ShareTeamOrderStatusEnum.SHAREING_BUYING_MYBUY.getCode();
                } else {
                    if(!isNew) {
                        status = ShareTeamOrderStatusEnum.SHAREING_NOPRIV.getCode();
                    }
                }
            } else if(order.getOrderStatus().shortValue() == ServiceOrderStatusEnum.FINISHED.getCode()){ //团购完成
                status = ShareTeamOrderStatusEnum.SHAREING_BUYED.getCode();
            }

        ShareTeamOrderDTO dto = new ShareTeamOrderDTO();
        dto.setTeamNo(teamNo);
        dto.setTeamNumbers(teamMin);
        dto.setNow(LocalDateTime.now());
        dto.setEndTime(order.getCreateTime().plusHours(2));
        dto.setOrders(appServiceTeamOrders);
        dto.setStatus(status);
        dto.setServiceItemId(suborder.getServiceItemId());
        dto.setIsCreator(order.getUnionid().equals(unionid)?1:0);

        return CommonResult.succeeded(dto);
    }

    @Override
    public List<AppServiceTeamOrder> getOrderListByTeamNo(String teamNo) {
        List<Short> statusList = new ArrayList<>();
        statusList.add(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        statusList.add(ServiceOrderStatusEnum.PAY_SUCCESS.getCode());
        statusList.add(ServiceOrderStatusEnum.FINISHED.getCode());
        statusList.add(ServiceOrderStatusEnum.CANCELING.getCode());
        statusList.add(ServiceOrderStatusEnum.CANCELED.getCode());

        AppServiceTeamOrderExample example = new AppServiceTeamOrderExample();
        example.createCriteria()
                .andTeamNoEqualTo(teamNo)
                .andOrderStatusIn(statusList);

        example.setOrderByClause(" create_time asc");
        List<AppServiceTeamOrder> appServiceTeamOrders = appServiceTeamOrderMapper.selectByExample(example);

        return appServiceTeamOrders;
    }
}
