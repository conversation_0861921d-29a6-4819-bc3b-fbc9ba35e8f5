package com.relle.service.impl;

import com.relle.enums.JobStatusEnum;
import com.relle.mbg.mapper.SysTaskMapper;
import com.relle.mbg.model.SysTask;
import com.relle.mbg.model.SysTaskExample;
import com.relle.service.IScheduleJobService;
import com.relle.service.ISysTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SysTaskServiceImpl implements ISysTaskService {
    @Resource
    private SysTaskMapper sysTaskMapper;
    @Autowired
    private IScheduleJobService iScheduleJobService;
    @Override
    public void close(String orderId) {
        SysTaskExample sysTaskExample =  new SysTaskExample();
        sysTaskExample.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
        // 这里获取任务信息数据
        List<SysTask> jobList = sysTaskMapper.selectByExample(sysTaskExample);
        for(SysTask sysTask : jobList){
            try {
                iScheduleJobService.deleteJob(sysTask);

                sysTask.setJobStatus(JobStatusEnum.CLOSED.getCode());
                SysTaskExample example1 = new SysTaskExample();
                example1.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
                sysTaskMapper.updateByExampleSelective(sysTask,example1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
