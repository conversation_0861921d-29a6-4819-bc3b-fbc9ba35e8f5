package com.relle.service.impl;

import com.relle.mbg.mapper.AppConfigMapper;
import com.relle.mbg.model.AppConfig;
import com.relle.mbg.model.AppConfigExample;
import com.relle.service.IAppConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AppConfigServiceImpl implements IAppConfigService {
    @Resource
    private AppConfigMapper appConfigMapper;
    @Override
    public  Map<String,Object> getConfigsByGroup(String groupNo) {
        AppConfigExample example = new AppConfigExample();
        example.createCriteria()
                .andGroupNoEqualTo(groupNo);
        List<AppConfig> appConfigs = appConfigMapper.selectByExample(example);
        Map<String,Object> result = new HashMap<>();
        for (AppConfig config : appConfigs ) {
            result.put(config.getRuleName(),config.getRuleValue());
        }
        return result;
    }
}
