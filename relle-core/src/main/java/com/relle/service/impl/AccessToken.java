package com.relle.service.impl;

import java.time.LocalDateTime;

public class AccessToken {
    private String token;
    private String refreshToken;
    private LocalDateTime validDateTime;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public LocalDateTime getValidDateTime() {
        return validDateTime;
    }

    public void setValidDateTime(LocalDateTime validDateTime) {
        this.validDateTime = validDateTime;
    }
}
