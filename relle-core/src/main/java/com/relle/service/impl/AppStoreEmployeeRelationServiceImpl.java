package com.relle.service.impl;

import com.relle.mbg.mapper.AppStoreEmployeeRelationMapper;
import com.relle.mbg.model.AppStoreEmployeeRelation;
import com.relle.mbg.model.AppStoreEmployeeRelationExample;
import com.relle.service.IAppStoreEmployeeRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

@Service
public class AppStoreEmployeeRelationServiceImpl implements IAppStoreEmployeeRelationService {
    @Resource
    private AppStoreEmployeeRelationMapper mapper;
    @Override
    public AppStoreEmployeeRelation getStoreEmployeeRelation(String employeeId,String storeId) {
        AppStoreEmployeeRelationExample example = new AppStoreEmployeeRelationExample();
        example.createCriteria()
                .andEmployeeIdEqualTo(employeeId)
                .andStoreIdEqualTo(storeId);
        List<AppStoreEmployeeRelation> appStoreEmployeeRelations = mapper.selectByExample(example);
        if(appStoreEmployeeRelations.isEmpty()){
            return null;
        } else {
            return appStoreEmployeeRelations.get(0);
        }
    }
}
