package com.relle.service.impl;

import com.relle.mbg.mapper.AppServiceItemCouponMapper;
import com.relle.mbg.model.AppServiceItemCoupon;
import com.relle.mbg.model.AppServiceItemCouponExample;
import com.relle.service.IAppServiceItemCouponService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppServiceItemCouponServiceImpl implements IAppServiceItemCouponService {
    @Resource
    private AppServiceItemCouponMapper appServiceItemCouponMapper;
    @Override
    public List<AppServiceItemCoupon> getListByServiceItem(String serviceItemId) {
        AppServiceItemCouponExample example = new AppServiceItemCouponExample();
        example.createCriteria()
                .andServiceItemIdEqualTo(serviceItemId);

        return appServiceItemCouponMapper.selectByExample(example);
    }
}
