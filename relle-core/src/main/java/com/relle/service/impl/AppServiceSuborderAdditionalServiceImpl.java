package com.relle.service.impl;

import com.relle.dto.AdditionalSuborderDTO;
import com.relle.enums.ServiceItemMediaTypeEnum;
import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.mbg.mapper.AppServiceSuborderAdditionalMapper;
import com.relle.mbg.model.*;
import com.relle.service.IAppCustomerInfoService;
import com.relle.service.IAppOrderPayService;
import com.relle.service.IAppServiceItemService;
import com.relle.service.IAppServiceOperationFeedbackService;
import com.relle.service.IAppServiceOperationRecordService;
import com.relle.service.IAppServiceOrderService;
import com.relle.service.IAppServiceSuborderAdditionalService;
import com.relle.service.IAppServiceSuborderService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class AppServiceSuborderAdditionalServiceImpl implements IAppServiceSuborderAdditionalService {
    @Resource
    private AppServiceSuborderAdditionalMapper appServiceSuborderAdditionalMapper;
    @Resource
    private IAppOrderPayService iAppOrderPayService;
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;
    @Resource
    private IAppServiceItemService iAppServiceItemService;
    @Resource
    private IAppServiceOperationRecordService iAppServiceOperationRecordService;
    @Resource
    private IAppServiceOperationFeedbackService ifeedbackService;
    @Resource
    private IAppServiceSuborderService iAppServiceSuborderService;
    @Resource
    private IAppServiceOrderService iAppServiceOrderService;

    @Override
    public AppServiceSuborderAdditional getOneBySuborderId(String suborderId) {
        AppServiceSuborderAdditionalExample example = new AppServiceSuborderAdditionalExample();
        example.createCriteria()
                .andSuborderIdEqualTo(suborderId);

        List<AppServiceSuborderAdditional> additionals = appServiceSuborderAdditionalMapper.selectByExample(example);
        if(additionals.isEmpty()){
            return null;
        }
        return additionals.get(0);
    }

    @Override
    public List<AdditionalSuborderDTO> getListByOrderId(String parentOrderId) {
        AppServiceSuborderAdditionalExample example = new AppServiceSuborderAdditionalExample();
        example.createCriteria()
                .andOrderIdEqualTo(parentOrderId)
                .andDeletedEqualTo((byte)0);

        List<AppServiceSuborderAdditional> additionals = appServiceSuborderAdditionalMapper.selectByExample(example);
        List<AdditionalSuborderDTO> dtoList = new ArrayList<>();

        AppServiceSuborder suborder = iAppServiceSuborderService.getSuborderBySuborderId(parentOrderId);
        if(suborder == null){
            return dtoList;
        }
        AppServiceOrder order = iAppServiceOrderService.getOrder(suborder.getOrderId());
        if(order == null){
            return dtoList;
        }

        for (AppServiceSuborderAdditional suborderAdditional :additionals) {

            AppCustomerInfo customerInfo = new AppCustomerInfo();
            customerInfo.setWechatNickname(order.getContactName());
            customerInfo.setWechatPhone(order.getContactPhone());

            AppServiceItem item = iAppServiceItemService.getItem(suborderAdditional.getServiceItemId());


            String customerName = order.getContactName();
            String customerPhone = order.getContactPhone();

            AppCustomerInfo info = iAppCustomerInfoService.selectByUnionid(suborderAdditional.getUnionid());
            info.setWechatPhone(customerPhone);
            info.setWechatNickname(customerName);

            AdditionalSuborderDTO dto1 = new AdditionalSuborderDTO();
            dto1.setAdditionalSuborder(suborderAdditional);
            dto1.setServiceItem(iAppServiceItemService.getItem(suborderAdditional.getServiceItemId()));
            dto1.setThumbnail(iAppServiceItemService.getOneServiceMediaRelation(suborderAdditional.getServiceItemId(), ServiceItemMediaTypeEnum.THUMBNAIL.getCode()));
            dto1.setCustomerInfo(customerInfo);

            AppServiceOperationRecord additionalRecord = iAppServiceOperationRecordService.getOperationRecord(suborderAdditional.getSuborderId());
            dto1.setOperationRecord(additionalRecord);

            dtoList.add(dto1);
        }
        return dtoList;
    }

    @Override
    public List<AppServiceSuborderAdditional> getAdditionalListByOrderId(String parentOrderId) {
        AppServiceSuborderAdditionalExample example = new AppServiceSuborderAdditionalExample();
        example.createCriteria()
                .andOrderIdEqualTo(parentOrderId)
                .andDeletedEqualTo((byte)0);

        List<AppServiceSuborderAdditional> additionals = appServiceSuborderAdditionalMapper.selectByExample(example);
        if(additionals.isEmpty()){
            additionals = new ArrayList<>();
        }
        return additionals;
    }

    @Override
    public AppServiceSuborderAdditional save(AppServiceSuborderAdditional serviceSuborderAdditional) {
        serviceSuborderAdditional.setCreateTime(LocalDateTime.now());
        serviceSuborderAdditional.setUpdateTime(LocalDateTime.now());
        appServiceSuborderAdditionalMapper.insertSelective(serviceSuborderAdditional);
        return serviceSuborderAdditional;
    }

    @Override
    public int update(AppServiceSuborderAdditional serviceSuborderAdditional) {
        serviceSuborderAdditional.setUpdateTime(LocalDateTime.now());
        return appServiceSuborderAdditionalMapper.updateByPrimaryKeySelective(serviceSuborderAdditional);
    }

    private int updateOrderStatus(String suborderId,short orderStatus,String updateBy){

        AppServiceSuborderAdditional suborder = new  AppServiceSuborderAdditional();
        suborder.setSuborderStatus(orderStatus);
        suborder.setUpdateBy(updateBy);
        suborder.setUpdateTime(LocalDateTime.now());
        AppServiceSuborderAdditionalExample suborderExample = new AppServiceSuborderAdditionalExample();
        suborderExample.createCriteria()
            .andSuborderIdEqualTo(suborderId);
        return  appServiceSuborderAdditionalMapper.updateByExampleSelective(suborder,suborderExample);

    }

    @Override
    public int paySuccess(String suborderId, String successTime, String transaction_id) {
        int r = this.updateOrderStatus(suborderId, ServiceOrderStatusEnum.PAY_SUCCESS.getCode(), "paySuccess");
        //保存支付情况
        AppOrderPay pay = new AppOrderPay();
        pay.setPayTime(LocalDateTime.parse(successTime));
        pay.setPayStatus((byte)1);
        pay.setOrderId(suborderId);    //这里采用子订单编号

        iAppOrderPayService.updatePay(pay);

        return r;
    }

    @Override
    public boolean checkOrderCanCancel(AppServiceSuborderAdditional suborderAdditional) {
        if(suborderAdditional == null) return false;
        if(suborderAdditional.getSuborderStatus() != ServiceOrderStatusEnum.WAIT_PAY.getCode()){
            return false;
        }
        return true;
    }
}
