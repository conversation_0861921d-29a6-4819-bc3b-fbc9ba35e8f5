package com.relle.service;

import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppServiceItemVO;
import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppServiceMedia;

/**
 * App Service Item Service Interface
 * Handles service item operations
 */
public interface IAppServiceItemService {

    /**
     * List all items by store
     * @param storeId store ID
     * @param unionid user union ID
     * @return list of service items
     */
    CommonResult<?> listAllItemByStore(String storeId, String unionid);

    /**
     * List all items by store and category
     * @param storeId store ID
     * @param serviceItemCategory service item category
     * @param unionid user union ID
     * @return list of service items
     */
    CommonResult<?> listAllItemByStore(String storeId, Byte serviceItemCategory, String unionid);

    /**
     * Get one service item by ID
     * @param storeId store ID
     * @param serviceItemId service item ID
     * @param unionid user union ID
     * @return service item details
     */
    CommonResult<?> getOneByServiceItemId(String storeId, String serviceItemId, String unionid);

    /**
     * Get item by service item ID
     * @param serviceItemId service item ID
     * @return service item details
     */
    AppServiceItem getItem(String serviceItemId);

    /**
     * List all items by category
     * @param category category code
     * @return list of items
     */
    List<AppServiceItemVO> listAllItemByCategory(byte category);

    /**
     * Get item detail
     * @param itemId item ID
     * @param storeId store ID
     * @return item detail
     */
    AppServiceItemVO getItemDetail(String itemId, String storeId);

    /**
     * Get item VO
     * @param serviceItemId service item ID
     * @return item VO
     */
    AppServiceItemVO getItemVO(String serviceItemId);

    /**
     * Update service item
     * @param item service item
     * @return update result
     */
    CommonResult<?> update(Object item);

    /**
     * Insert service item
     * @param item service item
     * @return insert result
     */
    CommonResult<?> insert(Object item);

    /**
     * Add coupon to service item
     * @param item service item
     * @param coupon coupon
     * @param num number
     * @return result
     */
    CommonResult<?> addCoupon(Object item, Object coupon, Object num);

    AppServiceMedia getOneServiceMediaRelation(String serviceItemId, String code);
}
