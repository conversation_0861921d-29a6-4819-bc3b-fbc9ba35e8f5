package com.relle.service;

import com.relle.mbg.model.MtSession;

/**
 * Meituan Session Service Interface
 * Handles session management for Meituan API integration
 */
public interface IMtSessionService {

    /**
     * Initialize session
     */
    void initSession();

    /**
     * Refresh session token
     * @return success status
     */
    boolean refreshSession();

    /**
     * Get current session token
     * @return session token
     */
    String getSessionToken();

    /**
     * Validate session
     * @param sessionId session ID to validate
     * @return validation result
     */
    boolean validateSession(String sessionId);

    // Additional methods needed by controllers
    /**
     * Get session
     * @return session object
     */
    MtSession get();

    /**
     * Save session
     * @param session session to save
     * @return save result
     */
    int save(MtSession session);

    /**
     * Update session
     * @param session session to update
     * @return update result
     */
    int update(MtSession session);
}
