package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppCoupon;
import com.relle.mbg.model.AppServiceItem;

/**
 * App Coupon Service Interface
 */
public interface AppCouponService {
    
    /**
     * Get coupon by ID
     * @param couponId coupon ID
     * @return coupon data
     */
    CommonResult<?> getCouponById(String couponId);
    
    /**
     * Get user coupons
     * @param userId user ID
     * @return user coupons
     */
    CommonResult<?> getUserCoupons(String userId);
    
    /**
     * Create coupon
     * @param couponData coupon data
     * @return creation result
     */
    CommonResult<?> createCoupon(Object couponData);
    
    /**
     * Update coupon
     * @param couponData coupon data
     * @return update result
     */
    CommonResult<?> updateCoupon(Object couponData);
    
    /**
     * Use coupon
     * @param couponId coupon ID
     * @param userId user ID
     * @return usage result
     */
    CommonResult<?> useCoupon(String couponId, String userId);

    AppCoupon getDKCouponBySerivceItem(String serviceId);

    AppCoupon save(AppServiceItem serviceItem);

    void saveRelation(AppServiceItem serviceItem, AppCoupon coupon);
}
