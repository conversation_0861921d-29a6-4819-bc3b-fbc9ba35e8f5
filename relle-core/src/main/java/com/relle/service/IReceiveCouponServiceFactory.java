package com.relle.service;

import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class IReceiveCouponServiceFactory {
    @Resource
    private ApplicationContext applicationContext;
    public IReceieveCouponService getService(String rule){
        return (IReceieveCouponService)applicationContext.getBean(rule);
    }

}
