package com.relle.service;

import org.springframework.web.multipart.MultipartFile;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppTestMedia;

/**
 * App Test Media Service Interface
 */
public interface IAppTestMediaService {
    
    /**
     * Get test media by ID
     * @param id media ID
     * @return test media data
     */
    CommonResult<?> getTestMediaById(String id);
    
    /**
     * Create test media
     * @param mediaData media data
     * @return creation result
     */
    CommonResult<?> createTestMedia(Object mediaData);
    
    /**
     * Update test media
     * @param mediaData media data
     * @return update result
     */
    CommonResult<?> updateTestMedia(Object mediaData);
    
    /**
     * Delete test media
     * @param id media ID
     * @return deletion result
     */
    CommonResult<?> deleteTestMedia(String id);

    AppTestMedia getMediaByMd5(String md5);

    CommonResult<?> saveMedia(String md5, MultipartFile file, String openid, String storeId);
}
