package com.relle.service;

import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.dto.SkinDetectorTestVO;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.mbg.model.AppServiceOperationRecord;

/**
 * Skin Detector Test Service Interface
 */
public interface ISkinDetectorTestService {
    
    /**
     * Get skin detector test by ID
     * @param testId test ID
     * @return skin detector test data
     */
    CommonResult<?> getSkinDetectorTestById(String testId);
    
    /**
     * Get skin detector tests by user ID
     * @param userId user ID
     * @return skin detector test list
     */
    CommonResult<?> getSkinDetectorTestsByUserId(String userId);
    
    /**
     * Create skin detector test
     * @param testData test data
     * @return creation result
     */
    CommonResult<?> createSkinDetectorTest(Object testData);
    
    /**
     * Update skin detector test
     * @param testData test data
     * @return update result
     */
    CommonResult<?> updateSkinDetectorTest(Object testData);
    
    /**
     * Delete skin detector test
     * @param testId test ID
     * @return deletion result
     */
    CommonResult<?> deleteSkinDetectorTest(String testId);

    List<SkinDetectorTestVO> getMediaFromClientData(AppCustomerInfo customerInfo,
            AppServiceOperationRecord operationRecord);
}
