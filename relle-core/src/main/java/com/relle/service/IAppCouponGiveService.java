package com.relle.service;

import com.relle.dto.AppCouponShareDTO;
import com.relle.mbg.model.AppCouponCheckout;
import com.relle.mbg.model.AppCouponGive;
import com.relle.mbg.model.AppCustomerInfo;

import java.util.List;

public interface IAppCouponGiveService {
    AppCouponGive share(AppCouponCheckout checkout, AppCustomerInfo customerInfo);
    int receive(Long giveId, AppCustomerInfo customerInfo);
    int back(Long giveId);

    AppCouponGive getShareRecordById(Long giveId);
    AppCouponGive getShareRecordByCouponNo(Long couponNo);

    List<AppCouponGive> getShareRecord(String unionid);

    int canReceive(AppCouponGive appCouponGive);

    AppCouponShareDTO getShareRecordDetailById(Long giveId);

    List<AppCouponGive> getListByStatus(Byte status);
}
