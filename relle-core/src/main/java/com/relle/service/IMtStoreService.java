package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.MtStore;

/**
 * Meituan Store Service Interface
 * Handles store operations for Meituan integration
 */
public interface IMtStoreService {

    /**
     * Get store information
     * @param storeId store ID
     * @return store information
     */
    CommonResult<?> getStoreInfo(String storeId);

    /**
     * Search stores by location
     * @param latitude latitude
     * @param longitude longitude
     * @param radius search radius
     * @return search results
     */
    CommonResult<?> searchStoresByLocation(double latitude, double longitude, int radius);

    /**
     * Validate store
     * @param storeId store ID
     * @return validation result
     */
    boolean validateStore(String storeId);

    // Additional methods needed by controllers
    /**
     * Get Meituan store by store ID
     * @param storeId store ID
     * @return MtStore object
     */
    MtStore getMtStoreByStoreId(String storeId);
}
