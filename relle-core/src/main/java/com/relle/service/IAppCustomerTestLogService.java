package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppCustomerTestLog;

/**
 * App Customer Test Log Service Interface
 */
public interface IAppCustomerTestLogService {
    
    /**
     * Get customer test log by ID
     * @param logId log ID
     * @return customer test log data
     */
    CommonResult<?> getCustomerTestLogById(String logId);
    
    /**
     * Get customer test logs by customer ID
     * @param customerId customer ID
     * @return customer test log list
     */
    CommonResult<?> getCustomerTestLogsByCustomerId(String customerId);
    
    /**
     * Create customer test log
     * @param logData log data
     * @return creation result
     */
    CommonResult<?> createCustomerTestLog(Object logData);
    
    /**
     * Update customer test log
     * @param logData log data
     * @return update result
     */
    CommonResult<?> updateCustomerTestLog(Object logData);
    
    /**
     * Delete customer test log
     * @param logId log ID
     * @return deletion result
     */
    CommonResult<?> deleteCustomerTestLog(String logId);

    AppCustomerTestLog getByOperationId(String operationRecordId);
}
