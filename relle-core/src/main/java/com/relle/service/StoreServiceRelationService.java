package com.relle.service;

import java.util.List;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppStoreServiceRelation;

/**
 * Store Service Relation Service Interface
 */
public interface StoreServiceRelationService {
    
    /**
     * Get store service relations by store ID
     * @param storeId store ID
     * @return store service relations
     */
    CommonResult<?> getStoreServiceRelationsByStoreId(String storeId);
    
    /**
     * Create store service relation
     * @param relationData relation data
     * @return creation result
     */
    CommonResult<?> createStoreServiceRelation(Object relationData);
    
    /**
     * Update store service relation
     * @param relationData relation data
     * @return update result
     */
    CommonResult<?> updateStoreServiceRelation(Object relationData);
    
    /**
     * Delete store service relation
     * @param relationId relation ID
     * @return deletion result
     */
    CommonResult<?> deleteStoreServiceRelation(String relationId);

    List<AppStoreServiceRelation> findByServiceItemId(String serviceItemId);

    void deleteAll(List<AppStoreServiceRelation> serviceRelationList);

    void save(AppStoreServiceRelation relation);
}
