package com.relle.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import javax.validation.Valid;

import com.relle.common.api.CommonResult;
import com.relle.dto.AccountReconciliationOrderVO;
import com.relle.dto.AppServiceOrderDTO;
import com.relle.dto.OrderDTO;
import com.relle.dto.OrderDao;
import com.relle.dto.OrderQueryDTO;
import com.relle.mbg.model.AppServiceOrder;
import com.relle.mbg.model.AppServiceSuborder;

/**
 * App Service Order Service Interface
 * Handles service order operations
 */
public interface IAppServiceOrderService {

    /**
     * Get suborder by ID
     * @param suborderId suborder ID
     * @return suborder entity
     */
    AppServiceSuborder getSuborder(String suborderId);

    /**
     * Create order
     * @param orderData order data
     * @return creation result
     */
    CommonResult<?> createOrder(Object orderData);

    /**
     * Get order details
     * @param orderId order ID
     * @return order details
     */
    CommonResult<?> getOrderDetails(String orderId);

    // Additional methods needed by controllers
    CommonResult<?> getServiceOrderList(String storeId, String unionid, String status);
    CommonResult<?> getServiceOrder(String orderId);
    CommonResult<?> checkOrder(String orderId, String unionid);
    CommonResult<?> updateOrderFinished(String orderId, String unionid, String status);
    AppServiceOrder getOrder(String orderId);

    void updateOrderStatus(String orderId, short code, String string);

    List<AppServiceSuborder> getSubOrders(String orderId);

    AppServiceOrderDTO getOrderDetailDTO(String orderId);

    List<OrderDTO> getFinishedOrder(String storeId, String openid, String queryDate);

    void refundResult(String orderId, byte code, String success_time, String refundOrderId);

    CommonResult getOrderDetail(String orderId);

    CommonResult<?> getOrderList(String unionid);

    CommonResult<?> updateBookTime(String orderId, String unionid_from_session, LocalDateTime bookTimeDate);

    List<OrderDTO> getFinishedOrder(String storeId, LocalDate startDate, LocalDate endDate, short code);

    Object uploadReport(OrderDao dao);

    int createPDF(String operationRecordId);

    List<OrderDTO> getFinishedOrder(String storeId, String format, String format2, short code);

	Collection<? extends AccountReconciliationOrderVO> getOrderListOfAccountReconciliation(OrderQueryDTO query);
}
