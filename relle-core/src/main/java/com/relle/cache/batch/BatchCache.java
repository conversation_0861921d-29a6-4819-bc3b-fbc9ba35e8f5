package com.relle.cache.batch;

import com.relle.mbg.model.AppAvailableTime;
import com.relle.mbg.model.AppServiceSuborder;
import com.relle.mbg.model.AppStoreInfo;
import com.relle.mbg.model.AppStoreRoom;
import com.relle.service.IAppAvailableTimeService;
import com.relle.service.IAppConfigService;
import com.relle.service.IAppServiceSuborderService;
import com.relle.service.IAppStoreInfoService;
import com.relle.service.IAppStoreRoomSerivce;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Component
public class BatchCache {
    IAppServiceSuborderService iAppServiceSuborderService;
    IAppStoreRoomSerivce iAppStoreRoomSerivce;
    IAppAvailableTimeService iAppAvailableTimeService;
    IAppStoreInfoService iAppStoreInfoService;
    IAppConfigService iAppConfigService;

    private Map<String, Object> configsByGroup;
    private Map<String, Map<String, Map<String, List<Batch>>>> storeDateRoomBatchMap = new HashMap<>();

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-ddHHmm");

    private BatchCache(IAppServiceSuborderService iAppServiceSuborderService, IAppStoreRoomSerivce iAppStoreRoomSerivce,
            IAppAvailableTimeService iAppAvailableTimeService, IAppStoreInfoService iAppStoreInfoService,
            IAppConfigService iAppConfigService) {
        this.iAppConfigService = iAppConfigService;
        this.iAppServiceSuborderService = iAppServiceSuborderService;
        this.iAppStoreInfoService = iAppStoreInfoService;
        this.iAppStoreRoomSerivce = iAppStoreRoomSerivce;
        this.iAppAvailableTimeService = iAppAvailableTimeService;
        _init();
    }

    public void reload() {
        storeDateRoomBatchMap.clear();
        _init();
    }

    private void _init() {
        configsByGroup = iAppConfigService.getConfigsByGroup("appointment.service.all");
        int offsetDay = Integer.parseInt((String) configsByGroup.get("offset.day"));
        int appointmentDays = Integer.parseInt((String) configsByGroup.get("appointment.days"));
        // int timeLength = Integer.parseInt((String)
        // configsByGroup.get("service.timeLength"));
        // int interval = Integer.parseInt((String)
        // configsByGroup.get("appointment.interval"));

        List<AppStoreInfo> storeList = iAppStoreInfoService.getStoreList();
        for (int i = 0; i < storeList.size(); i++) {
            AppStoreInfo storeInfo = storeList.get(i);
            List<AppStoreRoom> roomListByStore = iAppStoreRoomSerivce.getRoomListByStore(storeInfo.getStoreId());
            LocalDate startDate = LocalDate.now().plusDays(offsetDay);
            LocalDate endDate = startDate.plusDays(appointmentDays);
            while (!startDate.isAfter(endDate)) {
                String dateStr = startDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                // String dateStr = startDate.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                List<AppAvailableTime> availableTimeByDate = iAppAvailableTimeService.getAvailableTimeByDate(startDate);
                _initBatchMap(storeInfo.getStoreId(), dateStr, roomListByStore, availableTimeByDate);
                _initOrder(storeInfo.getStoreId(), startDate);
                startDate = startDate.plusDays(1);
            }
        }
    }

    private void _initBatchMap(String storeId, String date, List<AppStoreRoom> roomList,
            List<AppAvailableTime> timeList) {

        Map<String, Map<String, List<Batch>>> dateRoomBatchMap = storeDateRoomBatchMap.get(storeId);
        if (dateRoomBatchMap == null) {
            dateRoomBatchMap = new HashMap<>();
            storeDateRoomBatchMap.put(storeId, dateRoomBatchMap);
        }

        Map<String, List<Batch>> roomBatchMap = dateRoomBatchMap.get(date);
        if (roomBatchMap == null) {
            roomBatchMap = new HashMap<>();
            dateRoomBatchMap.put(date, roomBatchMap);
        }

        for (int i = 0; i < roomList.size(); i++) {
            AppStoreRoom room = roomList.get(i);
            List<Batch> batchList = roomBatchMap.get(room.getRoomId());
            if (batchList == null) {
                batchList = new ArrayList<>();
                roomBatchMap.put(room.getRoomId(), batchList);
            }
            List<AppAvailableTime> newTimeList = _copyList(timeList);
            for (int j = 0; j < timeList.size(); j++) {
                Batch batch = new Batch();
                batch.setStoreId(storeId);
                batch.setRoomId(room.getRoomId());
                batch.setAppAvailableTime(newTimeList.get(j));
                batchList.add(batch);
            }
        }

    }

    private void _initOrder(String storeId, LocalDate start) {
        List<AppServiceSuborder> suborders = iAppServiceSuborderService.getValidSuborderByDate(storeId, start);
        for (int i = 0; i < suborders.size(); i++) {
            AppServiceSuborder suborder = suborders.get(i);
            comfirmSuborderTime(suborder);
        }
    }

    public void comfirmSuborderTime(AppServiceSuborder suborder) {
        LocalDateTime bookTime = suborder.getBookTime();
        LocalDate startDate = suborder.getBookTime().toLocalDate();
        String startDateStr = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        String storeId = suborder.getStoreId();
        String roomId = suborder.getRoomId();

        Map<String, Map<String, List<Batch>>> dateRoomBatchMap = storeDateRoomBatchMap.get(storeId);
        Map<String, List<Batch>> roomBatchMapStartDate = dateRoomBatchMap.get(startDateStr);

        List<Batch> batchListOfRoom = roomBatchMapStartDate.get(roomId);

        // In the confirmSuborderTime method:
        LocalDateTime start = bookTime.minusSeconds(1);
        LocalDateTime end = bookTime.plusHours(1).minusSeconds(1);

        for (Batch batch : batchListOfRoom) {
            AppAvailableTime appAvailableTime = batch.getAppAvailableTime();
            LocalDateTime batchDateTime = LocalDateTime.from(appAvailableTime.getAvailableDate())
                    .withHour(appAvailableTime.getAvailableTime().getHour())
                    .withMinute(appAvailableTime.getAvailableTime().getMinute());

            // Parse the available date+time using the formatter

            // Check if the batch time falls within the interval
            if (batchDateTime.isAfter(start) && batchDateTime.isBefore(end)) {
                appAvailableTime.setStatus((byte) 0);
            }
        }
    }

    public String getRoomId(String storeId, String dateStr) {
        try {
            LocalDateTime bookTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            return getRoomId(storeId, bookTime.toLocalDate());
        } catch (DateTimeParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean canChangeRoom(String roomId, AppServiceSuborder suborder) {
        LocalDateTime bookTime = suborder.getBookTime();
        // Create time window: 1 second before to 1 hour minus 1 second after booking
        // time
        LocalDateTime start = bookTime.minusSeconds(1);
        LocalDateTime end = bookTime.plusHours(1).minusSeconds(1);

        // Format date string using LocalDateTime
        String yyyyMMdd = bookTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Map<String, Map<String, List<Batch>>> dateRoomBatchMap = storeDateRoomBatchMap.get(suborder.getStoreId());
        if (dateRoomBatchMap == null || dateRoomBatchMap.isEmpty()) {
            return false;
        }
        Map<String, List<Batch>> roomBatchMap = dateRoomBatchMap.get(yyyyMMdd);
        if (roomBatchMap == null || roomBatchMap.isEmpty()) {
            return false;
        }
        List<Batch> batchList = roomBatchMap.get(roomId);
        if (batchList == null || batchList.isEmpty()) {
            return false;
        }

        // Check if any batch in the time window is unavailable (status = 0)
        for (Batch batch : batchList) {
            AppAvailableTime appAvailableTime = batch.getAppAvailableTime();
            LocalDateTime batchDateTime = LocalDateTime.from(appAvailableTime.getAvailableDate())
                    .withHour(appAvailableTime.getAvailableTime().getHour())
                    .withMinute(appAvailableTime.getAvailableTime().getMinute());

            boolean isInTimeWindow = batchDateTime.isAfter(start) && batchDateTime.isBefore(end);
            boolean isUnavailable = appAvailableTime.getStatus().byteValue() == 0;

            if (isInTimeWindow && isUnavailable) {
                return false; // Room cannot be changed if any batch is unavailable
            }
        }
        return true; // Room can be changed if all batches are available
    }

    public String getRoomId(String storeId, LocalDate bookDate) {
        // Create time window: 1 second before to 1 hour minus 1 second after booking
        // time
        LocalDateTime start = bookDate.atStartOfDay().minusSeconds(1);
        LocalDateTime end = start.plusHours(1).minusSeconds(1);

        // Format date string
        String yyyyMMdd = bookDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // Get room batch map
        Map<String, Map<String, List<Batch>>> dateRoomBatchMap = storeDateRoomBatchMap.get(storeId);
        if (dateRoomBatchMap == null) {
            return null;
        }

        Map<String, List<Batch>> roomBatchMap = dateRoomBatchMap.get(yyyyMMdd);
        if (roomBatchMap == null) {
            return null;
        }

        // Find available room
        for (Map.Entry<String, List<Batch>> entry : roomBatchMap.entrySet()) {
            String roomId = entry.getKey();
            List<Batch> batchList = entry.getValue();

            boolean isRoomAvailable = true;

            // Check if room is available during the time window
            for (Batch batch : batchList) {
                AppAvailableTime appAvailableTime = batch.getAppAvailableTime();
                LocalDateTime batchDateTime = LocalDateTime.from(appAvailableTime.getAvailableDate())
                        .withHour(appAvailableTime.getAvailableTime().getHour())
                        .withMinute(appAvailableTime.getAvailableTime().getMinute());

                boolean isInTimeWindow = batchDateTime.isAfter(start) && batchDateTime.isBefore(end);
                boolean isUnavailable = appAvailableTime.getStatus().byteValue() == 0;

                if (isInTimeWindow && isUnavailable) {
                    isRoomAvailable = false;
                    break;
                }
            }

            // If room is available, lock it and return the room ID
            if (isRoomAvailable) {
                // Lock the room by setting status to 0 for the time window
                for (Batch batch : batchList) {
                    AppAvailableTime appAvailableTime = batch.getAppAvailableTime();
                    LocalDateTime batchDateTime = LocalDateTime.from(appAvailableTime.getAvailableDate())
                            .withHour(appAvailableTime.getAvailableTime().getHour())
                            .withMinute(appAvailableTime.getAvailableTime().getMinute());

                    if (batchDateTime.isAfter(start) && batchDateTime.isBefore(end)) {
                        appAvailableTime.setStatus((byte) 0);
                    }
                }
                return roomId;
            }
        }
        return null;
    }

    private LocalDateTime[] getTimeWindow(LocalDate date) {
        LocalDateTime start = date.atStartOfDay().minusSeconds(1);
        LocalDateTime end = start.plusHours(1).minusSeconds(1);
        return new LocalDateTime[] { start, end };
    }

    public void releaseRoomId(String storeId, LocalDate bookDate, String roomId) {
        LocalDateTime[] interval = getTimeWindow(bookDate);

        String yyyyMMdd = bookDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Map<String, Map<String, List<Batch>>> dateRoomBatchMap = storeDateRoomBatchMap.get(storeId);
        if (dateRoomBatchMap == null) {
            return;
        }
        Map<String, List<Batch>> roomBatchMap = dateRoomBatchMap.get(yyyyMMdd);
        if (roomBatchMap == null) {
            return;
        }
        List<Batch> batchList = roomBatchMap.get(roomId);
        if (batchList == null) {
            return;
        }
        for (Batch batch : batchList) {
            AppAvailableTime appAvailableTime = batch.getAppAvailableTime();
            LocalDateTime dateTime = LocalDateTime.from(appAvailableTime.getAvailableDate())
                    .withHour(appAvailableTime.getAvailableTime().getHour())
                    .withMinute(appAvailableTime.getAvailableTime().getMinute());
            if (dateTime.isAfter(interval[0]) && dateTime.isBefore(interval[1])) {
                appAvailableTime.setStatus((byte) 1);
            }
        }
    }

    public List<AppAvailableTime> getAppAvailableTimes(String storeId, String serviceItemId, String date) {
        Map<String, Map<String, List<Batch>>> dateRoomBatchMap = storeDateRoomBatchMap.get(storeId);
        if (dateRoomBatchMap == null) {
            return null;
        }
        Map<String, List<Batch>> roomBatchMap = dateRoomBatchMap.get(date);
        if (roomBatchMap == null) {
            return null;
        }
        List<AppAvailableTime> timeList = new ArrayList<>();
        Iterator<String> iterator = roomBatchMap.keySet().iterator();
        // Set<Short> timeSet = new HashSet<>();
        Map<Short, AppAvailableTime> timeMap = new HashMap<>();
        while (iterator.hasNext()) {
            String roomId = iterator.next();
            List<Batch> batchList = roomBatchMap.get(roomId);
            for (Batch batch : batchList) {
                AppAvailableTime time = batch.getAppAvailableTime();

                AppAvailableTime appAvailableTime = timeMap.get(Short.valueOf(time.getAvailableTime().format(formatter)));
                if (appAvailableTime == null) {
                    appAvailableTime = time;
                    timeMap.put(Short.valueOf(time.getAvailableTime().format(formatter)), time);
                }
                if (appAvailableTime.getStatus().byteValue() == 0 && time.getStatus().byteValue() == 1) {
                    timeMap.put(Short.valueOf(time.getAvailableTime().format(formatter)), time);
                }
            }
        }
        timeList.addAll(timeMap.values());
        timeList = _copyList(timeList);
        Collections.sort(timeList, new Comparator<AppAvailableTime>() {
            @Override
            public int compare(AppAvailableTime o1, AppAvailableTime o2) {
                return o1.getAvailableTime().isAfter(o2.getAvailableTime()) ? 1 : -1;
            }
        });

        // 这里还需要处理下，对部分可用时长小于单个项目服务时长的，进行过滤
        int timeLength = Integer.parseInt((String) configsByGroup.get("service.timeLength"));

        short validTimeLength = (short) (timeLength / 60.0 * 100); // 转换成100进制的分钟差

        for (int i = 1; i < timeList.size(); i++) {
            AppAvailableTime time = timeList.get(i);
            if (time.getStatus().byteValue() == 0) {
                for (int j = 0; j < i; j++) {
                    AppAvailableTime time1 = timeList.get(j);
                    if (time.getAvailableTime().isBefore(time1.getAvailableTime())) {
                        time1.setStatus((byte) 0);
                    }
                }

            }
        }
        return timeList;
    }

    private List<AppAvailableTime> _copyList(List<AppAvailableTime> list) {
        List<AppAvailableTime> returnList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            AppAvailableTime time = list.get(i);
            AppAvailableTime temp = new AppAvailableTime();
            BeanUtils.copyProperties(time, temp);
            returnList.add(temp);
        }
        return returnList;
    }

    public String toString() {
        return storeDateRoomBatchMap.toString();
    }
}
