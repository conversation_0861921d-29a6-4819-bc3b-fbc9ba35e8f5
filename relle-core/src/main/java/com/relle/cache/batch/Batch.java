package com.relle.cache.batch;


import com.relle.mbg.model.AppAvailableTime;

public class Batch {
    private String storeId;
    private AppAvailableTime appAvailableTime;
    private String roomId;

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public AppAvailableTime getAppAvailableTime() {
        return appAvailableTime;
    }

    public void setAppAvailableTime(AppAvailableTime appAvailableTime) {
        this.appAvailableTime = appAvailableTime;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public static void main(String[] args) {
        /*DateTime now = new DateTime();
        DateTime startDate = new DateTime();
        DateTime endDate = startDate.plus(Months.months(2));


        DateTime startDate2 = now.minus(Hours.hours(2));
        DateTime endDate2 = now.plus(Hours.hours(2));
        // 创建从开始到结束瞬间的间隔。
        Interval interval = new Interval(startDate, endDate);
        Interval interval2 = new Interval(startDate2, now);

        System.out.println(interval2.overlap(interval));
        // 将间隔再增加一个月
        interval = interval.withEnd(interval.getEnd().plusMonths(1));*/
    }
}
