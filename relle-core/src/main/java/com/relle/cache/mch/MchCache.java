package com.relle.cache.mch;

import com.relle.mbg.model.*;
import com.relle.service.IAppMchAccountConfigService;
import com.relle.service.IAppMchAccountService;

import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class MchCache {
    IAppMchAccountService iAppMchAccountService;
    IAppMchAccountConfigService iAppMchAccountConfigService;

    private Map<String, Map<Byte, AppMchAccount>> storeCategoryMchAccountMap = new HashMap<>();

    private MchCache(IAppMchAccountService iAppMchAccountService, IAppMchAccountConfigService iAppMchAccountConfigService){
        this.iAppMchAccountService = iAppMchAccountService;
        this.iAppMchAccountConfigService = iAppMchAccountConfigService;
        _init();
    }

    private void _init(){

        List<AppMchAccountConfig> mchAccountConfigs = iAppMchAccountConfigService.getAll();
        List<AppMchAccount> appMchAccounts = iAppMchAccountService.getAll();
        Map<String,AppMchAccount> accountMap = new HashMap<>();
        for (int i = 0; i < appMchAccounts.size(); i++) {
            AppMchAccount account = appMchAccounts.get(i);
            accountMap.put(account.getMchId(),account);
        }
        for (int i = 0; i < mchAccountConfigs.size(); i++) {
            AppMchAccountConfig config = mchAccountConfigs.get(i);
            String storeId = config.getStoreId();
            String mchId = config.getMchId();
            Byte serviceItemCategory = config.getServiceItemCategory();
            AppMchAccount account = accountMap.get(mchId);

            Map<Byte, AppMchAccount> appMchAccountMap = storeCategoryMchAccountMap.get(storeId);
            if(appMchAccountMap == null){
                appMchAccountMap = new HashMap<>();
                storeCategoryMchAccountMap.put(storeId,appMchAccountMap);
            }
            appMchAccountMap.put(serviceItemCategory,account);
        }
    }

    public  AppMchAccount getMchAccount(String storeId, Byte serviceItemCategory){
        Map<Byte, AppMchAccount> appMchAccountMap = storeCategoryMchAccountMap.get(storeId);
        if(appMchAccountMap==null){
            return null;
        }
        return appMchAccountMap.get(serviceItemCategory);
    }


}
