package com.relle.dto;

public class AdditionalOrderRO {
    private String orderId;
    private String storeId;
    private String serviceItemId;
    private Number serviceItemOriginAmount;
    private Number serviceItemReductionAmount;
    private Number serviceItemAmount;

    private String couponId;

    private String pathOfQrcode;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(String serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public Number getServiceItemOriginAmount() {
        return serviceItemOriginAmount;
    }

    public void setServiceItemOriginAmount(Number serviceItemOriginAmount) {
        this.serviceItemOriginAmount = serviceItemOriginAmount;
    }

    public Number getServiceItemReductionAmount() {
        return serviceItemReductionAmount;
    }

    public void setServiceItemReductionAmount(Number serviceItemReductionAmount) {
        this.serviceItemReductionAmount = serviceItemReductionAmount;
    }

    public Number getServiceItemAmount() {
        return serviceItemAmount;
    }

    public void setServiceItemAmount(Number serviceItemAmount) {
        this.serviceItemAmount = serviceItemAmount;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getPathOfQrcode() {
        return pathOfQrcode;
    }

    public void setPathOfQrcode(String pathOfQrcode) {
        this.pathOfQrcode = pathOfQrcode;
    }
}
