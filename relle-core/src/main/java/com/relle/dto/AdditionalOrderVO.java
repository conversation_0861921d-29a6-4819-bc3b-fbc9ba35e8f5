package com.relle.dto;

import com.relle.mbg.model.*;

public class AdditionalOrderVO {
    private AppServiceSuborderAdditional suborderAdditional;
    private AppServiceItem serviceItem;
    private AppServiceMedia thumbnail;
    private AppStoreInfo store;
    private AppCustomerInfo customerInfo;
    public AppServiceSuborderAdditional getSuborderAdditional() {
        return suborderAdditional;
    }
    private AppServiceOperationRecord operationRecord;

    public void setSuborderAdditional(AppServiceSuborderAdditional suborderAdditional) {
        this.suborderAdditional = suborderAdditional;
    }


    public AppServiceItem getServiceItem() {
        return serviceItem;
    }

    public void setServiceItem(AppServiceItem serviceItem) {
        this.serviceItem = serviceItem;
    }

    public AppServiceMedia getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(AppServiceMedia thumbnail) {
        this.thumbnail = thumbnail;
    }

    public AppStoreInfo getStore() {
        return store;
    }

    public void setStore(AppStoreInfo store) {
        this.store = store;
    }

    public AppCustomerInfo getCustomerInfo() {
        return customerInfo;
    }

    public void setCustomerInfo(AppCustomerInfo customerInfo) {
        this.customerInfo = customerInfo;
    }

    public AppServiceOperationRecord getOperationRecord() {
        return operationRecord;
    }

    public void setOperationRecord(AppServiceOperationRecord operationRecord) {
        this.operationRecord = operationRecord;
    }
}
