package com.relle.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppServiceMedia;

/**
 * App Service Item Detail Data Transfer Object
 */
public class AppServiceItemDetailDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String serviceId;
    private String serviceName;
    private String serviceDescription;
    private String serviceDetails;
    private String serviceContent;
    private BigDecimal servicePrice;
    private Integer serviceDurationSec;
    private Integer servicePreparationSec;
    private Integer serviceClosingSec;
    private String serviceCategory;
    private String serviceTagtree;
    private Integer status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
    private Boolean isDeleted;
    private List<Object> mediaList;
    private List<Object> detailMediaList;
    
    // Constructors
    public AppServiceItemDetailDTO() {}
    
    public AppServiceItemDetailDTO(String serviceId, String serviceName) {
        this.serviceId = serviceId;
        this.serviceName = serviceName;
    }
    
    // Getters and Setters
    public String getServiceId() { return serviceId; }
    public void setServiceId(String serviceId) { this.serviceId = serviceId; }
    
    public String getServiceName() { return serviceName; }
    public void setServiceName(String serviceName) { this.serviceName = serviceName; }
    
    public String getServiceDescription() { return serviceDescription; }
    public void setServiceDescription(String serviceDescription) { this.serviceDescription = serviceDescription; }
    
    public String getServiceDetails() { return serviceDetails; }
    public void setServiceDetails(String serviceDetails) { this.serviceDetails = serviceDetails; }
    
    public String getServiceContent() { return serviceContent; }
    public void setServiceContent(String serviceContent) { this.serviceContent = serviceContent; }
    
    public BigDecimal getServicePrice() { return servicePrice; }
    public void setServicePrice(BigDecimal servicePrice) { this.servicePrice = servicePrice; }
    
    public Integer getServiceDurationSec() { return serviceDurationSec; }
    public void setServiceDurationSec(Integer serviceDurationSec) { this.serviceDurationSec = serviceDurationSec; }
    
    public Integer getServicePreparationSec() { return servicePreparationSec; }
    public void setServicePreparationSec(Integer servicePreparationSec) { this.servicePreparationSec = servicePreparationSec; }
    
    public Integer getServiceClosingSec() { return serviceClosingSec; }
    public void setServiceClosingSec(Integer serviceClosingSec) { this.serviceClosingSec = serviceClosingSec; }
    
    public String getServiceCategory() { return serviceCategory; }
    public void setServiceCategory(String serviceCategory) { this.serviceCategory = serviceCategory; }
    
    public String getServiceTagtree() { return serviceTagtree; }
    public void setServiceTagtree(String serviceTagtree) { this.serviceTagtree = serviceTagtree; }
    
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public String getCreateBy() { return createBy; }
    public void setCreateBy(String createBy) { this.createBy = createBy; }
    
    public String getUpdateBy() { return updateBy; }
    public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
    
    public Boolean getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }
    
    public List<Object> getMediaList() { return mediaList; }
    public void setMediaList(List<Object> mediaList) { this.mediaList = mediaList; }
    
    public List<Object> getDetailMediaList() { return detailMediaList; }
    public void setDetailMediaList(List<Object> detailMediaList) { this.detailMediaList = detailMediaList; }

    public AppServiceItem getAppServiceItem() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAppServiceItem'");
    }

    public List<AppServiceItemDTO> getServiceItemList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getServiceItemList'");
    }

    public AppServiceMedia getThumbnail() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getThumbnail'");
    }

    public List<AppServiceMedia> getAppServiceMediaList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAppServiceMediaList'");
    }

    public List<AppServiceMedia> getAppServiceDetailMediaList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAppServiceDetailMediaList'");
    }
}
