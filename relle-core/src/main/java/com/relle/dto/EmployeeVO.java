package com.relle.dto;

import java.io.Serializable;

/**
 * Employee View Object
 */
public class EmployeeVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String employeeId;
    private String employeeName;
    private String phone;
    private String position;
    private String storeId;
    private String storeName;
    
    // Constructors
    public EmployeeVO() {}
    
    public EmployeeVO(Long id, String employeeId, String employeeName) {
        this.id = id;
        this.employeeId = employeeId;
        this.employeeName = employeeName;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }
    
    public String getEmployeeName() { return employeeName; }
    public void setEmployeeName(String employeeName) { this.employeeName = employeeName; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }
    
    public String getStoreId() { return storeId; }
    public void setStoreId(String storeId) { this.storeId = storeId; }
    
    public String getStoreName() { return storeName; }
    public void setStoreName(String storeName) { this.storeName = storeName; }

    public void setRoleCode(int roleCode) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setRoleCode'");
    }
}
