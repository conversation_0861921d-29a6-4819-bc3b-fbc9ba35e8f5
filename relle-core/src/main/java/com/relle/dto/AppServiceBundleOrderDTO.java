package com.relle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.relle.enums.OrderCategoryEnum;
import com.relle.mbg.model.*;

import java.time.LocalDateTime;
import java.util.List;

public class AppServiceBundleOrderDTO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime systemTime;
    private AppStoreInfo storeInfo;
    private AppServiceBundleOrder order;
    private List<AppServiceBundleSuborderDTO>  suborders;
    private AppOrderRefund refund;
    private AppOrderPay pay;
    private AppCoupon coupon;

    private byte isAdminAdd;
    private int orderCategory= OrderCategoryEnum.COUPON.getCode();

    public byte getIsAdminAdd() {
        return isAdminAdd;
    }

    public void setIsAdminAdd(byte isAdminAdd) {
        this.isAdminAdd = isAdminAdd;
    }

    public AppServiceBundleOrder getOrder() {
        return order;
    }

    public void setOrder(AppServiceBundleOrder order) {
        this.order = order;
    }

    public AppServiceBundleOrderDTO() {
        this.systemTime = LocalDateTime.now();
    }

    public AppStoreInfo getStoreInfo() {
        return storeInfo;
    }

    public void setStoreInfo(AppStoreInfo storeInfo) {
        this.storeInfo = storeInfo;
    }

    public AppOrderRefund getRefund() {
        return refund;
    }

    public void setRefund(AppOrderRefund refund) {
        this.refund = refund;
    }

    public AppOrderPay getPay() {
        return pay;
    }

    public void setPay(AppOrderPay pay) {
        this.pay = pay;
    }

    public LocalDateTime getSystemTime() {
        return systemTime;
    }

    public void setSystemTime(LocalDateTime systemTime) {
        this.systemTime = systemTime;
    }

    public AppCoupon getCoupon() {
        return coupon;
    }

    public void setCoupon(AppCoupon coupon) {
        this.coupon = coupon;
    }

    public List<AppServiceBundleSuborderDTO> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppServiceBundleSuborderDTO> suborders) {
        this.suborders = suborders;
    }

    public int getOrderCategory() {
        return orderCategory;
    }

    public void setOrderCategory(int orderCategory) {
        this.orderCategory = orderCategory;
    }
}
