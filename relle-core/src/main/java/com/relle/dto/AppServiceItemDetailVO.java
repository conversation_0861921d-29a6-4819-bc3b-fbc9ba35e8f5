package com.relle.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * App Service Item Detail View Object
 */
public class AppServiceItemDetailVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String serviceId;
    private String serviceName;
    private String serviceDescription;
    private String serviceDetails;
    private String serviceContent;
    private BigDecimal servicePrice;
    private Integer serviceDurationSec;
    private Integer servicePreparationSec;
    private Integer serviceClosingSec;
    private String serviceCategory;
    private String serviceTagtree;
    private String thumbnail;
    private String serviceImage;
    private Integer status;
    private String statusName;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
    private Integer sortOrder;
    private Boolean isActive;
    private Boolean isDeleted;
    private List<Object> mediaList;
    private List<Object> detailMediaList;
    private Object storeInfo;
    private Object serviceRelation;
    
    // Constructors
    public AppServiceItemDetailVO() {}
    
    public AppServiceItemDetailVO(String serviceId, String serviceName) {
        this.serviceId = serviceId;
        this.serviceName = serviceName;
    }
    
    // Getters and Setters
    public String getServiceId() { return serviceId; }
    public void setServiceId(String serviceId) { this.serviceId = serviceId; }
    
    public String getServiceName() { return serviceName; }
    public void setServiceName(String serviceName) { this.serviceName = serviceName; }
    
    public String getServiceDescription() { return serviceDescription; }
    public void setServiceDescription(String serviceDescription) { this.serviceDescription = serviceDescription; }
    
    public String getServiceDetails() { return serviceDetails; }
    public void setServiceDetails(String serviceDetails) { this.serviceDetails = serviceDetails; }
    
    public String getServiceContent() { return serviceContent; }
    public void setServiceContent(String serviceContent) { this.serviceContent = serviceContent; }
    
    public BigDecimal getServicePrice() { return servicePrice; }
    public void setServicePrice(BigDecimal servicePrice) { this.servicePrice = servicePrice; }
    
    public Integer getServiceDurationSec() { return serviceDurationSec; }
    public void setServiceDurationSec(Integer serviceDurationSec) { this.serviceDurationSec = serviceDurationSec; }
    
    public Integer getServicePreparationSec() { return servicePreparationSec; }
    public void setServicePreparationSec(Integer servicePreparationSec) { this.servicePreparationSec = servicePreparationSec; }
    
    public Integer getServiceClosingSec() { return serviceClosingSec; }
    public void setServiceClosingSec(Integer serviceClosingSec) { this.serviceClosingSec = serviceClosingSec; }
    
    public String getServiceCategory() { return serviceCategory; }
    public void setServiceCategory(String serviceCategory) { this.serviceCategory = serviceCategory; }
    
    public String getServiceTagtree() { return serviceTagtree; }
    public void setServiceTagtree(String serviceTagtree) { this.serviceTagtree = serviceTagtree; }
    
    public String getThumbnail() { return thumbnail; }
    public void setThumbnail(String thumbnail) { this.thumbnail = thumbnail; }
    
    public String getServiceImage() { return serviceImage; }
    public void setServiceImage(String serviceImage) { this.serviceImage = serviceImage; }
    
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    
    public String getStatusName() { return statusName; }
    public void setStatusName(String statusName) { this.statusName = statusName; }
    
    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    
    public LocalDateTime getUpdateTime() { return updateTime; }
    public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
    
    public String getCreateBy() { return createBy; }
    public void setCreateBy(String createBy) { this.createBy = createBy; }
    
    public String getUpdateBy() { return updateBy; }
    public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
    
    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }
    
    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }
    
    public Boolean getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }
    
    public List<Object> getMediaList() { return mediaList; }
    public void setMediaList(List<Object> mediaList) { this.mediaList = mediaList; }
    
    public List<Object> getDetailMediaList() { return detailMediaList; }
    public void setDetailMediaList(List<Object> detailMediaList) { this.detailMediaList = detailMediaList; }
    
    public Object getStoreInfo() { return storeInfo; }
    public void setStoreInfo(Object storeInfo) { this.storeInfo = storeInfo; }
    
    public Object getServiceRelation() { return serviceRelation; }
    public void setServiceRelation(Object serviceRelation) { this.serviceRelation = serviceRelation; }
}
