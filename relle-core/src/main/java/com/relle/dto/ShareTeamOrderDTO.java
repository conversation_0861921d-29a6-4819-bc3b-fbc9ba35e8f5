package com.relle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.relle.mbg.model.AppServiceTeamOrder;

import java.time.LocalDateTime;
import java.util.List;

public class ShareTeamOrderDTO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime endTime;
    private String teamNo;
    private int teamNumbers;
    private List<AppServiceTeamOrder> orders;
    private byte status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime now;

    private int isCreator = 0;
    private String serviceItemId;

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public int getTeamNumbers() {
        return teamNumbers;
    }

    public void setTeamNumbers(int teamNumbers) {
        this.teamNumbers = teamNumbers;
    }

    public String getTeamNo() {
        return teamNo;
    }

    public void setTeamNo(String teamNo) {
        this.teamNo = teamNo;
    }

    public List<AppServiceTeamOrder> getOrders() {
        return orders;
    }

    public void setOrders(List<AppServiceTeamOrder> orders) {
        this.orders = orders;
    }

    public byte getStatus() {
        return status;
    }

    public void setStatus(byte status) {
        this.status = status;
    }

    public LocalDateTime getNow() {
        return now;
    }

    public void setNow(LocalDateTime now) {
        this.now = now;
    }

    public String getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(String serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public int getIsCreator() {
        return isCreator;
    }

    public void setIsCreator(int isCreator) {
        this.isCreator = isCreator;
    }
}
