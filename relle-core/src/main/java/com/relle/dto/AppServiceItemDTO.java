package com.relle.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppStoreServiceRelation;
import com.relle.mbg.model.AppStoreInfo;

/**
 * App Service Item Data Transfer Object
 * Used for transferring service item data between layers
 */
public class AppServiceItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String serviceId;
    private String serviceName;
    private String serviceDescription;
    private BigDecimal servicePrice;
    private Integer serviceDurationSec;
    private Integer servicePreparationSec;
    private Integer serviceClosingSec;
    private String serviceCategory;
    private Integer categoryId;
    private String categoryName;
    private String thumbnail;
    private String serviceImage;
    private Integer status;
    private String statusName;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
    private String remark;
    private Integer sortOrder;
    private Boolean isActive;
    private Boolean isDeleted;

    // Additional fields for complex DTO operations
    private AppStoreInfo appStoreInfo;
    private AppStoreServiceRelation appStoreServiceRelation;
    private AppServiceItem appServiceItem;
    private List<Object> appServiceMediaList;
    private List<Object> appServiceDetailMediaList;
    private String restricted;

    // Default constructor
    public AppServiceItemDTO() {}

    // Getters and Setters
    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceDescription() {
        return serviceDescription;
    }

    public void setServiceDescription(String serviceDescription) {
        this.serviceDescription = serviceDescription;
    }

    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    public Integer getServiceDurationSec() {
        return serviceDurationSec;
    }

    public void setServiceDurationSec(Integer serviceDurationSec) {
        this.serviceDurationSec = serviceDurationSec;
    }

    public Integer getServicePreparationSec() {
        return servicePreparationSec;
    }

    public void setServicePreparationSec(Integer servicePreparationSec) {
        this.servicePreparationSec = servicePreparationSec;
    }

    public Integer getServiceClosingSec() {
        return serviceClosingSec;
    }

    public void setServiceClosingSec(Integer serviceClosingSec) {
        this.serviceClosingSec = serviceClosingSec;
    }

    public String getServiceCategory() {
        return serviceCategory;
    }

    public void setServiceCategory(String serviceCategory) {
        this.serviceCategory = serviceCategory;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    // Overloaded method to handle AppServiceMedia objects
    public void setThumbnail(Object thumbnailMedia) {
        if (thumbnailMedia != null) {
            this.thumbnail = thumbnailMedia.toString();
        }
    }

    public String getServiceImage() {
        return serviceImage;
    }

    public void setServiceImage(String serviceImage) {
        this.serviceImage = serviceImage;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // Additional getters and setters for complex DTO operations
    public AppStoreInfo getAppStoreInfo() {
        return appStoreInfo;
    }

    public void setAppStoreInfo(AppStoreInfo appStoreInfo) {
        this.appStoreInfo = appStoreInfo;
    }

    public AppStoreServiceRelation getAppStoreServiceRelation() {
        return appStoreServiceRelation;
    }

    public void setAppStoreServiceRelation(AppStoreServiceRelation appStoreServiceRelation) {
        this.appStoreServiceRelation = appStoreServiceRelation;
    }

    public AppServiceItem getAppServiceItem() {
        return appServiceItem;
    }

    public void setAppServiceItem(AppServiceItem appServiceItem) {
        this.appServiceItem = appServiceItem;
    }

    public List<Object> getAppServiceMediaList() {
        return appServiceMediaList;
    }

    public void setAppServiceMediaList(List<?> appServiceMediaList) {
        this.appServiceMediaList = (List<Object>) appServiceMediaList;
    }

    public List<Object> getAppServiceDetailMediaList() {
        return appServiceDetailMediaList;
    }

    public void setAppServiceDetailMediaList(List<?> appServiceDetailMediaList) {
        this.appServiceDetailMediaList = (List<Object>) appServiceDetailMediaList;
    }

    public String getRestricted() {
        return restricted;
    }

    public void setRestricted(String restricted) {
        this.restricted = restricted;
    }

    @Override
    public String toString() {
        return "AppServiceItemDTO{" +
                "serviceId='" + serviceId + '\'' +
                ", serviceName='" + serviceName + '\'' +
                ", serviceDescription='" + serviceDescription + '\'' +
                ", servicePrice=" + servicePrice +
                ", serviceDurationSec=" + serviceDurationSec +
                ", servicePreparationSec=" + servicePreparationSec +
                ", serviceClosingSec=" + serviceClosingSec +
                ", serviceCategory='" + serviceCategory + '\'' +
                ", categoryId=" + categoryId +
                ", categoryName='" + categoryName + '\'' +
                ", thumbnail='" + thumbnail + '\'' +
                ", serviceImage='" + serviceImage + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", remark='" + remark + '\'' +
                ", sortOrder=" + sortOrder +
                ", isActive=" + isActive +
                ", isDeleted=" + isDeleted +
                '}';
    }

    public Object getNum() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getNum'");
    }
}
