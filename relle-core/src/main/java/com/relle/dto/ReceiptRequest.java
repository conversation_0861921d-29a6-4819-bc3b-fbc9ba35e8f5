package com.relle.dto;

import java.io.Serializable;

/**
 * Receipt Request Data Transfer Object
 * Used for Meituan API receipt processing
 */
public class ReceiptRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private String receiptCode;
    private String storeId;
    private String dealId;
    private String couponCode;
    private String unionid;
    private String openid;
    private String sessionId;
    private String timestamp;
    private String signature;

    // Default constructor
    public ReceiptRequest() {}

    // Getters and Setters
    public String getReceiptCode() {
        return receiptCode;
    }

    public void setReceiptCode(String receiptCode) {
        this.receiptCode = receiptCode;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getDealId() {
        return dealId;
    }

    public void setDealId(String dealId) {
        this.dealId = dealId;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    // Additional methods needed by controllers
    public String getCode() {
        return this.receiptCode;
    }

    public void setCode(String code) {
        this.receiptCode = code;
    }

    @Override
    public String toString() {
        return "ReceiptRequest{" +
                "receiptCode='" + receiptCode + '\'' +
                ", storeId='" + storeId + '\'' +
                ", dealId='" + dealId + '\'' +
                ", couponCode='" + couponCode + '\'' +
                ", unionid='" + unionid + '\'' +
                ", openid='" + openid + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", signature='" + signature + '\'' +
                '}';
    }
}
