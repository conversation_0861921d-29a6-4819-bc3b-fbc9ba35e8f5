package com.relle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

public class AppCouponDTO {
    private Long id;
    private String couponId;
    private Byte couponCategory;
    private String couponType;
    private String couponName;
    private String couponCoverImg;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date receiveTime;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date useCouponTime;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date couponValidDatetimeBegin;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date couponValidDatetimeEnd;
    private BigDecimal couponMinCharge;
    private BigDecimal couponValueAmount;
    private int couponNum = 1;
    private byte couponStatus;
    private Integer couponSource;
    private String serviceItemId;
    private String storeIds;

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getCouponCoverImg() {
        return couponCoverImg;
    }

    public void setCouponCoverImg(String couponCoverImg) {
        this.couponCoverImg = couponCoverImg;
    }

    public Date getCouponValidDatetimeBegin() {
        return couponValidDatetimeBegin;
    }

    public void setCouponValidDatetimeBegin(Date couponValidDatetimeBegin) {
        this.couponValidDatetimeBegin = couponValidDatetimeBegin;
    }

    public Date getCouponValidDatetimeEnd() {
        return couponValidDatetimeEnd;
    }

    public void setCouponValidDatetimeEnd(Date couponValidDatetimeEnd) {
        this.couponValidDatetimeEnd = couponValidDatetimeEnd;
    }

    public BigDecimal getCouponMinCharge() {
        return couponMinCharge;
    }

    public void setCouponMinCharge(BigDecimal couponMinCharge) {
        this.couponMinCharge = couponMinCharge;
    }

    public BigDecimal getCouponValueAmount() {
        return couponValueAmount;
    }

    public void setCouponValueAmount(BigDecimal couponValueAmount) {
        this.couponValueAmount = couponValueAmount;
    }

    public byte getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(byte couponStatus) {
        this.couponStatus = couponStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public int getCouponNum() {
        return couponNum;
    }

    public void setCouponNum(int couponNum) {
        this.couponNum = couponNum;
    }

    public Integer getCouponSource() {
        return couponSource;
    }

    public void setCouponSource(Integer couponSource) {
        this.couponSource = couponSource;
    }

    public String getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(String serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public String getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(String storeIds) {
        this.storeIds = storeIds;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getUseCouponTime() {
        return useCouponTime;
    }

    public void setUseCouponTime(Date useCouponTime) {
        this.useCouponTime = useCouponTime;
    }

    public Byte getCouponCategory() {
        return couponCategory;
    }

    public void setCouponCategory(Byte couponCategory) {
        this.couponCategory = couponCategory;
    }
}
