package com.relle.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.relle.mbg.model.AppStoreInfo;

/**
 * Employee Detail Data Transfer Object
 */
public class EmployeeDetailDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String employeeId;
    private String employeeName;
    private String phone;
    private String email;
    private String position;
    private String department;
    private String storeId;
    private Date createTime;
    private Date updateTime;
    
    // Constructors
    public EmployeeDetailDTO() {}
    
    public EmployeeDetailDTO(Long id, String employeeId, String employeeName) {
        this.id = id;
        this.employeeId = employeeId;
        this.employeeName = employeeName;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getEmployeeId() { return employeeId; }
    public void setEmployeeId(String employeeId) { this.employeeId = employeeId; }
    
    public String getEmployeeName() { return employeeName; }
    public void setEmployeeName(String employeeName) { this.employeeName = employeeName; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPosition() { return position; }
    public void setPosition(String position) { this.position = position; }
    
    public String getDepartment() { return department; }
    public void setDepartment(String department) { this.department = department; }
    
    public String getStoreId() { return storeId; }
    public void setStoreId(String storeId) { this.storeId = storeId; }
    
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

    public String getUnionid() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getUnionid'");
    }

    public List<AppStoreInfo> getStoreList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getStoreList'");
    }

    public Integer getRoleCode() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getRoleCode'");
    }

    public String getEmployeePhone() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getEmployeePhone'");
    }
}
