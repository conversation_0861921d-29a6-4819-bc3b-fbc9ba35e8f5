package com.relle.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;


public class AppCouponShareDTO {
    private Long shareId;
    private String shareCustomerId;
    private String shareCustomerUnionid;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime shareTime;
    private Long couponNo;
    private String couponId;
    private String couponType;
    private String couponName;
    private String couponCoverImg;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime couponValidDatetimeBegin;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime couponValidDatetimeEnd;

    private int couponNum = 1;
    private byte couponStatus;

    private String serviceItemId;
    private String serviceItemName;

    public Long getShareId() {
        return shareId;
    }

    public void setShareId(Long shareId) {
        this.shareId = shareId;
    }

    public String getShareCustomerId() {
        return shareCustomerId;
    }

    public void setShareCustomerId(String shareCustomerId) {
        this.shareCustomerId = shareCustomerId;
    }

    public String getShareCustomerUnionid() {
        return shareCustomerUnionid;
    }

    public void setShareCustomerUnionid(String shareCustomerUnionid) {
        this.shareCustomerUnionid = shareCustomerUnionid;
    }

    public LocalDateTime getShareTime() {
        return shareTime;
    }

    public void setShareTime(LocalDateTime shareTime) {
        this.shareTime = shareTime;
    }

    public Long getCouponNo() {
        return couponNo;
    }

    public void setCouponNo(Long couponNo) {
        this.couponNo = couponNo;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public String getCouponCoverImg() {
        return couponCoverImg;
    }

    public void setCouponCoverImg(String couponCoverImg) {
        this.couponCoverImg = couponCoverImg;
    }

    public LocalDateTime getCouponValidDatetimeBegin() {
        return couponValidDatetimeBegin;
    }

    public void setCouponValidDatetimeBegin(LocalDateTime couponValidDatetimeBegin) {
        this.couponValidDatetimeBegin = couponValidDatetimeBegin;
    }

    public LocalDateTime getCouponValidDatetimeEnd() {
        return couponValidDatetimeEnd;
    }

    public void setCouponValidDatetimeEnd(LocalDateTime couponValidDatetimeEnd) {
        this.couponValidDatetimeEnd = couponValidDatetimeEnd;
    }

    public int getCouponNum() {
        return couponNum;
    }

    public void setCouponNum(int couponNum) {
        this.couponNum = couponNum;
    }

    public byte getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(byte couponStatus) {
        this.couponStatus = couponStatus;
    }

    public String getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(String serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }
}
