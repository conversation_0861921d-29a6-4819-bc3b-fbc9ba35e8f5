package com.relle.dto;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * Order Query Data Transfer Object
 */
public class OrderQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String orderId;
    private String customerId;
    private String storeId;
    private String status;
    private LocalDate startDate;
    private LocalDate endDate;
    private String serviceType;
    private Integer pageNum;
    private Integer pageSize;
    
    // Constructors
    public OrderQueryDTO() {}
    
    public OrderQueryDTO(String orderId, String customerId) {
        this.orderId = orderId;
        this.customerId = customerId;
    }
    
    // Getters and Setters
    public String getOrderId() { return orderId; }
    public void setOrderId(String orderId) { this.orderId = orderId; }
    
    public String getCustomerId() { return customerId; }
    public void setCustomerId(String customerId) { this.customerId = customerId; }
    
    public String getStoreId() { return storeId; }
    public void setStoreId(String storeId) { this.storeId = storeId; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
    
    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
    
    public String getServiceType() { return serviceType; }
    public void setServiceType(String serviceType) { this.serviceType = serviceType; }
    
    public Integer getPageNum() { return pageNum; }
    public void setPageNum(Integer pageNum) { this.pageNum = pageNum; }
    
    public Integer getPageSize() { return pageSize; }
    public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
}
