package com.relle.dto;

import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppServiceSuborder;

import java.util.List;

public class AppServiceSuborderDTO {
    private AppServiceSuborder suborder;
    private AppServiceItemDTO serviceItem;
    private List<AdditionalOrderVO> additionalOrders;

    public AppServiceSuborder getSuborder() {
        return suborder;
    }

    public void setSuborder(AppServiceSuborder suborder) {
        this.suborder = suborder;
    }

    public AppServiceItemDTO getServiceItem() {
        return serviceItem;
    }

    public void setServiceItem(AppServiceItemDTO serviceItem) {
        this.serviceItem = serviceItem;
    }

    public List<AdditionalOrderVO> getAdditionalOrders() {
        return additionalOrders;
    }

    public void setAdditionalOrders(List<AdditionalOrderVO> additionalOrders) {
        this.additionalOrders = additionalOrders;
    }
}
