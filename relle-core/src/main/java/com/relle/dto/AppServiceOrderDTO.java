package com.relle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.relle.enums.OrderCategoryEnum;
import com.relle.mbg.model.*;

import java.time.LocalDateTime;
import java.util.List;

public class AppServiceOrderDTO {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime systemTime;
    private AppStoreInfo storeInfo;
    private AppServiceOrder order;
    private List<AppServiceSuborderDTO>  suborders;
    private AppOrderRefund refund;
    private AppOrderPay pay;
    private AppCoupon coupon;

    private int orderCategory= OrderCategoryEnum.SERVICE.getCode();

    private byte isAdminAdd;
    private int additionals;
    private int additionalUnPays;

    public byte getIsAdminAdd() {
        return isAdminAdd;
    }

    public void setIsAdminAdd(byte isAdminAdd) {
        this.isAdminAdd = isAdminAdd;
    }

    public AppServiceOrderDTO() {
        this.systemTime = LocalDateTime.now();
    }

    public AppServiceOrder getOrder() {
        return order;
    }

    public void setOrder(AppServiceOrder order) {
        this.order = order;
    }

    public AppStoreInfo getStoreInfo() {
        return storeInfo;
    }

    public void setStoreInfo(AppStoreInfo storeInfo) {
        this.storeInfo = storeInfo;
    }

    public List<AppServiceSuborderDTO> getSuborders() {
        return suborders;
    }

    public void setSuborders(List<AppServiceSuborderDTO> suborders) {
        this.suborders = suborders;
    }

    public AppOrderRefund getRefund() {
        return refund;
    }

    public void setRefund(AppOrderRefund refund) {
        this.refund = refund;
    }

    public AppOrderPay getPay() {
        return pay;
    }

    public void setPay(AppOrderPay pay) {
        this.pay = pay;
    }

    public LocalDateTime getSystemTime() {
        return systemTime;
    }

    public void setSystemTime(LocalDateTime systemTime) {
        this.systemTime = systemTime;
    }

    public AppCoupon getCoupon() {
        return coupon;
    }

    public void setCoupon(AppCoupon coupon) {
        this.coupon = coupon;
    }

    public int getAdditionals() {
        return additionals;
    }

    public void setAdditionals(int additionals) {
        this.additionals = additionals;
    }

    public int getAdditionalUnPays() {
        return additionalUnPays;
    }

    public void setAdditionalUnPays(int additionalUnPays) {
        this.additionalUnPays = additionalUnPays;
    }

    public int getOrderCategory() {
        return orderCategory;
    }

    public void setOrderCategory(int orderCategory) {
        this.orderCategory = orderCategory;
    }


}
