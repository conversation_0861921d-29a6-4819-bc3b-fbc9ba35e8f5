package com.relle.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Skin Detector Test View Object
 */
public class SkinDetectorTestVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String testId;
    private String customerId;
    private String customerName;
    private String testType;
    private String testResult;
    private String skinType;
    private String skinCondition;
    private String recommendations;
    private LocalDateTime testTime;
    private String testOperator;
    private String storeId;
    private String storeName;
    private String deviceId;
    private String deviceModel;
    private String testImages;
    private String analysisReport;
    private String status;
    
    // Constructors
    public SkinDetectorTestVO() {}
    
    public SkinDetectorTestVO(String testId, String customerId) {
        this.testId = testId;
        this.customerId = customerId;
    }
    
    // Getters and Setters
    public String getTestId() { return testId; }
    public void setTestId(String testId) { this.testId = testId; }
    
    public String getCustomerId() { return customerId; }
    public void setCustomerId(String customerId) { this.customerId = customerId; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public String getTestType() { return testType; }
    public void setTestType(String testType) { this.testType = testType; }
    
    public String getTestResult() { return testResult; }
    public void setTestResult(String testResult) { this.testResult = testResult; }
    
    public String getSkinType() { return skinType; }
    public void setSkinType(String skinType) { this.skinType = skinType; }
    
    public String getSkinCondition() { return skinCondition; }
    public void setSkinCondition(String skinCondition) { this.skinCondition = skinCondition; }
    
    public String getRecommendations() { return recommendations; }
    public void setRecommendations(String recommendations) { this.recommendations = recommendations; }
    
    public LocalDateTime getTestTime() { return testTime; }
    public void setTestTime(LocalDateTime testTime) { this.testTime = testTime; }
    
    public String getTestOperator() { return testOperator; }
    public void setTestOperator(String testOperator) { this.testOperator = testOperator; }
    
    public String getStoreId() { return storeId; }
    public void setStoreId(String storeId) { this.storeId = storeId; }
    
    public String getStoreName() { return storeName; }
    public void setStoreName(String storeName) { this.storeName = storeName; }
    
    public String getDeviceId() { return deviceId; }
    public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    
    public String getDeviceModel() { return deviceModel; }
    public void setDeviceModel(String deviceModel) { this.deviceModel = deviceModel; }
    
    public String getTestImages() { return testImages; }
    public void setTestImages(String testImages) { this.testImages = testImages; }
    
    public String getAnalysisReport() { return analysisReport; }
    public void setAnalysisReport(String analysisReport) { this.analysisReport = analysisReport; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}
