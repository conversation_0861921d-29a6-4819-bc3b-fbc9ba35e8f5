package com.relle.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.relle.mbg.model.AppCustomerInfo;
import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppServiceMedia;
import com.relle.mbg.model.AppServiceOperationRecord;
import com.relle.mbg.model.AppServiceSuborderAdditional;

/**
 * Additional Suborder Data Transfer Object
 * Used for transferring additional suborder data between layers
 */
public class AdditionalSuborderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;
    private Integer orderId;
    private String orderNumber;
    private Integer suborderNumber;
    private Integer serviceItemId;
    private String serviceItemName;
    private BigDecimal amount;
    private BigDecimal discountAmount;
    private BigDecimal finalAmount;
    private Integer quantity;
    private Integer status;
    private String statusName;
    private LocalDateTime bookTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String remark;
    private Integer employeeId;
    private String employeeName;
    private Integer customerId;
    private String customerName;
    private String customerPhone;

    // Default constructor
    public AdditionalSuborderDTO() {}

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getSuborderNumber() {
        return suborderNumber;
    }

    public void setSuborderNumber(Integer suborderNumber) {
        this.suborderNumber = suborderNumber;
    }

    public Integer getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(Integer serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getFinalAmount() {
        return finalAmount;
    }

    public void setFinalAmount(BigDecimal finalAmount) {
        this.finalAmount = finalAmount;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public LocalDateTime getBookTime() {
        return bookTime;
    }

    public void setBookTime(LocalDateTime bookTime) {
        this.bookTime = bookTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    @Override
    public String toString() {
        return "AdditionalSuborderDTO{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", orderNumber='" + orderNumber + '\'' +
                ", suborderNumber=" + suborderNumber +
                ", serviceItemId=" + serviceItemId +
                ", serviceItemName='" + serviceItemName + '\'' +
                ", amount=" + amount +
                ", discountAmount=" + discountAmount +
                ", finalAmount=" + finalAmount +
                ", quantity=" + quantity +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", bookTime=" + bookTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", remark='" + remark + '\'' +
                ", employeeId=" + employeeId +
                ", employeeName='" + employeeName + '\'' +
                ", customerId=" + customerId +
                ", customerName='" + customerName + '\'' +
                ", customerPhone='" + customerPhone + '\'' +
                '}';
    }

    public void setAdditionalSuborder(AppServiceSuborderAdditional suborderAdditional) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setAdditionalSuborder'");
    }

    public void setServiceItem(AppServiceItem item) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setServiceItem'");
    }

    public void setThumbnail(AppServiceMedia oneServiceMediaRelation) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setThumbnail'");
    }

    public void setCustomerInfo(AppCustomerInfo customerInfo) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setCustomerInfo'");
    }

    public void setOperationRecord(AppServiceOperationRecord additionalRecord) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'setOperationRecord'");
    }
}
