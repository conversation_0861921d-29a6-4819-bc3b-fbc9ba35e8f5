package com.relle.component;

import com.relle.dto.AdditionalSuborderDTO;
import com.relle.dto.OrderDTO;
import com.relle.enums.ServiceItemMediaTypeEnum;
import com.relle.mbg.dao.AppServiceItemDao;
import com.relle.mbg.dao.AppServiceMediaDao;
import com.relle.mbg.dao.AppServiceSuborderAdditionalDao;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.mbg.model.AppServiceOperationRecord;
import com.relle.mbg.model.AppServiceSuborderAdditional;
import com.relle.service.IAppCustomerInfoService;
import com.relle.service.IAppServiceOperationRecordService;
import com.relle.service.IAppServiceOrderService;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component("ListAllOrderByDateCmp")
public class ListAllOrderByDateCmp extends NodeComponent {
    @Resource
    private IAppServiceOrderService iAppServiceOrderService;
    @Resource
    private AppServiceSuborderAdditionalDao appServiceSuborderAdditionalDao;
    @Resource
    private AppServiceItemDao appServiceItemDao;
    @Resource
    private AppServiceMediaDao appServiceMediaDao;
    @Resource
    private IAppServiceOperationRecordService iAppServiceOperationRecordService;
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;
    
    @Override
    public void process() throws Exception {

        Map<String,Object> paramMap = this.getRequestData();
        List<OrderDTO> context = this.getFirstContextBean();

        String storeId = (String)paramMap.get("storeId");
        LocalDate date = (LocalDate)paramMap.get("date");
        String openid = (String)paramMap.get("openid");

        List<OrderDTO> orderDTOS = iAppServiceOrderService.listAllByDate(storeId, date, openid);

        for (int i = 0; i < orderDTOS.size(); i++) {
            OrderDTO orderDTO = orderDTOS.get(i);
            List<AppServiceSuborderAdditional> additionals = appServiceSuborderAdditionalDao.getListByOrderId(orderDTO.getSubOrderId());
            List<AdditionalSuborderDTO> dtos = new ArrayList<>();
            for (AppServiceSuborderAdditional additional : additionals ) {
                AppCustomerInfo info = new AppCustomerInfo();
                info.setWechatNickname(orderDTO.getCustomerName());
                info.setWechatPhone(orderDTO.getCustomerPhone());
                AdditionalSuborderDTO dto1 = new AdditionalSuborderDTO();
                dto1.setAdditionalSuborder(additional);
                dto1.setServiceItem(appServiceItemDao.getByServiceItemId(additional.getServiceItemId()));
                dto1.setThumbnail(appServiceMediaDao.getOneServiceMediaRelation(additional.getServiceItemId(), ServiceItemMediaTypeEnum.THUMBNAIL.getCode()));
                dto1.setCustomerInfo(info);
                AppServiceOperationRecord additionalRecord = iAppServiceOperationRecordService.getOperationRecord(additional.getSuborderId());
                dto1.setOperationRecord(additionalRecord);
                dtos.add(dto1);
            }
            System.out.println("dtos.size():"+dtos.size());
            orderDTO.setAdditionals(dtos);

        }
        context.addAll(orderDTOS);

    }
}
