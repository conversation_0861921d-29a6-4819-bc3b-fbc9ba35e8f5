package com.relle.component;

import com.relle.dto.OrderDTO;
import com.relle.mbg.model.AppEmployee;
import com.relle.mbg.model.AppServiceOperationRecord;
import com.relle.service.IAppEmployeeService;
import com.relle.service.IAppServiceOperationRecordService;
import com.relle.service.impl.OperationOrderStatusEnum;
import com.relle.service.impl.OperationStatusEnum;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component("CheckOperationCmp")
public class CheckOperationCmp extends NodeComponent {
    @Resource
    private IAppServiceOperationRecordService iAppServiceOperationRecordService;
    @Resource
    private IAppEmployeeService iAppEmployeeService;
    @Override
    public void process() throws Exception {

        Map<String,Object> paramMap = this.getRequestData();
        List<OrderDTO> context = this.getFirstContextBean();

        String storeId = (String)paramMap.get("storeId");
        Date date = (Date)paramMap.get("date");
        String openid = (String)paramMap.get("openid");
        Map<String, AppEmployee> allEmployeeMap = iAppEmployeeService.getAllMap();
        for (OrderDTO order : context ) {
            AppServiceOperationRecord record = iAppServiceOperationRecordService.getOperationRecord(order.getSubOrderId());
            order.setOperationRecord(record);
            if(record!=null){
                AppEmployee appEmployee = allEmployeeMap.get(record.getServiceOperatorId());
                if(appEmployee == null) {
                    order.setOperatorId(record.getServiceOperatorId());
                } else {
                    order.setOperatorId(appEmployee.getEmployeeName());
                }
            }
            if(order.getServiceStatus()==OperationOrderStatusEnum.UNTREATED.getCode()) {
                int status = OperationOrderStatusEnum.WAIT_SERVICE.getCode();
                if (record != null) {
                    //是否服务结束
                    if (record.getServiceOperationStatus() == OperationStatusEnum.FINISHED.getCode()) {
                        status = -1;
                    } else {
                        status = OperationOrderStatusEnum.SERVERING.getCode();
                    }
                }
                order.setServiceStatus(status);
            }
        }

    }
}
