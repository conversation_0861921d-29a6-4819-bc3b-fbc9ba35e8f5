package com.relle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.relle.mbg.model.AppServiceItem;
import com.relle.mbg.model.AppStoreInfo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class BundleOrderVO {
    private AppStoreInfo storeInfo;
    private AppServiceItem serviceItem;

    private String bundleOrderId;
    private BigDecimal suborderOriginPrice;
    private BigDecimal suborderReductionAmount;
    private BigDecimal suborderAmount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    private Short orderStatus;

    public AppStoreInfo getStoreInfo() {
        return storeInfo;
    }

    public void setStoreInfo(AppStoreInfo storeInfo) {
        this.storeInfo = storeInfo;
    }

    public AppServiceItem getServiceItem() {
        return serviceItem;
    }

    public void setServiceItem(AppServiceItem serviceItem) {
        this.serviceItem = serviceItem;
    }

    public BigDecimal getSuborderOriginPrice() {
        return suborderOriginPrice;
    }

    public void setSuborderOriginPrice(BigDecimal suborderOriginPrice) {
        this.suborderOriginPrice = suborderOriginPrice;
    }

    public BigDecimal getSuborderReductionAmount() {
        return suborderReductionAmount;
    }

    public void setSuborderReductionAmount(BigDecimal suborderReductionAmount) {
        this.suborderReductionAmount = suborderReductionAmount;
    }

    public BigDecimal getSuborderAmount() {
        return suborderAmount;
    }

    public void setSuborderAmount(BigDecimal suborderAmount) {
        this.suborderAmount = suborderAmount;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Short getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Short orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getBundleOrderId() {
        return bundleOrderId;
    }

    public void setBundleOrderId(String bundleOrderId) {
        this.bundleOrderId = bundleOrderId;
    }
}
