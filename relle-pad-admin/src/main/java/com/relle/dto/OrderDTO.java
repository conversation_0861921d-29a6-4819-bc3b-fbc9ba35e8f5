package com.relle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.relle.mbg.model.AppCustomerTestLog;
import com.relle.mbg.model.AppServiceOperationFeedback;
import com.relle.mbg.model.AppServiceOperationRecord;
import com.relle.mbg.model.AppStoreInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public class OrderDTO {
    private String orderId;
    private String subOrderId;

    private String serviceItemId;
    private String serviceItemName;
    private int serviceTimeLength;
    private String customerId;
    private String customerUnionid;
    private String customerName;
    private String customerPhone;
    private int customerAge;
    private byte customerGender;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private LocalDateTime bookTime;
    private int serviceStatus;
    private String customerSource;
    private String sourceOrderId;
    private AppServiceOperationRecord operationRecord;
    private AppServiceOperationFeedback operationFeedback;
    private byte writeOff;

    private String createUser;
    private byte isAdminAdd;
    private String roomId;

    private BigDecimal subOrderAmount;
    private String operatorId;
    private List<AdditionalSuborderDTO> additionals;

    private AppStoreInfo storeInfo;

    private AppCustomerTestLog testLog;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public int getCustomerAge() {
        return customerAge;
    }

    public void setCustomerAge(int customerAge) {
        this.customerAge = customerAge;
    }

    public LocalDateTime getBookTime() {
        return bookTime;
    }

    public void setBookTime(LocalDateTime bookTime) {
        this.bookTime = bookTime;
    }

    public int getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(int serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public byte getCustomerGender() {
        return customerGender;
    }

    public void setCustomerGender(byte customerGender) {
        this.customerGender = customerGender;
    }

    public String getCustomerUnionid() {
        return customerUnionid;
    }

    public void setCustomerUnionid(String customerUnionid) {
        this.customerUnionid = customerUnionid;
    }

    public int getServiceTimeLength() {
        return serviceTimeLength;
    }

    public void setServiceTimeLength(int serviceTimeLength) {
        this.serviceTimeLength = serviceTimeLength;

    }

    public AppServiceOperationRecord getOperationRecord() {
        return operationRecord;
    }

    public void setOperationRecord(AppServiceOperationRecord operationRecord) {
        this.operationRecord = operationRecord;
    }

    public AppServiceOperationFeedback getOperationFeedback() {
        return operationFeedback;
    }

    public void setOperationFeedback(AppServiceOperationFeedback operationFeedback) {
        this.operationFeedback = operationFeedback;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerSource() {
        return customerSource;
    }

    public void setCustomerSource(String customerSource) {
        this.customerSource = customerSource;
    }

    public String getSourceOrderId() {
        return sourceOrderId;
    }

    public void setSourceOrderId(String sourceOrderId) {
        this.sourceOrderId = sourceOrderId;
    }

    public String getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(String subOrderId) {
        this.subOrderId = subOrderId;
    }

    public String getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(String serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public byte getWriteOff() {
        return writeOff;
    }

    public void setWriteOff(byte writeOff) {
        this.writeOff = writeOff;
    }

    public byte getIsAdminAdd() {
        return isAdminAdd;
    }

    public void setIsAdminAdd(byte isAdminAdd) {
        this.isAdminAdd = isAdminAdd;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public BigDecimal getSubOrderAmount() {
        return subOrderAmount;
    }

    public void setSubOrderAmount(BigDecimal subOrderAmount) {
        this.subOrderAmount = subOrderAmount;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public List<AdditionalSuborderDTO> getAdditionals() {
        return additionals;
    }

    public void setAdditionals(List<AdditionalSuborderDTO> additionals) {
        this.additionals = additionals;
    }

    public AppStoreInfo getStoreInfo() {
        return storeInfo;
    }

    public void setStoreInfo(AppStoreInfo storeInfo) {
        this.storeInfo = storeInfo;
    }

    public AppCustomerTestLog getTestLog() {
        return testLog;
    }

    public void setTestLog(AppCustomerTestLog testLog) {
        this.testLog = testLog;
    }
}
