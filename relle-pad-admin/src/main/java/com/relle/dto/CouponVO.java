package com.relle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;

public class CouponVO {
    private Long checkoutId;
    private String couponId;
    private String couponName;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponValidStarttime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime couponValidEndtime;
    private Byte couponStatus;

    public Long getCheckoutId() {
        return checkoutId;
    }

    public void setCheckoutId(Long checkoutId) {
        this.checkoutId = checkoutId;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public LocalDateTime getCouponValidStarttime() {
        return couponValidStarttime;
    }

    public void setCouponValidStarttime(LocalDateTime couponValidStarttime) {
        this.couponValidStarttime = couponValidStarttime;
    }

    public LocalDateTime getCouponValidEndtime() {
        return couponValidEndtime;
    }

    public void setCouponValidEndtime(LocalDateTime couponValidEndtime) {
        this.couponValidEndtime = couponValidEndtime;
    }

    public Byte getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(Byte couponStatus) {
        this.couponStatus = couponStatus;
    }
}
