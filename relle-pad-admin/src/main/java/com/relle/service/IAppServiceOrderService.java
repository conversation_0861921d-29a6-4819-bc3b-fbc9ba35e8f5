package com.relle.service;

import com.relle.common.api.CommonResult;
import com.relle.dto.OrderDTO;
import com.relle.dto.OrderDao;
import com.relle.mbg.model.AppServiceOrder;
import com.relle.mbg.model.AppServiceSuborder;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public interface IAppServiceOrderService {
    List<OrderDTO> getMyCreateOrder(String storeId, String unionid) throws ParseException;
    List<OrderDTO> listAllByDate(String storeId, LocalDate date, String unionid) throws ParseException;

    List<OrderDTO> listAllByCustomer(String unionid);

    AppServiceSuborder getSuborder(String suborderId);
    AppServiceOrder getOrder(String orderId);
    CommonResult<?> createOrder(OrderDao orderDao) throws ParseException;
    CommonResult<?> cancelOrder(String orderId);
    CommonResult<?> updateBookTime(String orderId, LocalDateTime bookTime);
    CommonResult<?> writeOff(String orderId,String userId,String customerSource,String sourceOrderId);
    CommonResult<?> modifyRoomId(String orderId,String userId,String roomId);
}
