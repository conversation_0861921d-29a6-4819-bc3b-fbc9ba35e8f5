package com.relle.service;

import com.relle.mbg.model.AppAvailableTime;
import com.relle.mbg.model.AppServiceSuborder;

import java.time.LocalDateTime;
import java.util.List;

public interface IBatchCacheService {
    String getRoomId(String storeId, LocalDateTime bookTime);
    String getRoomId(String storeId, String date);
    void releaseRoomId(String storeId, LocalDateTime booktime_d, String roomId);
    List<AppAvailableTime> getAvailableTimeFormCache(String storeId, String serviceItemId, String date);
    boolean canChangeRoom(String roomId, AppServiceSuborder suborder);
    void comfirmSuborder(AppServiceSuborder suborder);
}
