package com.relle.service.impl;

import com.relle.common.utils.NumberGenerator;
import com.relle.mbg.mapper.AppServiceOperationRecordMapper;
import com.relle.mbg.model.AppServiceOperationRecord;
import com.relle.mbg.model.AppServiceOperationRecordExample;
import com.relle.service.IAppServiceOperationRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AppServiceOperationRecordServiceImpl implements IAppServiceOperationRecordService {
    @Resource
    private AppServiceOperationRecordMapper appServiceOperationRecordMapper;
    @Override
    public int addRecord(String unionid, String suborderId, String storeId, String roomId,String serviceOperatorId) {
        AppServiceOperationRecordExample example = new AppServiceOperationRecordExample();
        example.createCriteria().andSuborderIdEqualTo(suborderId);

        List<AppServiceOperationRecord> list = appServiceOperationRecordMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)) {
             LocalDateTime now = LocalDateTime.now();
            AppServiceOperationRecord record = new AppServiceOperationRecord();
            record.setUnionid(unionid);
            record.setSuborderId(suborderId);
            record.setStoreId(storeId);
            record.setRoomId(roomId);
            record.setServiceOperationId(NumberGenerator.getOperationId(suborderId, 1));//生成一个操作编号
            record.setServiceOperatorId(serviceOperatorId);
            record.setServiceStarttime(now);
            record.setServiceOperationStatus(OperationStatusEnum.TEST_START.getCode());
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setCreateBy(unionid);
            record.setUpdateBy(unionid);

            appServiceOperationRecordMapper.insertSelective(record);
        }
        return 1;
    }

    @Override
    public AppServiceOperationRecord getOperationRecord(String suborderId) {
        AppServiceOperationRecordExample example = new AppServiceOperationRecordExample();
        example.createCriteria().andSuborderIdEqualTo(suborderId);

        List<AppServiceOperationRecord> list = appServiceOperationRecordMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)){
            return null;
        } else {
            return list.get(0);
        }
    }

    @Override
    public int stepOneEnd(Long id) {
        System.out.println("**********"+id);
        AppServiceOperationRecord record = new AppServiceOperationRecord();
        record.setServiceOperationStatus(OperationStatusEnum.TEST_END.getCode());
        record.setId(id);
        return appServiceOperationRecordMapper.updateByPrimaryKeySelective(record);
    }
    @Override
    public int stepTwoStart(Long id) {
        AppServiceOperationRecord record = new AppServiceOperationRecord();
        record.setServiceOperationStatus(OperationStatusEnum.NURSE_START.getCode());
        record.setNursingStarttime(LocalDateTime.now());
        record.setId(id);
        return appServiceOperationRecordMapper.updateByPrimaryKeySelective(record);
    }
    @Override
    public int stepTwoEnd(Long id) {
        AppServiceOperationRecord record = new AppServiceOperationRecord();
        record.setServiceOperationStatus(OperationStatusEnum.NURSE_END.getCode());
        record.setNursingEndtime(LocalDateTime.now());
        record.setId(id);
        return appServiceOperationRecordMapper.updateByPrimaryKeySelective(record);
    }
    @Override
    public int serviceEnd(Long id) {
        AppServiceOperationRecord record = new AppServiceOperationRecord();
        record.setServiceOperationStatus(OperationStatusEnum.FINISHED.getCode());
        record.setServiceEndtime(LocalDateTime.now());
        record.setId(id);
        return appServiceOperationRecordMapper.updateByPrimaryKeySelective(record);
    }
}
