package com.relle.service.impl;

import com.relle.common.api.CommonResult;
import com.relle.mbg.mapper.AppAvailableTimeMapper;
import com.relle.mbg.mapper.AppServiceSuborderMapper;
import com.relle.mbg.model.AppAvailableTime;
import com.relle.service.IAppAvailableTimeService;
import com.relle.service.IBatchCacheService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;

@Service
public class AppAvailableTimeServiceImpl implements IAppAvailableTimeService {

    @Resource
    private AppAvailableTimeMapper appAvailableTimeMapper;

    @Resource
    private AppServiceSuborderMapper appServiceSuborderMapper;

    // private int roomNum = 4;
    @Resource
    private IBatchCacheService iBatchCacheService;


    @Override
    public CommonResult<?> getAvailableTimeList(String storeId, String serviceId, String date) {
        return CommonResult.succeeded(iBatchCacheService.getAvailableTimeFormCache(storeId,serviceId,date));
        /*AppAvailableTimeExample example = new AppAvailableTimeExample();
        example.createCriteria().andAvailableDateEqualTo(date);
        List<AppAvailableTime> appAvailableTimes =  appAvailableTimeMapper.selectByExample(example);
        List<Short> shortList = new ArrayList<>();
        shortList.add(ServiceOrderStatusEnum.PAY_SUCCESS.getCode());
        shortList.add(ServiceOrderStatusEnum.WAIT_PAY.getCode());

        Calendar nowAdd3 = Calendar.getInstance();
        //nowAdd3.add(Calendar.HOUR_OF_DAY,3);

        for (AppAvailableTime appAvailableTime:appAvailableTimes) {
            String availableDate = appAvailableTime.getAvailableDate();
            Short availableTime = appAvailableTime.getAvailableTime();

            try {
                Date availableDate_date = DateTimeUtil.getDateTime(availableDate+availableTime,"yyyy-MM-ddHHmm").getTime();
                if(availableDate_date.getTime() < nowAdd3.getTimeInMillis()){
                    appAvailableTime.setStatus((byte)0);
                } else {
                    //根据店铺房间情况，确认订单是否已占满该时段
                    AppServiceSuborderExample example1 = new AppServiceSuborderExample();
                    example1.createCriteria()
                            .andBookTimeEqualTo(availableDate_date)
                            .andSuborderStatusIn(shortList);

                    List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(example1);
                    if (suborders.size() >= roomNum) {
                        appAvailableTime.setStatus((byte) 0);
                    }
                }

            } catch (ParseException e) {
                e.printStackTrace();
            }


        }
        return CommonResult.succeeded(appAvailableTimes);*/
    }

    @Override
    public CommonResult<?> createAvailableTime(String startDateStr, String endDateStr) {
        LocalDate startDate = LocalDate.parse(startDateStr);
        LocalDate endDate = LocalDate.parse(endDateStr);
        Short[] times = {1000,1130,1300,1430,1600,1800,1930,2000};
        while(startDate.isBefore(endDate)){
            for (int i=0;i<times.length;i++){
                AppAvailableTime time = new AppAvailableTime();
                time.setAvailableDate(startDate);
                time.setAvailableTime(LocalTime.parse((times[i]).toString()));
                time.setStatus((byte)1);
                appAvailableTimeMapper.insertSelective(time);
            }
            startDate = startDate.plusDays(1);
        }

        return null;
    }
}
