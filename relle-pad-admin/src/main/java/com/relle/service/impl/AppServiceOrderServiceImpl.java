package com.relle.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.common.api.CommonResult;
import com.relle.common.utils.NumberGenerator;
import com.relle.dto.OrderDTO;
import com.relle.dto.OrderDao;
import com.relle.mbg.mapper.AppCustomerInfoMapper;
import com.relle.mbg.mapper.AppServiceOrderMapper;
import com.relle.mbg.mapper.AppServiceSuborderMapper;
import com.relle.mbg.mapper.AppStoreRoomMapper;
import com.relle.mbg.model.*;
import com.relle.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Service
public class AppServiceOrderServiceImpl implements IAppServiceOrderService {
    private static final Logger logger = LoggerFactory.getLogger(AppServiceOrderServiceImpl.class);
    @Resource
    private AppServiceOrderMapper appServiceOrderMapper;
    @Resource
    private AppServiceSuborderMapper appServiceSuborderMapper;

    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;
    @Resource
    private IAppServiceItemService iAppServiceItemService;
    @Resource
    private IAppServiceOperationRecordService iAppServiceOperationRecordService;
    @Resource
    private IAppServiceOperationFeedbackService feedbackService;
    @Resource
    private IAppCustomerTestLogService iAppCustomerTestLogService;
    @Resource
    private AppStoreRoomMapper appStoreRoomMapper;
    @Resource
    private AppCustomerInfoMapper appCustomerInfoMapper;
    @Resource
    private IBatchCacheService iBatchCacheService;
    @Resource
    private StoreInfoService storeInfoService;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public List<OrderDTO> getMyCreateOrder(String storeId, String openid) throws ParseException {

        AppServiceSuborderExample example = new AppServiceSuborderExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andCreateByEqualTo("pad-admin-" + openid)
                .andSuborderStatusBetween(ServiceOrderStatusEnum.PAY_SUCCESS.getCode(),
                        ServiceOrderStatusEnum.PART_CANCELED.getCode());
        example.setOrderByClause(" suborder_status asc,book_time asc ");

        List<AppServiceSuborder> orders = appServiceSuborderMapper.selectByExample(example);
        List<OrderDTO> orderDTOS = new ArrayList<>();
        for (AppServiceSuborder suborder : orders) {
            // 查询子订单
            OrderDTO dto = new OrderDTO();
            dto.setOrderId(suborder.getOrderId());
            dto.setSubOrderId(suborder.getSuborderId());
            AppServiceOrder order = getOrder(suborder.getOrderId());
            dto.setWriteOff(order.getWriteOff());
            dto.setCustomerSource(order.getCustomerSource());
            dto.setSourceOrderId(order.getSourceOrderId());
            String customerName = order.getContactName();
            String customerPhone = order.getContactPhone();
            AppCustomerInfo info = iAppCustomerInfoService.get(suborder.getUnionid());

            AppServiceItem item = iAppServiceItemService.getItem(suborder.getServiceItemId());
            dto.setServiceItemId(item.getServiceId());
            dto.setServiceItemName(item.getServiceName());
            dto.setServiceTimeLength(
                    item.getServicePreparationSec() + item.getServiceDurationSec() + item.getServiceClosingSec());
            dto.setBookTime(suborder.getBookTime());
            dto.setCustomerName(customerName);
            dto.setCustomerPhone(customerPhone);

            String birthday = info.getUserBirthdate();
            int age = 0;
            if (StringUtils.hasLength(birthday)) {
                try {
                    LocalDate today = LocalDate.now();
                    LocalDate birthDate = LocalDate.parse(birthday, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    age = _calcAge(today, birthDate);
                } catch (Exception e) {

                }
            }
            dto.setCustomerAge(age);
            dto.setCustomerGender(info.getUserGender());

            AppServiceOperationRecord record = iAppServiceOperationRecordService
                    .getOperationRecord(suborder.getSuborderId());
            dto.setOperationRecord(record);
            Short suborderStatus = suborder.getSuborderStatus();
            if (suborderStatus == ServiceOrderStatusEnum.CANCELING.getCode() ||
                    suborderStatus == ServiceOrderStatusEnum.CANCELED.getCode() ||
                    suborderStatus == ServiceOrderStatusEnum.PART_CANCELED.getCode()) {
                int status = OperationOrderStatusEnum.CANCELED.getCode();
                dto.setServiceStatus(status);
            } else {

                int status = OperationOrderStatusEnum.WAIT_SERVICE.getCode();
                if (record != null) {
                    // 是否服务结束
                    if (record.getServiceOperationStatus() == OperationStatusEnum.FINISHED.getCode()) {
                        // 判断是否已反馈
                        AppServiceOperationFeedback feedBack = feedbackService
                                .getFeedBack(record.getServiceOperationId());
                        dto.setOperationFeedback(feedBack);
                        boolean hasFeedback = (feedBack != null);
                        if (!hasFeedback) {
                            status = OperationOrderStatusEnum.WAIT_FEEKBACK.getCode();
                        } else {
                            status = OperationOrderStatusEnum.FINISHED.getCode();
                        }
                    } else {
                        status = OperationOrderStatusEnum.SERVERING.getCode();
                    }
                }
                dto.setServiceStatus(status);
            }
            dto.setCustomerUnionid(info.getUnionid());
            dto.setCustomerId(info.getCustomerId());
            orderDTOS.add(dto);
        }

        Collections.sort(orderDTOS, new Comparator<OrderDTO>() {
            @Override
            public int compare(OrderDTO o1, OrderDTO o2) {
                if (o1.getServiceStatus() == OperationOrderStatusEnum.SERVERING.getCode()) {
                    return -1;
                } else if (o2.getServiceStatus() == OperationOrderStatusEnum.SERVERING.getCode()) {
                    return 1;
                }
                return 0;
            }
        });

        return orderDTOS;
    }

    @Override // listOrderByUser() getOrderListByDate()
    public List<OrderDTO> listAllByDate(String storeId, LocalDate date, String unionid) throws ParseException {
        LocalDateTime startDate = date.atStartOfDay();
        LocalDateTime endDate = date.plusDays(1).atStartOfDay();
        AppServiceSuborderExample example = new AppServiceSuborderExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andBookTimeBetween(startDate, endDate)
                .andSuborderStatusBetween(ServiceOrderStatusEnum.PAY_SUCCESS.getCode(),
                        ServiceOrderStatusEnum.PART_CANCELED.getCode());

        example.setOrderByClause(" suborder_status asc,book_time asc ");

        List<AppServiceSuborder> orders = appServiceSuborderMapper.selectByExample(example);
        List<OrderDTO> orderDTOS = new ArrayList<>();
        for (AppServiceSuborder suborder : orders) {
            // 查询子订单
            OrderDTO dto = new OrderDTO();

            dto.setOrderId(suborder.getOrderId());
            dto.setSubOrderId(suborder.getSuborderId());
            dto.setRoomId(suborder.getRoomId());

            AppServiceOrder order = getOrder(suborder.getOrderId());
            dto.setWriteOff(order.getWriteOff());
            dto.setCustomerSource(order.getCustomerSource());
            dto.setSourceOrderId(order.getSourceOrderId());
            String customerName = order.getContactName();
            String customerPhone = order.getContactPhone();
            AppCustomerInfo info = iAppCustomerInfoService.get(suborder.getUnionid());

            AppServiceItem item = iAppServiceItemService.getItem(suborder.getServiceItemId());
            dto.setServiceItemId(item.getServiceId());
            dto.setServiceItemName(item.getServiceName());
            dto.setServiceTimeLength(
                    item.getServicePreparationSec() + item.getServiceDurationSec() + item.getServiceClosingSec());
            dto.setBookTime(suborder.getBookTime());
            dto.setCustomerName(customerName);
            dto.setCustomerPhone(customerPhone);
            dto.setSubOrderAmount(suborder.getSuborderAmount());
            String birthday = info.getUserBirthdate();
            int age = 0;
            if (StringUtils.hasLength(birthday)) {
                try {
                    LocalDate today = LocalDate.now();
                    LocalDate birthDate = LocalDate.parse(birthday, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    age = _calcAge(today, birthDate);
                } catch (Exception e) {

                }
            }
            dto.setCustomerAge(age);
            dto.setCustomerGender(info.getUserGender());
            dto.setCustomerUnionid(info.getUnionid());
            dto.setCustomerId(info.getCustomerId());
            dto.setCreateUser(suborder.getCreateBy());
            Short suborderStatus = suborder.getSuborderStatus();
            if (suborderStatus == ServiceOrderStatusEnum.CANCELING.getCode() ||
                    suborderStatus == ServiceOrderStatusEnum.CANCELED.getCode() ||
                    suborderStatus == ServiceOrderStatusEnum.PART_CANCELED.getCode()) {
                int status = OperationOrderStatusEnum.CANCELED.getCode();
                dto.setServiceStatus(status);
            } else {
                dto.setServiceStatus(OperationOrderStatusEnum.UNTREATED.getCode());
            }
            orderDTOS.add(dto);
        }

        return orderDTOS;
    }

    @Override
    public List<OrderDTO> listAllByCustomer(String unionid) {
        AppServiceSuborderExample example = new AppServiceSuborderExample();
        example.createCriteria()
                .andUnionidEqualTo(unionid)
                .andSuborderStatusBetween(ServiceOrderStatusEnum.PAY_SUCCESS.getCode(),
                        ServiceOrderStatusEnum.PART_CANCELED.getCode());

        example.setOrderByClause(" suborder_status asc,book_time asc ");

        List<AppServiceSuborder> orders = appServiceSuborderMapper.selectByExample(example);
        List<OrderDTO> orderDTOS = new ArrayList<>();
        for (AppServiceSuborder suborder : orders) {
            // 查询子订单
            OrderDTO dto = new OrderDTO();

            dto.setOrderId(suborder.getOrderId());
            dto.setSubOrderId(suborder.getSuborderId());
            dto.setRoomId(suborder.getRoomId());

            AppServiceOrder order = getOrder(suborder.getOrderId());
            dto.setWriteOff(order.getWriteOff());
            dto.setCustomerSource(order.getCustomerSource());
            dto.setSourceOrderId(order.getSourceOrderId());
            String customerName = order.getContactName();
            String customerPhone = order.getContactPhone();
            AppCustomerInfo info = iAppCustomerInfoService.get(suborder.getUnionid());

            AppServiceItem item = iAppServiceItemService.getItem(suborder.getServiceItemId());
            dto.setServiceItemId(item.getServiceId());
            dto.setServiceItemName(item.getServiceName());
            dto.setServiceTimeLength(
                    item.getServicePreparationSec() + item.getServiceDurationSec() + item.getServiceClosingSec());
            dto.setBookTime(suborder.getBookTime());
            dto.setCustomerName(customerName);
            dto.setCustomerPhone(customerPhone);
            dto.setSubOrderAmount(suborder.getSuborderAmount());
            String birthday = info.getUserBirthdate();
            int age = 0;
            if (StringUtils.hasLength(birthday)) {
                try {
                    LocalDate today = LocalDate.now();
                    LocalDate birthDate = LocalDate.parse(birthday, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    age = _calcAge(today, birthDate);
                } catch (Exception e) {

                }
            }
            dto.setCustomerAge(age);
            dto.setCustomerGender(info.getUserGender());
            dto.setCustomerUnionid(info.getUnionid());
            dto.setCustomerId(info.getCustomerId());
            dto.setCreateUser(suborder.getCreateBy());
            Short suborderStatus = suborder.getSuborderStatus();
            if (suborderStatus == ServiceOrderStatusEnum.CANCELING.getCode() ||
                    suborderStatus == ServiceOrderStatusEnum.CANCELED.getCode() ||
                    suborderStatus == ServiceOrderStatusEnum.PART_CANCELED.getCode()) {
                int status = OperationOrderStatusEnum.CANCELED.getCode();
                dto.setServiceStatus(status);
            } else {
                dto.setServiceStatus(OperationOrderStatusEnum.UNTREATED.getCode());
            }

            dto.setOperationRecord(iAppServiceOperationRecordService.getOperationRecord(suborder.getSuborderId()));
            if (dto.getOperationRecord() != null) {
                dto.setOperationFeedback(feedbackService.getFeedBack(dto.getOperationRecord().getServiceOperationId()));
                AppCustomerTestLog appCustomerTestLog = iAppCustomerTestLogService
                        .getByOperationId(dto.getOperationRecord().getServiceOperationId());
                dto.setTestLog(appCustomerTestLog);
            }
            dto.setStoreInfo(storeInfoService.getByStoreId(suborder.getStoreId()));

            orderDTOS.add(dto);
        }

        return orderDTOS;
    }

    private int _calcAge(LocalDate today, LocalDate birthday_d) {
        long dayMargin = today.toEpochDay() - birthday_d.toEpochDay();
        return (int) (dayMargin / 365);
    }

    @Override
    public AppServiceSuborder getSuborder(String suborderId) {
        AppServiceSuborderExample example = new AppServiceSuborderExample();
        example.createCriteria()
                .andSuborderIdEqualTo(suborderId);
        List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(example);
        if (suborders.isEmpty()) {
            return null;
        }
        AppServiceSuborder suborder = suborders.get(0);
        return suborder;
    }

    public List<AppServiceSuborder> _getAllSuborder(String orderId) {
        AppServiceSuborderExample example = new AppServiceSuborderExample();
        example.createCriteria()
                .andOrderIdEqualTo(orderId);
        List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(example);
        return suborders;
    }

    @Override // getOrderByOrderId
    public AppServiceOrder getOrder(String orderId) {
        AppServiceOrderExample example = new AppServiceOrderExample();
        example.createCriteria()
                .andOrderIdEqualTo(orderId);
        List<AppServiceOrder> orders = appServiceOrderMapper.selectByExample(example);
        if (orders.isEmpty()) {
            return null;
        }
        AppServiceOrder order = orders.get(0);
        return order;
    }

    @Override
    @Transactional // createSubOrder() Object Order, fileds, default
    public CommonResult<?> createOrder(OrderDao orderDao) throws ParseException {
        AppCustomerInfo info = orderDao.getCustomerInfo();

        AppCustomerInfo updateInfo = new AppCustomerInfo();
        updateInfo.setId(info.getId());
        updateInfo.setUserGender(orderDao.getCustomerGender());
        appCustomerInfoMapper.updateByPrimaryKeySelective(updateInfo);

        AppServiceOrder order = new AppServiceOrder();
        order.setOrderId(NumberGenerator.getServiceOrderId(orderDao.getStoreId()));
        order.setSotreId(orderDao.getStoreId());
        String roomId = _getRoomIdFromCache(orderDao.getStoreId(), orderDao.getBookTime());
        if (roomId == null) {
            return CommonResult.failed("该时段已约满");
        }

        order.setRoomId(roomId);

        order.setUnionid(info.getUnionid());

        order.setOrderAmount(new BigDecimal(0));
        order.setOriginPrice(new BigDecimal(0));
        order.setReductionAmount(new BigDecimal(0));

        order.setContactName(orderDao.getCustomerName());
        order.setContactPhone(orderDao.getCustomerPhone());

        order.setOrderStatus(ServiceOrderStatusEnum.PAY_SUCCESS.getCode());

        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        order.setCreateBy("pad-admin-" + orderDao.getOperator());
        order.setUpdateBy("pad-admin-" + orderDao.getOperator());

        order.setCustomerSource(orderDao.getCustomerSource());
        order.setSourceOrderId(orderDao.getSourceOrderId());

        int insert = appServiceOrderMapper.insertSelective(order);

        AppServiceSuborder suborder = new AppServiceSuborder();
        suborder.setOrderId(order.getOrderId());
        suborder.setSuborderId(order.getOrderId() + "01");

        suborder.setServiceItemId(orderDao.getServiceItemId());
        suborder.setSuborderAmount(new BigDecimal(0));
        suborder.setSuborderOriginPrice(new BigDecimal(0));
        suborder.setSuborderReductionAmount(new BigDecimal(0));

        suborder.setStoreId(orderDao.getStoreId());
        suborder.setRoomId(order.getRoomId());

        suborder.setBookTime(
                LocalDateTime.parse(orderDao.getBookTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));

        suborder.setUnionid(order.getUnionid());
        suborder.setSuborderStatus(ServiceOrderStatusEnum.PAY_SUCCESS.getCode());

        suborder.setCreateBy(order.getCreateBy());
        suborder.setUpdateBy(order.getUpdateBy());
        suborder.setCreateTime(order.getCreateTime());
        suborder.setUpdateTime(order.getUpdateTime());

        appServiceSuborderMapper.insertSelective(suborder); // subOrder

        ObjectNode resultNode = objectMapper.createObjectNode();
        resultNode.set("order", objectMapper.valueToTree(order));
        resultNode.set("suborder", objectMapper.valueToTree(suborder));

        return CommonResult.succeeded(resultNode);
    }

    @Override // updateOrder() -> cancelOrder() , updateBookTime(), updateStatusOfOders()
    public CommonResult<?> cancelOrder(String orderId) {
        AppServiceOrder order = new AppServiceOrder();
        order.setOrderStatus(ServiceOrderStatusEnum.CANCELED.getCode());
        AppServiceOrderExample orderExample = new AppServiceOrderExample();
        orderExample.createCriteria().andOrderIdEqualTo(orderId);

        int update = appServiceOrderMapper.updateByExampleSelective(order, orderExample);
        if (update == 1) {
            AppServiceSuborder suborder = new AppServiceSuborder();
            suborder.setSuborderStatus(ServiceOrderStatusEnum.CANCELED.getCode());
            AppServiceSuborderExample suborderExample = new AppServiceSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(orderId);
            update = appServiceSuborderMapper.updateByExampleSelective(suborder, suborderExample);

            List<AppServiceSuborder> suborders = _getAllSuborder(orderId);
            for (int i = 0; i < suborders.size(); i++) {
                AppServiceSuborder suborder1 = suborders.get(i);
                iBatchCacheService.releaseRoomId(suborder1.getStoreId(), suborder1.getBookTime(),
                        suborder1.getRoomId());
            }

            return CommonResult.succeeded(update);
        } else {
            return CommonResult.failed("取消失败");
        }
    }

    @Override
    @Transactional
    public CommonResult<?> updateBookTime(String orderId, LocalDateTime bookTime) {
        // 获取订单对象
        AppServiceOrderExample example = new AppServiceOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceOrder> orders = appServiceOrderMapper.selectByExample(example);
        if (orders.isEmpty()) {
            return CommonResult.failed("订单未找到");
        } else {
            AppServiceOrder order = orders.get(0);

            AppServiceSuborderExample suborderExample = new AppServiceSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(orderId);
            List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(suborderExample);
            if (suborders.isEmpty()) {
                return CommonResult.failed("订单信息出错");
            } else {
                AppServiceSuborder suborder = suborders.get(0);
                // 更新状态
                /*
                 * AppServiceOrder modifyOrder = new AppServiceOrder();
                 * modifyOrder.setId(order.getId());
                 * appServiceOrderMapper.updateByPrimaryKeySelective(modifyOrder);
                 */

                String roomId = iBatchCacheService.getRoomId(suborder.getStoreId(), bookTime);
                if (roomId == null) {
                    return CommonResult.failed("该时段已约满");
                }
                iBatchCacheService.releaseRoomId(suborder.getStoreId(), suborder.getBookTime(), suborder.getRoomId());

                AppServiceSuborder modifySuborder = new AppServiceSuborder();
                modifySuborder.setBookTime(bookTime);
                modifySuborder.setRoomId(roomId);
                int update = appServiceSuborderMapper.updateByExampleSelective(modifySuborder, suborderExample);

                AppServiceOrder modifyOrder = new AppServiceOrder();
                modifyOrder.setRoomId(roomId);
                appServiceOrderMapper.updateByExampleSelective(modifyOrder, example);

                suborder.setBookTime(bookTime);
                suborder.setRoomId(roomId);

                return CommonResult.succeeded(suborder);
            }
        }
    }

    // checkOut() updateOrder(), Object Order -> getOrderObj, getCtx(),
    // ctx->orderObj, updateOrder(orderObj)
    public CommonResult<?> writeOff(String orderId, String userId, String customerSource, String sourceOrderId) {
        AppServiceOrderExample exampleCheck = new AppServiceOrderExample();
        exampleCheck.createCriteria()
                .andSourceOrderIdEqualTo(sourceOrderId)
                .andWriteOffEqualTo((byte) 1);
        List<AppServiceOrder> checkOrders = appServiceOrderMapper.selectByExample(exampleCheck);
        if (!checkOrders.isEmpty()) {
            return CommonResult.failed("核销失败，此券号/订单号已核销过了");
        }
        // 获取订单对象
        AppServiceOrderExample example = new AppServiceOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceOrder> orders = appServiceOrderMapper.selectByExample(example);
        if (orders.isEmpty()) {
            return CommonResult.failed("订单未找到");
        } else {
            AppServiceOrder order = checkOrders.get(0);

            AppServiceSuborderExample suborderExample = new AppServiceSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(orderId);
            List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(suborderExample);
            if (suborders.isEmpty()) {
                return CommonResult.failed("订单信息出错");
            } else {
                AppServiceSuborder suborder = suborders.get(0);
                // 更新状态
                AppServiceOrder modifyOrder = new AppServiceOrder();
                modifyOrder.setId(order.getId());
                modifyOrder.setWriteOff((byte) 1);
                modifyOrder.setWriteOffUser(userId);
                modifyOrder.setWriteOffTime(LocalDateTime.now());
                modifyOrder.setCustomerSource(customerSource);
                modifyOrder.setSourceOrderId(sourceOrderId);
                int update = appServiceOrderMapper.updateByPrimaryKeySelective(modifyOrder);

                ObjectNode obj = objectMapper.createObjectNode();
                obj.put("update", update);
                return CommonResult.succeeded(obj);
            }
        }
    }

    public CommonResult<?> modifyRoomId(String orderId, String userId, String roomId) {
        // 获取订单对象
        AppServiceOrderExample example = new AppServiceOrderExample();
        example.createCriteria().andOrderIdEqualTo(orderId);
        List<AppServiceOrder> orders = appServiceOrderMapper.selectByExample(example);
        if (orders.isEmpty()) {
            return CommonResult.failed("订单未找到");
        } else {
            AppServiceOrder order = orders.get(0);

            AppServiceSuborderExample suborderExample = new AppServiceSuborderExample();
            suborderExample.createCriteria().andOrderIdEqualTo(orderId);
            List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(suborderExample);
            if (suborders.isEmpty()) {
                return CommonResult.failed("订单信息出错");
            } else {
                AppServiceSuborder suborder = suborders.get(0);
                // 验证是否可以换至新房间
                boolean canChange = iBatchCacheService.canChangeRoom(roomId, suborder);
                if (!canChange) {// 验证通过后，占房间时间，释放原房间时间
                    return CommonResult.failed(roomId + "房间该时段已被占用");
                }
                String oldRoomId = suborder.getRoomId();
                suborder.setRoomId(roomId);
                iBatchCacheService.comfirmSuborder(suborder);
                iBatchCacheService.releaseRoomId(suborder.getStoreId(), suborder.getBookTime(), oldRoomId);

                // 更新状态
                AppServiceOrder modifyOrder = new AppServiceOrder();
                modifyOrder.setId(order.getId());
                modifyOrder.setRoomId(roomId);
                modifyOrder.setUpdateBy(userId);
                modifyOrder.setUpdateTime(LocalDateTime.now());
                int update = appServiceOrderMapper.updateByPrimaryKeySelective(modifyOrder);

                AppServiceSuborder modifySuborder = new AppServiceSuborder();
                modifySuborder.setId(suborder.getId());
                modifySuborder.setRoomId(roomId);
                modifySuborder.setUpdateBy(userId);
                modifySuborder.setUpdateTime(LocalDateTime.now());
                int update2 = appServiceSuborderMapper.updateByPrimaryKeySelective(modifySuborder);

                ObjectNode obj = objectMapper.createObjectNode();
                obj.put("update", update);
                obj.put("update2", update2);
                return CommonResult.succeeded(obj);
            }
        }
    }

    // getRoomIdWithSequencial() getRoomIdWithRandom() getRoomIdWithRule2()
    // getAvailableRoomId()
    // getAvailableRoomsOfStoreByTime(storeId) ; selectRoomByRules(Rule rule);
    public String _getRoomIdFromCache(String storeId, String booktime) {
        return iBatchCacheService.getRoomId(storeId, booktime);
    }

    @Deprecated
    private String _getRoomId(String storeId, String booktime) {
        AppStoreRoomExample example = new AppStoreRoomExample();
        example.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andDeletedEqualTo((byte) 0);
        List<AppStoreRoom> roomList = appStoreRoomMapper.selectByExample(example);
        LocalDateTime booktime_d = null;
        booktime_d = LocalDateTime.parse(booktime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        List<Short> shortList = new ArrayList<>();
        shortList.add(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        shortList.add(ServiceOrderStatusEnum.PAY_SUCCESS.getCode());
        shortList.add(ServiceOrderStatusEnum.FINISHED.getCode());

        // getRoomsOfStoreByTime(storeId).stream().filter(lambda)
        // 数据库调取，根据基本的条件，1个字段查找出来
        // 连个条件分别查，然后做交集，并集处理
        for (AppStoreRoom room : roomList) {
            AppServiceSuborderExample suborderExample = new AppServiceSuborderExample();
            suborderExample.createCriteria()
                    .andBookTimeEqualTo(booktime_d)
                    .andStoreIdEqualTo(storeId)
                    .andRoomIdEqualTo(room.getRoomId())
                    .andSuborderStatusIn(shortList);
            List<AppServiceSuborder> suborders = appServiceSuborderMapper.selectByExample(suborderExample);
            if (suborders.isEmpty()) {
                return room.getRoomId();
            }
        }
        return null;
    }

}
