package com.relle.service.impl;

import com.relle.mbg.mapper.AppCustomerInfoMapper;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.mbg.model.AppCustomerInfoExample;
import com.relle.service.IAppCustomerInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppCustomerInfoServiceImpl implements IAppCustomerInfoService {
    @Resource
    private AppCustomerInfoMapper appCustomerInfoMapper;
    
    @Override
    public int update(AppCustomerInfo info) {
        return 0;
    }

    @Override
    public AppCustomerInfo get(String unionid) {
        AppCustomerInfoExample example = new AppCustomerInfoExample();
        example.createCriteria()
                .andUnionidEqualTo(unionid)
                .andDeletedEqualTo((byte)0);
        List<AppCustomerInfo> infos = appCustomerInfoMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(infos)){
            return null;
        }
        return CollectionUtils.get(infos,0);
    }
    
    @Override
    public AppCustomerInfo getCustomerByCustomerId(String customerId) {
        AppCustomerInfoExample example = new AppCustomerInfoExample();
        example.createCriteria()
                .andCustomerIdEqualTo(customerId)
                .andDeletedEqualTo((byte)0);
        List<AppCustomerInfo> infos = appCustomerInfoMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(infos)){
            return null;
        }
        return CollectionUtils.get(infos,0);
    }
    
    @Override
    public List<AppCustomerInfo> listCustomers(String customerLike) {
        AppCustomerInfoExample example = new AppCustomerInfoExample();
        AppCustomerInfoExample.Criteria criteria = example.createCriteria()
                .andDeletedEqualTo((byte)0);
        
        if (customerLike != null && !customerLike.trim().isEmpty()) {
            String likePattern = "%" + customerLike.trim() + "%";
            criteria.andWechatNicknameLike(likePattern);
        }
        
        example.setOrderByClause("create_time desc");
        return appCustomerInfoMapper.selectByExample(example);
    }
}
