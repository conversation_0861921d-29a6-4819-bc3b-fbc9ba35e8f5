package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.BundleOrderVO;
import com.relle.dto.CouponVO;
import com.relle.dto.CustomerVO;
import com.relle.mbg.model.AppCoupon;
import com.relle.mbg.model.AppCouponCheckout;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.mbg.model.AppServiceBundleSuborder;
import com.relle.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "客户管理接口")
@RestController
@RequestMapping(value = "/manage/customer/")
public class AppCustomerManageController {
    private static final Logger logger = LoggerFactory.getLogger(AppCustomerManageController.class);
    @Resource
    private IAppCustomerInfoService customerInfoService;
    @Resource
    private IAppServiceOrderService serviceOrderService;
    @Resource
    private BundleOrderService bundleOrderService;
    @Resource
    private IAppServiceItemService serviceItemService;
    @Resource
    private IAppCouponCheckoutService couponCheckoutService;
    @Resource
    private IAppCouponService couponService;
    @Resource
    private StoreInfoService storeInfoService;

    @ApiOperation(value = "根据手机号或者客户编号获取客户列表（尾号或者全部）")
    @PostMapping(value = "/list/{customerLike}")
    public CommonResult<List<AppCustomerInfo>> list (@PathVariable String customerLike) throws ParseException {
        return CommonResult.succeeded(customerInfoService.listCustomers(customerLike));
    }
    @ApiOperation(value = "根据客户编号获取客户详细信息（编号为全部）")
    @PostMapping(value = "/get/{customerId}")
    public CommonResult<CustomerVO> get (@PathVariable String customerId) throws ParseException {
        AppCustomerInfo customerInfo = customerInfoService.getCustomerByCustomerId(customerId);
        if(customerInfo == null){
            return CommonResult.failed("客户不存在");
        }
        CustomerVO vo = new CustomerVO();
        vo.setCustomerInfo(customerInfo);
        //服务订单
        vo.setOrderList(serviceOrderService.listAllByCustomer(customerInfo.getUnionid()));
        //计划包订单
        List<AppServiceBundleSuborder> suborderList = bundleOrderService.findByUnionid(customerInfo.getUnionid());
        if(suborderList!=null) {
            List<BundleOrderVO> bundleOrderVOList = suborderList.stream().map(suborder -> {
                BundleOrderVO bundleOrderVO = new BundleOrderVO();
                bundleOrderVO.setBundleOrderId(suborder.getOrderId());
                bundleOrderVO.setSuborderOriginPrice(suborder.getSuborderOriginPrice());
                bundleOrderVO.setSuborderReductionAmount(suborder.getSuborderReductionAmount());
                bundleOrderVO.setSuborderAmount(suborder.getSuborderAmount());
                bundleOrderVO.setCreateTime(suborder.getCreateTime());
                bundleOrderVO.setOrderStatus(suborder.getSuborderStatus());
                bundleOrderVO.setServiceItem(serviceItemService.getItem(suborder.getServiceItemId()));
                bundleOrderVO.setStoreInfo(storeInfoService.getByStoreId(suborder.getStoreId()));

                return bundleOrderVO;
            }).collect(Collectors.toList());
            vo.setBundleOrderList(bundleOrderVOList);
        }
        //剩余卡券
        List<AppCouponCheckout> couponCheckoutList = couponCheckoutService.getListByUnionid(customerInfo.getUnionid());
        if(couponCheckoutList!=null) {
            List<CouponVO> couponList = couponCheckoutList.stream()
                    .filter(checkout -> checkout.getCouponStatus().byteValue() == (byte) 1
                            || (checkout.getCouponStatus().byteValue() == (byte) 4 && StringUtils.hasLength(checkout.getCouponReceiveId())))
                    .map(checkout -> {
                        CouponVO couponVO = new CouponVO();
                        couponVO.setCouponId(checkout.getCouponId());
                        couponVO.setCheckoutId(checkout.getId());
                        couponVO.setCouponValidStarttime(checkout.getCouponValidStarttime());
                        couponVO.setCouponValidEndtime(checkout.getCouponValidEndtime());
                        couponVO.setCouponStatus(checkout.getCouponStatus());
                        AppCoupon coupon = couponService.getCoupon(checkout.getCouponId());
                        String couponName = "已下架";
                        if (coupon != null) {
                            couponName = coupon.getCouponName();
                        }
                        couponVO.setCouponName(couponName);
                        return couponVO;
                    }).collect(Collectors.toList());
            vo.setCouponList(couponList);
        }
        return CommonResult.succeeded(vo);
    }

    @ApiOperation(value = "卡券续期")
    @PostMapping(value = "/coupon/extension/{checkoutId}/{date}")
    public CommonResult<Integer> extension (@PathVariable Long checkoutId,
                                            @PathVariable String dateStr) throws ParseException {
        AppCouponCheckout checkout = couponCheckoutService.getById(checkoutId);
        LocalDateTime validDate = LocalDateTime.parse(dateStr+" 23:59:59");
        checkout.setCouponValidEndtime(validDate);
        checkout.setCouponStatus((byte)1);
        return CommonResult.succeeded(couponCheckoutService.update(checkout));
    }
}
