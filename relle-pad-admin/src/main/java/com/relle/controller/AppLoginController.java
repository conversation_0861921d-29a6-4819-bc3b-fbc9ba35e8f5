package com.relle.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.common.api.CommonResult;
import com.relle.common.api.ResultCodeEnum;
import com.relle.common.utils.JWTUtil;
import com.relle.mbg.model.AppEmployee;
import com.relle.mbg.model.AppStoreEmployeeRelation;
import com.relle.service.IAppEmployeeService;
import com.relle.service.IAppStoreEmployeeRelationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

@RestController
@RequestMapping(value = "/login")
@PropertySource("classpath:/wxOpenConfig_${spring.profiles.active}.properties")
/* @ConfigurationProperties(prefix = "wx.open") */
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);
    @Resource
    private IAppEmployeeService iAppEmployeeService;
    @Resource
    private IAppStoreEmployeeRelationService iAppStoreEmployeeRelationService;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private JWTUtil jwtUtil;

    @Value("${wx.open.appid}")
    private String appid;
    @Value("${wx.open.appsecret}")
    private String appsecret;
    @Value("#{'${wx.open.manager}'.split(',')}")
    private List<String> manager;

    @Value("${limit}")
    private boolean limit = true;

    @RequestMapping(value = "/jscode2session/{storeId}/{code}")
    public CommonResult<?> jscode2session(@PathVariable String storeId, @PathVariable String code,
            HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {
        logger.info("pad-admin entry into jscode2session,storeId: {},code: {}", storeId, code);

        // JSONObject returnObj = new JSONObject();

        String requestUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appid + "&secret=" + appsecret
                + "&code=" + code + "&grant_type=authorization_code";
        String responseStr = restTemplate.getForObject(requestUrl, String.class);

        try {
            ObjectNode jsonObject = (ObjectNode) objectMapper.readTree(responseStr);
            if (jsonObject.has("errcode")) {
                Integer errcode = jsonObject.get("errcode").asInt();
                logger.info("用户登录失败：{}", errcode);
                return CommonResult.failed(1000, errcode + ":" + jsonObject.get("errmsg").asText());
            }
            String openid = jsonObject.get("openid").asText();
            String unionid = jsonObject.get("unionid").asText();
            logger.info("用户登录openid：" + openid + ",unionid：" + unionid);
            logger.info("管理员有：" + manager);

            if (limit) {
                AppEmployee employee = iAppEmployeeService.getAppEmployeeByOpenId(storeId, unionid);
                if (employee == null) {
                    employee = iAppEmployeeService.getAppEmployeeByOpenId(storeId, openid);
                }
                if (employee == null) {
                    return CommonResult.failed(ResultCodeEnum.PRIVILEDGE_FORBIDDEN);
                }

                AppStoreEmployeeRelation storeEmployeeRelation = iAppStoreEmployeeRelationService
                        .getStoreEmployeeRelation(employee.getEmployeeId(), storeId);
                if (storeEmployeeRelation == null) {
                    storeEmployeeRelation = iAppStoreEmployeeRelationService
                            .getStoreEmployeeRelation(employee.getEmployeeId(), "all");
                }
                if (storeEmployeeRelation == null || storeEmployeeRelation.getRole() < 2) {
                    return CommonResult.failed(ResultCodeEnum.PRIVILEDGE_FORBIDDEN);
                }
                ObjectNode resultJsonObject = objectMapper.createObjectNode();
                resultJsonObject.put("openid", openid);
                resultJsonObject.put("unionid", unionid);
                resultJsonObject.put("token", jwtUtil.getToken(unionid, openid, 780));
                resultJsonObject.put("validTime", 780);
                return CommonResult.succeeded(resultJsonObject);

            }
        } catch (Exception e) {
            logger.info("认证服务器连接失败");
        }
        return CommonResult.failed("认证服务器连接失败");
    }
}
