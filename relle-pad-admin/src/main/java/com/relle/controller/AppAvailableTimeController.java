package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.service.IAppAvailableTimeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

@RestController
@RequestMapping(value = "/appointment/")
public class AppAvailableTimeController {
    private static final Logger logger = LoggerFactory.getLogger(AppAvailableTimeController.class);

    @Autowired
    private IAppAvailableTimeService iAppAvailableTimeService;

    @RequestMapping(value = "/getAvailableTime/{storeId}/{serviceItemId}/{date}")
    public CommonResult<?> getAvailableTime(@PathVariable String storeId, @PathVariable String serviceItemId, @PathVariable String date, HttpServletRequest request, ModelMap model) throws ParseException {
        return iAppAvailableTimeService.getAvailableTimeList(storeId,serviceItemId,date);
    }

    @RequestMapping(value = "/createAvailableTime/{startDate}_{endDate}")
    public CommonResult<?> createAvailableTime(@PathVariable String startDate,@PathVariable String endDate, HttpServletRequest request, ModelMap model) throws ParseException {
        logger.info("request params:"+startDate+","+endDate);
        return iAppAvailableTimeService.createAvailableTime(startDate,endDate);
    }


}
