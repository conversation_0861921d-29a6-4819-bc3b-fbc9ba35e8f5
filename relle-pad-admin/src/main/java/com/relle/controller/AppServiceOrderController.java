package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.OrderDTO;
import com.relle.dto.OrderDao;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.service.IAppCustomerInfoService;
import com.relle.service.IAppServiceOrderService;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * pad端服务订单类
 */
@Api(tags = "店长端服务订单接口")
@RestController
@RequestMapping(value = "/orderAdmin")
public class AppServiceOrderController {
    private static final Logger logger = LoggerFactory.getLogger(AppServiceOrderController.class);
    @Autowired
    private IAppServiceOrderService iAppServiceOrderService;
    @Autowired
    private IAppCustomerInfoService iAppCustomerInfoService;

    @Resource
    private FlowExecutor flowExecutor;

    @ApiOperation(value = "获取服务订单列表")
    @RequestMapping(value = "/listAll/{storeId}/{dateStr}")
    public CommonResult<?> listAll(@PathVariable String storeId, @PathVariable String dateStr,
            HttpServletRequest request) {
        // 获取用户信息
        String openid = (String) request.getSession().getAttribute("openid");
        // 参数验证
        LocalDate localDate = null;
        try {
            
             localDate = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd")).toLocalDate();
        } catch (Exception e) {
            logger.error(e.getMessage());
            localDate = LocalDate.now();

        }
        // 参数组装
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("storeId", storeId);
        paramMap.put("date", localDate);
        paramMap.put("openid", openid);

        // LiteFlow，流程管理,具体流程参阅listAllOrder
        List<OrderDTO> dtos = new ArrayList<>();
        LiteflowResponse response = flowExecutor.execute2Resp("listAllOrder", paramMap, dtos);
        dtos = response.getFirstContextBean();
        if (response.isSuccess()) {
            return CommonResult.succeeded(dtos);
        } else {
            return CommonResult.failed();
        }

        // return methodService(obj);
        // 把接口名，服务名，POJO类，beans, methods, enum
    }

    @RequestMapping(value = "/listMyCreateOrder/{storeId}")
    public CommonResult<?> listMyCreateOrder(@PathVariable String storeId, HttpServletRequest request,
            HttpServletResponse response) throws ParseException {
        // 获取用户信息
        String openid = (String) request.getSession().getAttribute("openid");
        logger.info("listMyCreateOrder storeId: {}, user: {}", storeId, openid);
        return CommonResult.succeeded(iAppServiceOrderService.getMyCreateOrder(storeId, openid));
    }

    @RequestMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody @Valid OrderDao orderDao, HttpServletRequest request,
            HttpServletResponse response) throws ParseException {
        String openid = (String) request.getSession().getAttribute("openid");
        AppCustomerInfo info = iAppCustomerInfoService.getCustomerByCustomerId(orderDao.getCustomerId());
        // 获取用户信息
        if (info == null) {
            return CommonResult.failed("客户编号不正确");
        }
        orderDao.setCustomerInfo(info);
        orderDao.setOperator(openid);
        return iAppServiceOrderService.createOrder(orderDao);

        // 1, get request, parameters
        // 2, generalMethod(request) -> get serviceObject by request, parse, do
        // something, return, obj
        // 3, get result
    }

    @RequestMapping(value = "/cancelOrder/{orderId}")
    public CommonResult<?> cancelOrder(@PathVariable String orderId, HttpServletRequest request,
            HttpServletResponse response) throws ParseException {
        // 获取用户信息
        String openid = (String) request.getSession().getAttribute("openid");

        logger.info("orderId: {}, modify_user: {}", orderId, openid);
        return iAppServiceOrderService.cancelOrder(orderId);
    }

    @PostMapping(value = "/updateBookTime/{orderId}")
    public CommonResult<?> updateBookTime(@PathVariable String orderId, @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        String bookTime = (String) params.get("bookTime");
        String openid = (String) request.getSession().getAttribute("openid");
        logger.info("orderId: {}, bookTime: {},modify_user: {}", orderId, bookTime, openid);
        try {
            LocalDateTime bookTimeDate = LocalDateTime.parse(bookTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            return iAppServiceOrderService.updateBookTime(orderId, bookTimeDate);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.failed("预约时间格式不正确");
        }
    }

    @RequestMapping(value = "/writeOff/{orderId}")
    public CommonResult<?> writeOff(@PathVariable String orderId, @RequestBody OrderDao orderDao,
            HttpServletRequest request) {
        String openid = (String) request.getSession().getAttribute("openid");

        String customerSource = orderDao.getCustomerSource();
        String sourceOrderId = orderDao.getSourceOrderId();
        logger.info("writeOff orderId: {} ,modify_user: {},{},{}", orderId, openid, customerSource, sourceOrderId);

        return iAppServiceOrderService.writeOff(orderId, openid, customerSource, sourceOrderId);
    }

    @RequestMapping(value = "/modifyRoomId/{orderId}/{roomId}")
    public CommonResult<?> modifyRoomId(@PathVariable String orderId, @PathVariable String roomId,
            HttpServletRequest request) {
        String openid = (String) request.getSession().getAttribute("openid");
        logger.info("modifyRoomId orderId: {} ,modify_user: {},{}", orderId, openid, roomId);

        return iAppServiceOrderService.modifyRoomId(orderId, openid, roomId);
    }

    /*
     * @GetMapping(value = "/check/{orderId}/{roomId}")
     * public CommonResult<?> modifyRoomId(@PathVariable String
     * orderId,@PathVariable String roomId,HttpServletRequest request) {
     * String openid = (String)request.getSession().getAttribute("openid");
     * logger.info("modifyRoomId orderId: {} ,modify_user: {},{}",
     * orderId,openid,roomId);
     * 
     * return iAppServiceOrderService.modifyRoomId(orderId,openid,roomId);
     * }
     */
}
