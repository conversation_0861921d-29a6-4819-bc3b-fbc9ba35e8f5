package com.relle.controller;

import com.alibaba.fastjson.JSON;
import com.relle.common.api.CommonResult;
import com.relle.dto.OrderDTO;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/test")
public class AppLiteFlowController {
    private static final Logger logger = LoggerFactory.getLogger(AppLiteFlowController.class);
    @Resource
    private FlowExecutor flowExecutor;

    @RequestMapping(value="/testParam/{code}")
    public CommonResult<?> testParam (@PathVariable  String code, HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {
        List<OrderDTO> dtos = new ArrayList<>();
        LiteflowResponse response = flowExecutor.execute2Resp("chain1", code, dtos);
        dtos = response.getFirstContextBean();
        if (response.isSuccess()){
            logger.info("执行成功，最终选择的渠道是{}", JSON.toJSON(dtos));
        }else{
            return CommonResult.failed();
        }
        return CommonResult.succeeded(dtos);
    }


}
