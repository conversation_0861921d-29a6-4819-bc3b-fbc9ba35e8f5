package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.service.IAppStoreRoomService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/room")
public class AppRoomController {
    private static final Logger logger = LoggerFactory.getLogger(AppRoomController.class);
    @Autowired
    private IAppStoreRoomService iAppStoreRoomService;

    @RequestMapping(value = "/list/{storeId}")
    public CommonResult<?> list(@PathVariable String storeId,HttpServletRequest req, HttpServletResponse res) {
        return  CommonResult.succeeded(iAppStoreRoomService.getListByStore(storeId));
    }
}
