package com.relle.controller;

import com.alibaba.druid.util.StringUtils;
import com.relle.common.api.CommonResult;
import com.relle.service.IAppServiceOperationFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@RestController
@RequestMapping(value = "/feedback")
public class AppServiceOperationFeedbackController {
    @Autowired
    private IAppServiceOperationFeedbackService feedbackService;

    @PostMapping(value = "/save")
    public CommonResult<?> signIn(@RequestBody Map<String,Object> map, HttpServletRequest request, HttpServletResponse response) {
        String unionidFromSession = (String)request.getSession().getAttribute("unionid");
        String feedbackContent = (String)map.get("feedbackContent");
        Number operationId = (Number)map.get("operationId");
        if(StringUtils.isEmpty(feedbackContent)){
            return CommonResult.failed("请输入反馈内容");
        }

         return CommonResult.succeeded(feedbackService.save(operationId.longValue(),feedbackContent,unionidFromSession));
    }
}
