package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.service.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/operation")
public class AppServiceOperationRecordController {

    @Resource
    private IAppServiceOrderService iAppServiceOrderService;
    @Resource
    private IAppServiceOperationRecordService iAppServiceOperationRecordService;
    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;
    @Resource
    private IAppServiceItemService iAppServiceItemService;
    @Resource
    private IAppOrderRecommendService iAppOrderRecommendService;
    @Resource
    private IAppActivityService iAppActivityService;
    @Resource
    private IAppActivityCouponService iAppActivityCouponService;
    @Resource
    private IAppCouponService iAppCouponService;
    @Resource
    private IBatchCacheService iBatchCacheService;
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;

    // TODO: Re-enable when type mismatch issues are resolved
    /*
    @RequestMapping(value = "/signIn/{orderId}/{serviceOperatorId}")
    public CommonResult<?> signIn(@PathVariable String orderId, @PathVariable String serviceOperatorId,
            HttpServletRequest request, HttpServletResponse response) {
        String unionidFromSession = (String) request.getSession().getAttribute("unionid");
        // 确认订单，是否存在，状态是否符合，判断此订单是否是本人
        boolean check = iAppServiceOrderService.checkOrder(orderId, unionidFromSession);
        if (check) {
            int update = iAppServiceOrderService.updateOrderFinished(orderId, "signIn", serviceOperatorId);
            if (update > 0) {
                return CommonResult.succeeded(update);
            } else {
                return CommonResult.failed("操作失败，请重新扫码");
            }
        } else {
            AppCustomerInfo customerInfo = iAppCustomerInfoService.get(unionidFromSession);
            return CommonResult.failed(customerInfo.getCustomerId() + "：操作失败，客户信息不匹配！");
        }

    }
    */

    @RequestMapping(value = "/getOperation/{orderId}")
    public CommonResult<?> operation(@PathVariable String orderId, HttpServletRequest request,
            HttpServletResponse response) {
        return CommonResult.succeeded(iAppServiceOperationRecordService.getOperationRecord(orderId));
    }

    @RequestMapping(value = "/stepOneEnd/{id}")
    public CommonResult<?> stepOneEnd(@PathVariable("id") Long id, HttpServletRequest request,
            HttpServletResponse response) {

        return CommonResult.succeeded(iAppServiceOperationRecordService.stepOneEnd(id));
    }

    @RequestMapping(value = "/stepTwoStart/{id}")
    public CommonResult<?> stepTwoStart(@PathVariable("id") Long id, HttpServletRequest request,
            HttpServletResponse response) {
        return CommonResult.succeeded(iAppServiceOperationRecordService.stepTwoStart(id));
    }

    @RequestMapping(value = "/stepTwoEnd/{id}")
    public CommonResult<?> stepTwoEnd(@PathVariable("id") Long id, HttpServletRequest request,
            HttpServletResponse response) {

        return CommonResult.succeeded(iAppServiceOperationRecordService.stepTwoEnd(id));
    }

    // TODO: Re-enable when complex services are fully implemented
    /*
    @RequestMapping(value = "/serviceEnd/{id}")
    public CommonResult<?> serviceEnd(@PathVariable("id") Long id, HttpServletRequest request,
            HttpServletResponse response) {
        String unionid = (String) request.getSession().getAttribute("unionid");
        String openid = (String) request.getSession().getAttribute("openid");
        // 结束服务
        iAppServiceOperationRecordService.serviceEnd(id);

        AppServiceOperationRecord operationRecord = iAppServiceOperationRecordService.getOperationRecordById(id);
        String suborderId = operationRecord.getSuborderId();
        AppServiceSuborder suborder = iAppServiceOrderService.getSuborder(suborderId);
        AppServiceItem item = iAppServiceItemService.getItem(suborder.getServiceItemId());

        // 释放房间
        iBatchCacheService.releaseRoomId(suborder.getStoreId(), suborder.getBookTime(), suborder.getRoomId());

        // 判断是否发放卡券
        if (item.getChannelType().byteValue() == AppServiceItemCategoryEnum.RECRUITNEW.getCode()) {
            String couponId = "TY202309090003";
            AppOrderRecommend byOrderId = iAppOrderRecommendService.getByOrderId(suborder.getOrderId());
            if (byOrderId != null) {
                iAppCouponCheckoutService.grant(
                        couponId,
                        byOrderId.getRecommendEmployeeId(),
                        1,
                        CouponStatusEnum.NO_USE.getCode(),
                        CouponCheckoutSourceEnum.RECRUIT_NEW.getCode(),
                        byOrderId.getOrderId());

                byOrderId.setGrantCoupon(couponId);
                iAppOrderRecommendService.update(byOrderId, openid);
            }
        }

        // 判断是否有消费返利活动
        AppServiceOrder order = iAppServiceOrderService.getOrder(suborder.getOrderId());
        List<AppActivity> appActivities = iAppActivityService.getActivityListByCategory(order.getSotreId(),
                ActivityCategoryEnum.REBATE_MONEY.getCode().byteValue());
        if (!CollectionUtils.isEmpty(appActivities)) {
            for (int i = 0; i < appActivities.size(); i++) {
                AppActivity appActivity = appActivities.get(i);
                List<AppActivityCoupon> coupons = iAppActivityCouponService.getByActivity(appActivity.getId());
                for (AppActivityCoupon activityCoupon : coupons) {
                    AppCoupon coupon = iAppCouponService.getCouponById(activityCoupon.getCouponId());
                    iAppCouponCheckoutService.grant(
                            coupon.getCouponId(),
                            order.getUnionid(),
                            1,
                            CouponStatusEnum.NO_USE.getCode(),
                            CouponCheckoutSourceEnum.REBATE_MONEY.getCode(),
                            order.getOrderId());

                }
            }
        }

        return CommonResult.succeeded("");
    }
    */
}
