package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.OrderDTO;
import com.relle.service.IAppServiceOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;

/**
 * pad端服务订单类
 */
@Api(tags = "pad端服务订单接口")
@RestController
@RequestMapping(value = "/order")
public class AppServiceOrderController {
    private static final Logger logger = LoggerFactory.getLogger(AppServiceOrderController.class);
    @Autowired
    private IAppServiceOrderService iAppServiceOrderService;

    @ApiOperation(value = "根据日期查询服务订单信息")
    @GetMapping(value = "/list/{storeId}/{roomId}/{date}")
    public CommonResult<?> list(@PathVariable String storeId,@PathVariable String roomId,@PathVariable String date, HttpServletRequest request, HttpServletResponse response) throws ParseException {

        //获取用户信息
        return CommonResult.succeeded(iAppServiceOrderService.getServiceOrderList(storeId,roomId,date));
    }

    // TODO: Re-enable when OrderDTO type mismatch is resolved
    /*
    @ApiOperation(value = "获取服务订单信息明细")
    @GetMapping(value = "/get/{suborderId}")
    public CommonResult<?> get(@PathVariable String suborderId,HttpServletRequest request, HttpServletResponse response) {
        //获取用户信息
        OrderDTO dto = iAppServiceOrderService.getServiceOrder(suborderId);
        if(dto==null){
            CommonResult.failed("订单信息出错");
        }
        return CommonResult.succeeded(dto);
    }
    */

}
