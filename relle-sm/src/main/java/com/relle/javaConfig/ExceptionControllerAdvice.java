package com.relle.javaConfig;

import com.relle.common.api.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


@RestControllerAdvice(basePackages="com.relle")
public class ExceptionControllerAdvice {
    private static final Logger logger = LoggerFactory.getLogger(ExceptionControllerAdvice.class);

   /* @ExceptionHandler()
    public CommonResult<?> handleValidException(MethodArgumentNotValidException e) {

        logger.error("数据校验出现问题：{}，异常类型：{}",e.getMessage(),e.getClass());

        // ExceptionHandler里面可以获取BindingResult，通过BindingResult对象获取实际的错误信息
        BindingResult bindingResult = e.getBindingResult();
        StringBuffer stringBuffer = new StringBuffer();
        bindingResult.getFieldErrors().forEach( item -> {
            String message = item.getDefaultMessage();
            String field = item.getField();
            stringBuffer.append(field + ":" + message + "");
        });

        return CommonResult.failed(stringBuffer + "");
    }*/
   @ExceptionHandler(value=Exception.class)
   public CommonResult<?> handleGeneralExceptions(Exception e) {
       logger.error("系统错误:", e);
       return CommonResult.failed("系统异常");
   }
    @ExceptionHandler(value=Throwable.class)
    public CommonResult<?> handleException(Throwable throwable) {

        logger.error("系统错误:", throwable);
        return CommonResult.failed("系统错误");
    }


}
