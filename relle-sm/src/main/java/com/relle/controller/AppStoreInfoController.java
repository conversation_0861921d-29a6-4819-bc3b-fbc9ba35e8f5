package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.mbg.model.AppStoreInfo;
import com.relle.mbg.model.AppStoreServiceRelation;
import com.relle.service.IAppStoreInfoService;
import com.relle.service.IAppStoreServiceRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "店铺接口")
@RestController
@RequestMapping(value = "/store_info")
public class AppStoreInfoController {
    private static final Logger logger = LoggerFactory.getLogger(AppStoreInfoController.class);
    @Resource
    private IAppStoreInfoService appStoreInfoService;
    @Resource
    private IAppStoreServiceRelationService iAppStoreServiceRelationService;

    @ApiOperation(value = "获取所有店铺,0为美容店，1为诊所")
    @GetMapping(value = "/list/{storeType}")
    public CommonResult<?> list(@PathVariable  String storeType,HttpServletRequest request, ModelMap model) {
        //String store_type = request.getParameter("store_type");
        Byte store_type_b = Byte.parseByte(storeType,Character.MIN_RADIX);
       return appStoreInfoService.queryAllStoreByType(store_type_b);
    }
    @ApiOperation(value = "获取某个店铺")
    @GetMapping(value = "/get/{storeId}")
    public CommonResult<?> get(@PathVariable  String storeId,HttpServletRequest request, ModelMap model) {
        return appStoreInfoService.getByStoreId(storeId);
    }
    @ApiOperation(value = "根据产品获取店铺列表")
    @GetMapping(value = "/listByItem/{serviceItemId}")
    public CommonResult<?> listByItem(@PathVariable  String serviceItemId,HttpServletRequest request, ModelMap model) {
        //String store_type = request.getParameter("store_type");
        String storeType = "0";
        Byte store_type_b = Byte.parseByte(storeType,Character.MIN_RADIX);
        List<AppStoreInfo> appStoreInfos = appStoreInfoService.listAllStoreByType(store_type_b);
        List<AppStoreServiceRelation> storeIdByServiceItem = iAppStoreServiceRelationService.getStoreIdByServiceItem(serviceItemId);
        List<String> storeIds = storeIdByServiceItem.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
        List<AppStoreInfo> stores = appStoreInfos.stream().filter(appStoreInfo -> storeIds.contains(appStoreInfo.getStoreId())).collect(Collectors.toList());
        return CommonResult.succeeded(stores);
    }
}
