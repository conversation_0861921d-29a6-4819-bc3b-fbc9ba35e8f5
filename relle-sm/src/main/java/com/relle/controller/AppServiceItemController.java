package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.service.IAppServiceItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
@Api(tags = "产品接口")
@RestController
@RequestMapping(value = "/service_item")
public class AppServiceItemController {

    private static final Logger logger = LoggerFactory.getLogger(AppServiceItemController.class);

    @Autowired
    private IAppServiceItemService appServiceItemService;

    @GetMapping(value = "/list/{storeId}")
    public CommonResult<?> list(@PathVariable String storeId, HttpServletRequest request, ModelMap model) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        String serviceItemCategory = request.getParameter("serviceItemCategory");
        if(serviceItemCategory.isEmpty()){
            serviceItemCategory = "1";
            return appServiceItemService.listAllItemByStore(storeId,unionid);
        } else {
            Byte serviceItemCategoryB = Byte.valueOf(serviceItemCategory);
            return appServiceItemService.listAllItemByStore(storeId,serviceItemCategoryB,unionid);
        }

    }
    @GetMapping(value = "/list/{storeId}/{serviceItemCategory}")
    public CommonResult<?> list(@PathVariable String storeId,@PathVariable String serviceItemCategory, HttpServletRequest request, ModelMap model) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        Byte serviceItemCategoryB = Byte.valueOf(serviceItemCategory);
        return appServiceItemService.listAllItemByStore(storeId,serviceItemCategoryB,unionid);
    }

    @GetMapping(value = "/get/{storeId}/{serviceItemId}")
    public CommonResult<?> get(@PathVariable String storeId,@PathVariable String serviceItemId,HttpServletRequest request, ModelMap model) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        return appServiceItemService.getOneByServiceItemId(storeId,serviceItemId,unionid);
    }

    @GetMapping(value = "/get/{serviceItemId}")
    public CommonResult<?> get(@PathVariable String serviceItemId,HttpServletRequest request, ModelMap model) {
        String storeId = "SH0001";
        String unionid = (String)request.getSession().getAttribute("unionid");
        return appServiceItemService.getOneByServiceItemId(storeId,serviceItemId,unionid);
    }

    @GetMapping(value = "/getByActivity/{serviceItemId}")
    public CommonResult<?> getByActivity(@PathVariable String serviceItemId,HttpServletRequest request, ModelMap model) {
        String storeId = "SH0001";
        String unionid = (String)request.getSession().getAttribute("unionid");
        return appServiceItemService.getOneByServiceItemId(storeId,serviceItemId,unionid);
    }
    @ApiOperation(value = "加单产品列表")
    @GetMapping(value = "/listAdditional/{storeId}")
    public CommonResult<?> listAdditional(@PathVariable String storeId, HttpServletRequest request, ModelMap model) {
        String unionid = (String)request.getSession().getAttribute("unionid");
        String serviceItemCategory = request.getParameter("serviceItemCategory");
        if(serviceItemCategory.isEmpty()){
            serviceItemCategory = "1";
            return appServiceItemService.listAllItemByStore(storeId,unionid);
        } else {
            Byte serviceItemCategoryB = Byte.valueOf(serviceItemCategory);
            return appServiceItemService.listAllItemByStore(storeId,serviceItemCategoryB,unionid);
        }

    }
}
