# MyBatis to JPA Migration Guide

This document provides a comprehensive guide for migrating from MyBatis to JPA in the Relle application.

## Overview

The migration involves:
1. Converting MyBatis models to JPA entities
2. Creating JPA repositories to replace MyBatis mappers
3. Updating service implementations to use JPA repositories
4. Configuring JPA and disabling MyBatis where appropriate

## 1. Base Entity Pattern

### Created BaseEntity Class
All entities now extend `BaseEntity` which contains common fields:
- `id` (Primary Key)
- `createBy` (Creator)
- `createTime` (Creation timestamp)
- `updateBy` (Updater)
- `updateTime` (Update timestamp)
- `deleted` (Soft delete flag)

```java
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;
    
    @Column(name = "create_by")
    protected String createBy;
    
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    protected Date createTime;
    
    // ... other common fields
}
```

## 2. Entity Migration Examples

### Before (MyBatis Model)
```java
public class AppCustomerInfo implements Serializable {
    private Long id;
    private String unionid;
    private String miniOpenid;
    // ... other fields
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private Byte deleted;
}
```

### After (JPA Entity)
```java
@Entity
@Table(name = "app_customer_info")
public class AppCustomerInfo extends BaseEntity {
    @Column(name = "unionid")
    private String unionid;
    
    @Column(name = "mini_openid")
    private String miniOpenid;
    
    // ... other entity-specific fields only
    // Base fields inherited from BaseEntity
}
```

## 3. Repository Migration

### Before (MyBatis Mapper)
```java
@Mapper
public interface AppCustomerInfoMapper {
    int countByExample(AppCustomerInfoExample example);
    int deleteByExample(AppCustomerInfoExample example);
    int deleteByPrimaryKey(Long id);
    int insert(AppCustomerInfo record);
    List<AppCustomerInfo> selectByExample(AppCustomerInfoExample example);
    AppCustomerInfo selectByPrimaryKey(Long id);
    int updateByExample(AppCustomerInfo record, AppCustomerInfoExample example);
}
```

### After (JPA Repository)
```java
@Repository
public interface AppCustomerInfoRepository extends JpaRepository<AppCustomerInfo, Long>, 
                                                  JpaSpecificationExecutor<AppCustomerInfo> {
    Optional<AppCustomerInfo> findByUnionidAndDeleted(String unionid, Byte deleted);
    Optional<AppCustomerInfo> findByMiniOpenidAndDeleted(String miniOpenid, Byte deleted);
    List<AppCustomerInfo> findByDeleted(Byte deleted);
    boolean existsByUnionidAndDeleted(String unionid, Byte deleted);
    
    @Query("SELECT c FROM AppCustomerInfo c WHERE LOWER(c.wechatNickname) LIKE LOWER(CONCAT('%', :nickname, '%')) AND c.deleted = :deleted")
    List<AppCustomerInfo> findByWechatNicknameContainingIgnoreCaseAndDeleted(@Param("nickname") String nickname, @Param("deleted") Byte deleted);
}
```

## 4. Service Layer Migration

### Before (MyBatis Service)
```java
@Service
public class AppEmployeeServiceImpl implements IAppEmployeeService {
    @Resource
    private AppEmployeeMapper appEmployeeMapper;
    
    @Override
    public AppEmployee getAppEmployeeByOpenId(String storeId, String openId) {
        AppEmployeeExample example = new AppEmployeeExample();
        example.createCriteria()
                .andEmployeeOpenIdEqualTo(openId)
                .andDeletedEqualTo((byte)0);
        List<AppEmployee> appEmployees = appEmployeeMapper.selectByExample(example);
        if(appEmployees.isEmpty()) {
            return null;
        }
        return appEmployees.get(0);
    }
}
```

### After (JPA Service)
```java
@Service
@Transactional
public class AppEmployeeJpaService {
    @Autowired
    private AppEmployeeRepository appEmployeeRepository;
    
    public AppEmployee getAppEmployeeByOpenId(String openId) {
        return appEmployeeRepository.findByEmployeeOpenIdAndDeleted(openId, (byte) 0)
                .orElse(null);
    }
    
    // Advanced search replacing MyBatis Example classes
    public Page<AppEmployee> searchEmployees(String name, String employeeId, String phone, 
                                           Byte status, Byte departmentId, Pageable pageable) {
        Specification<AppEmployee> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));
            
            if (name != null && !name.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("employeeName")), 
                    "%" + name.toLowerCase() + "%"
                ));
            }
            // ... other dynamic criteria
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return appEmployeeRepository.findAll(spec, pageable);
    }
}
```

## 5. Configuration Changes

### JPA Configuration
```java
@Configuration
@EnableTransactionManagement
@EnableJpaAuditing
@EntityScan("com.relle.db.entity")
@EnableJpaRepositories("com.relle.db.repository")
public class JpaConfig {
    // Configuration can be extended as needed
}
```

### Application Properties
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
```

## 6. Migration Benefits

### Advantages of JPA over MyBatis:
1. **Type Safety**: Compile-time checking of queries
2. **Less Boilerplate**: No need for XML mapper files
3. **Built-in Pagination**: Native pagination support
4. **Specifications**: Dynamic query building
5. **Auditing**: Automatic timestamp management
6. **Caching**: Built-in second-level caching
7. **Lazy Loading**: Automatic relationship loading

### Query Comparison:

#### MyBatis Example Class Pattern:
```java
AppEmployeeExample example = new AppEmployeeExample();
example.createCriteria()
    .andEmployeeNameLike("%" + name + "%")
    .andEmployeeStatusEqualTo((byte) 1)
    .andDeletedEqualTo((byte) 0);
List<AppEmployee> employees = mapper.selectByExample(example);
```

#### JPA Specification Pattern:
```java
Specification<AppEmployee> spec = (root, query, cb) -> {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(cb.equal(root.get("deleted"), 0));
    predicates.add(cb.equal(root.get("employeeStatus"), 1));
    predicates.add(cb.like(cb.lower(root.get("employeeName")), "%" + name.toLowerCase() + "%"));
    return cb.and(predicates.toArray(new Predicate[0]));
};
Page<AppEmployee> employees = repository.findAll(spec, pageable);
```

## 7. Migration Steps

### Step 1: Create JPA Entities
1. Create `BaseEntity` class with common fields
2. Convert MyBatis models to JPA entities extending `BaseEntity`
3. Add proper JPA annotations (`@Entity`, `@Table`, `@Column`)

### Step 2: Create JPA Repositories
1. Create repository interfaces extending `JpaRepository` and `JpaSpecificationExecutor`
2. Add custom query methods using Spring Data JPA naming conventions
3. Add `@Query` annotations for complex queries

### Step 3: Create JPA Services
1. Create new service classes using JPA repositories
2. Implement business logic using JPA methods
3. Use Specifications for dynamic queries

### Step 4: Update Existing Services
1. Replace MyBatis mapper dependencies with JPA repositories
2. Convert MyBatis Example usage to JPA Specifications
3. Update method implementations to use JPA patterns

### Step 5: Configuration
1. Enable JPA configuration
2. Disable MyBatis configuration where appropriate
3. Update application properties

## 8. Testing the Migration

### Unit Tests Example:
```java
@DataJpaTest
class AppCustomerInfoRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private AppCustomerInfoRepository repository;
    
    @Test
    void testFindByUnionidAndDeleted() {
        // Given
        AppCustomerInfo customer = new AppCustomerInfo();
        customer.setUnionid("test-unionid");
        customer.setDeleted((byte) 0);
        entityManager.persistAndFlush(customer);
        
        // When
        Optional<AppCustomerInfo> found = repository.findByUnionidAndDeleted("test-unionid", (byte) 0);
        
        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getUnionid()).isEqualTo("test-unionid");
    }
}
```

## 9. Performance Considerations

1. **Lazy Loading**: Configure appropriate fetch strategies
2. **Batch Processing**: Use `saveAll()` for bulk operations
3. **Query Optimization**: Use `@Query` for complex queries
4. **Caching**: Enable second-level cache for read-heavy entities
5. **Pagination**: Always use `Pageable` for large result sets

## 10. Common Pitfalls and Solutions

### N+1 Query Problem
```java
// Problem: N+1 queries
List<Order> orders = orderRepository.findAll();
orders.forEach(order -> order.getCustomer().getName()); // N+1 queries

// Solution: Use fetch joins
@Query("SELECT o FROM Order o JOIN FETCH o.customer")
List<Order> findAllWithCustomer();
```

### Soft Delete Handling
```java
// Always include deleted check in queries
@Query("SELECT e FROM Employee e WHERE e.deleted = 0")
List<Employee> findActiveEmployees();

// Or use Specifications
Specification<Employee> notDeleted = (root, query, cb) -> 
    cb.equal(root.get("deleted"), 0);
```

This migration guide provides a comprehensive approach to converting from MyBatis to JPA while maintaining functionality and improving code quality.
