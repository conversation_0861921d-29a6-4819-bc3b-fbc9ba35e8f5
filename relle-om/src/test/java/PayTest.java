import com.relle.RelleApplication;
import com.relle.service.IAppAvailableTimeService;
import com.relle.service.IWXInterfaceService;
import org.junit.After;
import org.junit.Before;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.Test;
import org.junit.jupiter.api.*;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;

//@ExtendWith(SpringExtension.class)
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {RelleApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class PayTest {
    MockWebServer mockServer;
    RestTemplate restTemplate;
    private static final String host = "http://localhost";
    private static final String payUrl = "/test/pay";
    private static final int port = 8999;
    private Logger log= LoggerFactory.getLogger(PayTest.class);
    @Resource
    private IAppAvailableTimeService iAppAvailableTimeService;
    @Resource
    private IWXInterfaceService iwxInterfaceService;
    @Before
    public void before() throws IOException {
        System.out.println("单元测试开始");

       /* MockWebServer mockWebServer = new MockWebServer();
        mockWebServer.start(port);
        mockWebServer.url(payUrl); //mock api
        mockWebServer.enqueue(new MockResponse()
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .setBody("123"));
        this.mockServer = mockWebServer;
//        mockServer.start();
        HttpComponentsClientHttpRequestFactory factory =
                new HttpComponentsClientHttpRequestFactory();
        this.restTemplate = new RestTemplate(factory);*/
    }

//    public void init() throws IOException {
//        mockServer = new MockWebServer();
//        mockServer.start( 9099);
//        final Dispatcher dispatcher = new Dispatcher() {
//            @Override
//            public MockResponse dispatch(RecordedRequest request) {
//                if (request.getPath().equals("/lims")) {
//                    return new MockResponse()
//                            .setResponseCode(200)
//                            .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
//                            .setBody(limsStr);
//                }
//                if (request.getPath().equals("/lims/upload") && request.getMethod().equals("GET")) {
//                    return  new MockResponse()
//                            .setResponseCode(200)
//                            .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
//                            .setBody(uploasStr);
//                }
//                if (request.getPath().equals("/lims/upload") && request.getMethod().equals("POST")) {
//                    return  new MockResponse()
//                            .setResponseCode(200)
//                            .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE)
//                            .setBody(uploasStr);
//                }
//                return new MockResponse().setResponseCode(404);
//            }
//        };
//        mockServer.setDispatcher(dispatcher);
//        try {
//            mockServer.start(9099);
//        } catch (IOException e) {
//            e.printStackTrace();
//            System.exit(0);
//        }
//    }


   /* @Transactional
    @Rollback*/
    @Test
    public void testCreateAvailableTime() throws Exception {
        //System.out.println(payConstants.getAll());
       /* iAppAvailableTimeService.createAvailableTime("20231120","20231130");
        String result =this.restTemplate.getForObject(host +":"+ port + payUrl, String.class);
        Assertions.assertEquals("123", result);*/
        //String token = iwxInterfaceService.getToken();
        //System.out.println(token);
        String trial = iwxInterfaceService.getURLLink("pages/order/payFromServer", "trial", "o=SH00012312010915138450115");
        System.out.println(trial);
    }


    @After
    public void after() {
        System.out.println("单元测试结束");
    }
}
