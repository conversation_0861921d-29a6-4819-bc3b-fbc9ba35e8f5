import com.relle.RelleApplication;
import com.relle.mbg.model.AppActivity;
import com.relle.service.*;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {RelleApplication.class})
@EnableAsync
public class OrderTest {
    private Logger log= LoggerFactory.getLogger(OrderTest.class);
    @Resource
    private IAppAvailableTimeService iAppAvailableTimeService;

    @Autowired
    private IVSubordersService ivSubordersService;

    @Autowired
    private IAppServiceItemService iAppServiceItemService;

    @Autowired
    private AppServiceOrderServiceFactory iAppServiceOrderServiceFactory;

    @Autowired
    private IAppActivityService iAppActivityService;
    @Autowired
    private PayConstantsService payConstants;


    @Before
    public void before() {
        System.out.println("单元测试开始");
    }

    @Test
    public void testCheckOrder() {
        //System.out.println(payConstants.getAll());
        List<AppActivity> activityList = iAppActivityService.getActivityList("SH0002");
        System.out.println(activityList.size());
    }




@Test
    public void queryOrder() {
        assert iAppAvailableTimeService.getAvailableTimeList("SH0001","","2023-01-26").getCode()==1 ;
    }

    @Test
    public void modifyTime() {
        assert iAppAvailableTimeService.getAvailableTimeList("SH0001","","2023-01-26").getCode()==1 ;
    }




    @After
    public void after() {
        System.out.println("单元测试结束");
    }
}
