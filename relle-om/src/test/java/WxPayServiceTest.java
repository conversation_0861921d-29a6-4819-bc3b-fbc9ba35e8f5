import com.relle.RelleApplication;
import com.relle.service.IWxPayService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {RelleApplication.class})
public class WxPayServiceTest {
    private Logger log= LoggerFactory.getLogger(WxPayServiceTest.class);
    @Resource
    private IWxPayService iWxPayService;
    @Before
    public void before() {
        System.out.println("单元测试开始");
    }

    @Test
    public void testCreateAvailableTime() {
        //System.out.println(payConstants.getAll());
        iWxPayService.createPrepay("SH0001",(byte)1,"测试",1,"SH000123120109151384501ZN","o4GyE5FkOb6Bt--U8itfr_Al9TM4");
    }


    @After
    public void after() {
        System.out.println("单元测试结束");
    }
}
