import com.relle.RelleApplication;
import com.relle.cache.batch.BatchCache;
import com.relle.mbg.model.AppServiceSuborder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {RelleApplication.class})
@EnableAsync
public class BactCacheTest {
    // private Logger log= LoggerFactory.getLogger(BactCacheTest.class);

    @Autowired
    private BatchCache batchCache;


    @Before
    public void before() {
        System.out.println("单元测试开始");
    }

    @Test
    public void testCheckOrder() throws ParseException {
        //System.out.println(payConstants.getAll());

        System.out.println(batchCache.getRoomId("SH0003","2023-11-17 13:00"));
        System.out.println(batchCache.getRoomId("SH0003","2023-11-17 13:00"));
        System.out.println(batchCache.getRoomId("SH0001","2023-10-29 13:30"));
        System.out.println(batchCache.getRoomId("SH0001","2023-10-29 13:00"));
        System.out.println(batchCache.getRoomId("SH0001","2023-10-29 12:30"));

        //修改时间
        LocalDateTime bookTime = LocalDateTime.parse("2023-10-29 14:00");
        String roomId = batchCache.getRoomId("SH0001",bookTime.toLocalDate());
        LocalDate bookTime2 = LocalDate.parse("2023-10-29");
        batchCache.releaseRoomId("SH0001",bookTime2,"01");
        //修改房间
        AppServiceSuborder suborder = new AppServiceSuborder();
        suborder.setStoreId("SH0001");
        suborder.setBookTime(bookTime);
        suborder.setRoomId("01");
        // boolean canChange = batchCache.canChangeRoom("02",suborder);

        // String oldRoomId = suborder.getRoomId();
        suborder.setRoomId(roomId);

    }

    @After
    public void after() {
        System.out.println("单元测试结束");
    }
}
