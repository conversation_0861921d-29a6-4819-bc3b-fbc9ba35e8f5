import com.relle.RelleApplication;
import com.relle.service.IReceieveCouponService;
import com.relle.service.IReceiveCouponServiceFactory;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {RelleApplication.class})
public class ServiceFactoryTest {
    private Logger log= LoggerFactory.getLogger(ServiceFactoryTest.class);
    @Resource
    private IReceiveCouponServiceFactory iReceiveCouponServiceFactory;
    @Before
    public void before() {
        System.out.println("单元测试开始");
    }

    @Test
    public void testCreateAvailableTime() {
        //System.out.println(payConstants.getAll());
        IReceieveCouponService is_new = iReceiveCouponServiceFactory.getService("is_new");
        System.out.println(is_new.check());
    }


    @After
    public void after() {
        System.out.println("单元测试结束");
    }
}
