package com.relle.schedule;

import com.relle.enums.CouponStatusEnum;
import com.relle.mbg.mapper.AppCouponCheckoutMapper;
import com.relle.mbg.mapper.AppCouponMapper;
import com.relle.mbg.model.AppCoupon;
import com.relle.mbg.model.AppCouponCheckout;
import com.relle.mbg.model.AppCouponCheckoutExample;
import com.relle.mbg.model.AppCouponExample;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

@Configuration // 1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling // 2.开启定时任务
public class CouponScheduleTask {
    @Resource
    private AppCouponMapper mapper;
    @Resource
    private AppCouponCheckoutMapper couponCheckoutMapper;

    // 3.添加定时任务
    @Scheduled(cron = "0 0 0 * * ?")
    private void couponValidateTasks() {
        AppCouponCheckoutExample example = new AppCouponCheckoutExample();
        example.createCriteria()
                .andCouponStatusEqualTo((byte) 1);
        List<AppCouponCheckout> couponCheckoutList = couponCheckoutMapper.selectByExample(example);
        LocalDateTime now = LocalDateTime.now();
        for (AppCouponCheckout checkout : couponCheckoutList) {
            // LocalDateTime start = checkout.getCouponValidStarttime();
            LocalDateTime end = checkout.getCouponValidEndtime();
            if (now.isAfter(end)) {
                // 必须要根据当前优惠券状态来进行更新失效，防止有人在临界点进行使用此优惠券
                AppCouponCheckout update = new AppCouponCheckout();
                update.setCouponStatus(CouponStatusEnum.EXPIRE.getCode());
                AppCouponCheckoutExample query = new AppCouponCheckoutExample();
                query.createCriteria()
                        .andIdEqualTo(checkout.getId())
                        .andCouponStatusEqualTo(checkout.getCouponStatus());
                couponCheckoutMapper.updateByExampleSelective(update, query);
            }
        }

    }

}
