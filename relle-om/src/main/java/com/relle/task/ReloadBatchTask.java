package com.relle.task;


import com.relle.cache.batch.BatchCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class ReloadBatchTask {
    private static final Logger logger = LoggerFactory.getLogger(ReloadBatchTask.class);

    @Resource
    private BatchCache batchCache;

    //3.添加定时任务
    @Scheduled(cron = "1 0 0 * * ?")
    private void configureTasks() {
        batchCache.reload();
    }
}