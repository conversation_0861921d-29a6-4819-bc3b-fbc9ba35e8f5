package com.relle.task;


import com.relle.enums.CouponStatusEnum;
import com.relle.mbg.model.AppCouponCheckout;
import com.relle.service.IAppCouponCheckoutService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.Resource;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CloseCouponTask {
    private static final Logger logger = LoggerFactory.getLogger(CloseCouponTask.class);

    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;
    //3.添加定时任务
    @Scheduled(cron = "0 0 0 * * ?")
    private void configureTasks() {
        logger.info("_____执行卡券失效任务start_____");
        List<AppCouponCheckout> checkoutList = iAppCouponCheckoutService.getListByStatus(CouponStatusEnum.NO_USE.getCode());
        for (AppCouponCheckout checkout : checkoutList ) {
            if(LocalDateTime.now().isAfter(checkout.getCouponValidEndtime())){
                int update  = iAppCouponCheckoutService.invalidateCoupon(checkout.getId());
                if(update>0){
                    logger.info("_____{}已失效_____",checkout.getId());
                } else {
                    logger.info("_____{}失效失败_____",checkout.getId());
                }
            }
        }
        logger.info("_____执行卡券失效任务end_____");
    }
}