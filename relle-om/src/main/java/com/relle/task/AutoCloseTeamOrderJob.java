package com.relle.task;

import com.relle.enums.JobStatusEnum;
import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.mbg.mapper.SysTaskMapper;
import com.relle.mbg.model.SysTask;
import com.relle.mbg.model.SysTaskExample;
import com.relle.service.IAppServiceTeamOrderService;
import com.relle.service.IScheduleJobService;
import org.quartz.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DisallowConcurrentExecution //作业不并发
@Component
public class AutoCloseTeamOrderJob implements Job {

    @Resource
    private IAppServiceTeamOrderService iAppServiceTeamOrderService;
    @Resource
    private IScheduleJobService iScheduleJobService;
    @Resource
    private SysTaskMapper sysTaskMapper;

    @Override
    public void execute(JobExecutionContext arg0) throws JobExecutionException {
        JobKey jobKey = arg0.getJobDetail().getKey();
        JobDataMap jobDataMap = arg0.getMergedJobDataMap();
        String orderId = (String)jobDataMap.get("orderId");
        iAppServiceTeamOrderService.updateOrderStatus(orderId, ServiceOrderStatusEnum.CLOSED_BY_JOB.getCode(),"AutoCloseTeamOrderJob");

        // iScheduleJobService.deleteJob(jobKey);
        SysTask task = new SysTask();
        task.setJobStatus(JobStatusEnum.FINISHED.getCode());
        SysTaskExample example = new SysTaskExample();
        example.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
        sysTaskMapper.updateByExampleSelective(task,example);
    }

}