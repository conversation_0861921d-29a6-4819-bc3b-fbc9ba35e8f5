package com.relle.task;


import com.relle.enums.CouponShareStatusEnum;
import com.relle.mbg.model.AppCouponGive;
import com.relle.service.IAppCouponCheckoutService;
import com.relle.service.IAppCouponGiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class BackCouponTask {
    private static final Logger logger = LoggerFactory.getLogger(BackCouponTask.class);

    @Autowired
    private IAppCouponGiveService iAppCouponGiveService;
    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;
    //3.添加定时任务
    @Scheduled(cron = "0 0/1 * * * ?")
    private void configureTasks() {
        logger.info("_____执行退回卡券失效任务start_____");
        List<AppCouponGive> giveList = iAppCouponGiveService.getListByStatus(CouponShareStatusEnum.SHAREING.getCode());
        LocalDateTime now = LocalDateTime.now();
        for (AppCouponGive give : giveList ) {
            try {
                LocalDateTime giveTime = give.getGiveTime();
                if (now.isAfter(giveTime.plusDays(1))) {
                    int update = iAppCouponGiveService.back(give.getId());

                    if (update > 0) {
                        iAppCouponCheckoutService.backCoupon(give.getCouponNo(), give.getCouponId());
                        logger.info("_____{}已退回_____", give.getId());
                    } else {
                        logger.info("_____{}退回失败_____", give.getId());
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        logger.info("_____执行退回卡券失效任务end_____");
    }
}