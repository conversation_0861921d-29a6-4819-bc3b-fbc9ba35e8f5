package com.relle.task;

import com.relle.cache.batch.BatchCache;
import com.relle.dto.AppServiceOrderDTO;
import com.relle.mbg.mapper.SysTaskMapper;
import com.relle.mbg.model.AppServiceSuborder;
import com.relle.mbg.model.SysTask;
import com.relle.mbg.model.SysTaskExample;
import com.relle.service.IAppCouponCheckoutService;
import com.relle.service.IAppServiceOrderService;
import com.relle.service.IScheduleJobService;
import com.relle.enums.JobStatusEnum;
import com.relle.enums.ServiceOrderStatusEnum;
import org.quartz.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@DisallowConcurrentExecution //作业不并发
@Component
public class AutoCloseOrderJob implements Job {

    @Resource
    private IAppServiceOrderService iAppServiceOrderService;
    @Resource
    private IScheduleJobService iScheduleJobService;
    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;
    @Resource
    private SysTaskMapper sysTaskMapper;
    @Resource
    private BatchCache batchCache;

    @Override
    public void execute(JobExecutionContext arg0) throws JobExecutionException {
        JobKey jobKey = arg0.getJobDetail().getKey();
        JobDataMap jobDataMap = arg0.getMergedJobDataMap();
        String orderId = (String)jobDataMap.get("orderId");
        //更新订单状态
        iAppServiceOrderService.updateOrderStatus(orderId, ServiceOrderStatusEnum.CLOSED_BY_JOB.getCode(),"AutoCloseOrderJob");
        List<AppServiceSuborder> suborders = iAppServiceOrderService.getSubOrders(orderId);
        for (AppServiceSuborder suborder : suborders ) {
            batchCache.releaseRoomId(suborder.getStoreId(),suborder.getBookTime().toLocalDate(),suborder.getRoomId());
        }

        //退还卡券
        AppServiceOrderDTO orderDetailDTO = iAppServiceOrderService.getOrderDetailDTO(orderId);
        iAppCouponCheckoutService.backCoupon(orderDetailDTO.getOrder().getUnionid(),orderId);

        // iScheduleJobService.deleteJob(jobKey);
        SysTask task = new SysTask();
        task.setJobStatus(JobStatusEnum.FINISHED.getCode());
        SysTaskExample example = new SysTaskExample();
        example.createCriteria().andJobNameEqualTo(orderId).andJobGroupEqualTo("task-close");
        sysTaskMapper.updateByExampleSelective(task,example);
    }

}