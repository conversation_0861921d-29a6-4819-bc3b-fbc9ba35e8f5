package com.relle.task;

import com.alibaba.fastjson.JSONObject;
import com.relle.common.api.CommonResult;
import com.relle.common.utils.NumberGenerator;
import com.relle.dto.AppServiceTeamOrderDTO;
import com.relle.enums.JobStatusEnum;
import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.mbg.mapper.SysTaskMapper;
import com.relle.mbg.model.AppServiceTeamOrder;
import com.relle.mbg.model.SysTask;
import com.relle.mbg.model.SysTaskExample;
import com.relle.service.IAppServiceTeamOrderService;
import com.relle.service.IScheduleJobService;
import com.relle.service.IWxPayService;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@DisallowConcurrentExecution //作业不并发
@Component
public class AutoCloseTeamOrderFailedJob implements Job {

    @Resource
    private IAppServiceTeamOrderService iAppServiceTeamOrderService;
    @Resource
    private IScheduleJobService iScheduleJobService;
    @Resource
    private IWxPayService iWxPayService;
    @Resource
    private SysTaskMapper sysTaskMapper;

    @Override
    public void execute(JobExecutionContext arg0) throws JobExecutionException {
        JobKey jobKey = arg0.getJobDetail().getKey();
        JobDataMap jobDataMap = arg0.getMergedJobDataMap();
        String teamNo = (String)jobDataMap.get("orderId");
        String jobStatus = JobStatusEnum.WAITING.getCode();
        List<AppServiceTeamOrder> appServiceTeamOrders = iAppServiceTeamOrderService.getOrderListByTeamNo(teamNo);
        if(CollectionUtils.isEmpty(appServiceTeamOrders)){ //发起人最终没有付钱，导致组团失败
            jobStatus =  JobStatusEnum.CLOSED.getCode();
        } else {

            for (AppServiceTeamOrder teamOrder : appServiceTeamOrders ) {
                //已支付订单，进行退款
                if(teamOrder.getOrderStatus().shortValue() == ServiceOrderStatusEnum.PAY_SUCCESS.getCode()) {
                    refund(teamOrder.getOrderId(),(byte)4, teamOrder.getOrderAmount(),"组团失败");
                } else if(teamOrder.getOrderStatus().shortValue() == ServiceOrderStatusEnum.WAIT_PAY.getCode()){
                    close(teamOrder.getOrderId());
                }
            }
            jobStatus =  JobStatusEnum.FINISHED.getCode();
        }
        // iScheduleJobService.deleteJob(jobKey);
        SysTask task = new SysTask();
        task.setJobStatus(jobStatus);
        SysTaskExample example = new SysTaskExample();
        example.createCriteria().andJobNameEqualTo(teamNo).andJobGroupEqualTo("team-fail-close");
        sysTaskMapper.updateByExampleSelective(task,example);
    }

    private void refund(String orderId,Byte serviceItemCategory,Number refundAmount,String refundReason){
        if(iAppServiceTeamOrderService.checkOrder(orderId,"AutoCloseTeamOrderFailedJob")){
            boolean isClose = false;
            String storeId = orderId.substring(0,6);
            CommonResult result = iAppServiceTeamOrderService.getOrderDetail(orderId);
            AppServiceTeamOrderDTO dto = (AppServiceTeamOrderDTO)result.getData();
            AppServiceTeamOrder order = dto.getOrder();
            String refundOrderId = NumberGenerator.getRefundOrderId(order.getOrderId(),0);
            try {
                String response = iWxPayService.createRefundOrder(
                        storeId,
                        serviceItemCategory,
                        null,
                        orderId,
                        refundOrderId,
                        (int)(order.getOrderAmount().doubleValue()*100),
                        (int)(refundAmount.doubleValue()*100));
                System.out.println("退款结果***************"+response);
                JSONObject responseObj = JSONObject.parseObject(response);
                if("PROCESSING".equalsIgnoreCase(responseObj.getString("status")) || "SUCCESS".equalsIgnoreCase(responseObj.getString("status"))){
                    isClose = true;
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            if(isClose){
                iAppServiceTeamOrderService.refundOrder(orderId,"AutoCloseTeamOrderFailedJob",refundOrderId,refundAmount.doubleValue(),refundReason);
                return;
            } else {
                CommonResult.failed(3001,"退款失败");
                return;
            }
        } else {
            CommonResult.failed(3000,"不支持退款");
            return;
        }
    }

    private void close(String orderId){
        //确认订单支付状态，需要查询下
        iAppServiceTeamOrderService.updateOrderStatus(orderId,ServiceOrderStatusEnum.CLOSED_BY_JOB.getCode(),"AutoCloseTeamOrderFailedJob");
    }

    public static void main(String[] args) {
        String orderId = "SH000199999";
        String storeId = orderId.substring(0,6);
        System.out.println(storeId);
    }

}