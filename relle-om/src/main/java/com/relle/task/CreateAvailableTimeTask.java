package com.relle.task;


import com.relle.service.IAppAvailableTimeService;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;


@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class CreateAvailableTimeTask {
    private static final Logger logger = LoggerFactory.getLogger(CreateAvailableTimeTask.class);

    @Autowired
    private IAppAvailableTimeService iAppAvailableTimeService;
    //3.添加定时任务
    @Scheduled(cron = "0 0 1 1 * ?")//每月1日1点生成下个月的预约时间
    private void configureTasks() {
        logger.info("_____1日生成预约时间任务 start _____");
        LocalDate today = LocalDate.now();
        LocalDate nextMonth = today.plusMonths(1);
        String startDate = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String endDate = nextMonth.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        iAppAvailableTimeService.createAvailableTime(startDate,endDate);

        logger.info("_____1日生成预约时间任务 end _____");
    }
}