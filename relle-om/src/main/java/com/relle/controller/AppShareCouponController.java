package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.AppCouponShareDTO;
import com.relle.mbg.model.AppCouponCheckout;
import com.relle.mbg.model.AppCouponGive;
import com.relle.mbg.model.AppCustomerInfo;
import com.relle.service.IAppCouponCheckoutService;
import com.relle.service.IAppCouponGiveService;
import com.relle.service.IAppCustomerInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping(value = "/share")
public class AppShareCouponController {
    private static final Logger logger = LoggerFactory.getLogger(AppShareCouponController.class);

    @Autowired
    private IAppCouponGiveService iAppCouponGiveService;
    @Autowired
    private IAppCustomerInfoService iAppCustomerInfoService;
    @Autowired
    private IAppCouponCheckoutService iAppCouponCheckoutService;


    @RequestMapping(value = "/canShare/{couponId}")
    public CommonResult<?> canShare(@PathVariable Long couponId, HttpServletRequest request, HttpServletResponse response) {
        //1、获取分享用户信息
        //2、核实分享优惠券的信息,判断是否已经分享过
        //3、分享过，返回错误
        //4、未分享过，进行分享
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppCustomerInfo customerInfo = iAppCustomerInfoService.selectByUnionid(unionid);

        AppCouponCheckout checkout = iAppCouponCheckoutService.getById(couponId);
        boolean canShare = iAppCouponCheckoutService.canShare(checkout,customerInfo);
        if(!canShare){
            return CommonResult.failed(1000,"该优惠券不支持分享");
        }
        AppCouponGive give = iAppCouponGiveService.share(checkout,customerInfo);
        if(give == null || give.getId() == null){
            return CommonResult.failed(2000,"分享失败");
        }
        return CommonResult.succeeded(give);
    }

    @RequestMapping(value = "/confirmShare/{couponId}")
    public CommonResult<?> confirmShare(@PathVariable Long couponId, HttpServletRequest request, HttpServletResponse response) {
        //1、获取分享用户信息
        //2、更新分享用户的代金券
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppCustomerInfo customerInfo = iAppCustomerInfoService.selectByUnionid(unionid);

        AppCouponCheckout checkout = iAppCouponCheckoutService.getById(couponId);
        AppCouponGive give = iAppCouponGiveService.share(checkout,customerInfo);
        int updateNum = iAppCouponCheckoutService.confirmShare(checkout,customerInfo);
        if(updateNum==0){
            return CommonResult.failed(1000,"保存失败");
        }
        return CommonResult.succeeded(give);
    }


    @RequestMapping(value = "/openShare/{couponId}")
    public CommonResult<?> openShare(@PathVariable Long couponId, HttpServletRequest request, HttpServletResponse response) {
        //1、获取分享用户信息
        //2、更新分享用户的代金券
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppCustomerInfo customerInfo = iAppCustomerInfoService.selectByUnionid(unionid);

        AppCouponGive give = iAppCouponGiveService.getShareRecordByCouponNo(couponId);

        int status = iAppCouponGiveService.canReceive(give);
        if(status==0){
            return CommonResult.failed(give,"未找到分享记录");
        } else {
            AppCouponShareDTO shareDTO = iAppCouponGiveService.getShareRecordDetailById(give.getId());
            if(shareDTO==null){
                return CommonResult.failed(5000,"信息出错");
            }
            return CommonResult.succeeded(shareDTO);
        }
    }

    @RequestMapping(value = "/openShare2/{shareId}")
    public CommonResult<?> openShare2(@PathVariable Long shareId, HttpServletRequest request, HttpServletResponse response) {
        //1、获取分享用户信息
        //2、更新分享用户的代金券
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppCustomerInfo customerInfo = iAppCustomerInfoService.selectByUnionid(unionid);

        AppCouponGive give = iAppCouponGiveService.getShareRecordById(shareId);

        int status = iAppCouponGiveService.canReceive(give);
        if(status==0){
            return CommonResult.failed(give,"未找到分享记录");
        } else {
            AppCouponShareDTO shareDTO = iAppCouponGiveService.getShareRecordDetailById(give.getId());
            if(shareDTO==null){
                return CommonResult.failed(5000,"信息出错");
            }
            return CommonResult.succeeded(shareDTO);
        }
    }

    @RequestMapping(value = "/receiveCoupon/{couponId}")
    public CommonResult<?> receiveCoupon(@PathVariable Long couponId, HttpServletRequest request, HttpServletResponse response) {
        //1、获取分享用户信息
        //2、更新分享用户的代金券
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppCustomerInfo customerInfo = iAppCustomerInfoService.selectByUnionid(unionid);

        AppCouponGive give = iAppCouponGiveService.getShareRecordByCouponNo(couponId);

        int status = iAppCouponGiveService.canReceive(give);
        if(status==0){
            return CommonResult.failed(1000,"未找到分享记录");
        } else if(status==1){
            return CommonResult.failed(2000,"该券已领取");
        } else if(status==2){
            return CommonResult.failed(3000,"该券已失效");
        }
        int update = iAppCouponGiveService.receive(give.getId(),customerInfo);
        if(update==1) {
            int i = iAppCouponCheckoutService.sharedCouponById(give.getCouponNo(),unionid);
            if(i==1){
                return CommonResult.succeeded("");
            }
        }
        return CommonResult.failed(500,"领取失败");
    }

    @RequestMapping(value = "/receiveCoupon2/{shareId}")
    public CommonResult<?> receiveCoupon2(@PathVariable Long shareId, HttpServletRequest request, HttpServletResponse response) {
        //1、获取分享用户信息
        //2、更新分享用户的代金券
        String unionid = (String)request.getSession().getAttribute("unionid");
        AppCustomerInfo customerInfo = iAppCustomerInfoService.selectByUnionid(unionid);

        AppCouponGive give = iAppCouponGiveService.getShareRecordById(shareId);

        int status = iAppCouponGiveService.canReceive(give);
        if(status==0){
            return CommonResult.failed(1000,"未找到分享记录");
        } else if(status==1){
            return CommonResult.failed(2000,"该券已领取");
        } else if(status==2){
            return CommonResult.failed(3000,"该券已失效");
        }
        int update = iAppCouponGiveService.receive(give.getId(),customerInfo);
        if(update==1) {
            int i = iAppCouponCheckoutService.sharedCouponById(give.getCouponNo(),unionid);
            if(i==1){
                return CommonResult.succeeded("");
            }
        }
        return CommonResult.failed(500,"领取失败");
    }

    @RequestMapping(value = "/myShareList")
    public CommonResult<?> myShareList(HttpServletRequest request, HttpServletResponse response) {

        String unionid = (String)request.getSession().getAttribute("unionid");
        List<AppCouponGive> shareRecord = iAppCouponGiveService.getShareRecord(unionid);

        return CommonResult.succeeded(shareRecord);
    }

}
