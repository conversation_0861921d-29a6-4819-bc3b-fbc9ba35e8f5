package com.relle.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.common.api.CommonResult;
import com.relle.dto.AppCouponDTO;
import com.relle.enums.ActivityCategoryEnum;
import com.relle.mbg.model.*;
import com.relle.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "活动接口")
@RestController
@RequestMapping(value = "/activity")
@CrossOrigin(origins = "*", allowCredentials = "true")
public class AppActivityController {
    private static final Logger logger = LoggerFactory.getLogger(AppActivityController.class);

    @Resource
    private IAppActivityService iAppActivityService;
    @Resource
    private IAppCouponService iAppCouponService;

    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;

    @Resource
    private ICouponRuleService iCouponRuleService;

    @Resource
    private IAppActivityQrcodeService iAppActivityQrcodeService;
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;

    @Resource
    private ObjectMapper objectMapper;

    @RequestMapping(value = "/list/{storeId}")
    public CommonResult<?> list(@PathVariable String storeId, HttpServletRequest request,
            HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        List<AppActivity> activityList = iAppActivityService.getActivityList(storeId);
        List<ObjectNode> array = new ArrayList<>();
        for (AppActivity activity : activityList) {
            ObjectNode obj = objectMapper.valueToTree(activity);

            if (activity.getActivityCategory().byteValue() == ActivityCategoryEnum.RECRUITNEW.getCode()
                    && iCouponRuleService.isNew(unionid)) {
                continue;
            }
            if (activity.getActivityCategory().byteValue() == ActivityCategoryEnum.TEAM.getCode()) {
                obj.put("serviceItemId", "TM20230328233232001");
            }

            array.add(obj);

        }
        return CommonResult.succeeded(array);
    }

    @RequestMapping(value = "/getCouponByActivity/{activityId}")
    public CommonResult<?> getCouponByActivity(@PathVariable Long activityId, HttpServletRequest request,
            HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        return iAppActivityService.getCouponByActivity(activityId, unionid);
    }

    @RequestMapping(value = "/getCouponList")
    public CommonResult<?> getCouponList(HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        String storeId = request.getParameter("storeId");
        List<AppCouponDTO> myCoupons = iAppCouponService.getMyCoupon(unionid);
        List<AppCouponDTO> dtoCoupons = myCoupons.stream()
                .filter(a -> (a.getStoreIds() == null || a.getStoreIds().indexOf(storeId) > -1))
                .collect(Collectors.toList());
        return CommonResult.succeeded(dtoCoupons);
    }

    @RequestMapping(value = "/getValidCouponList/{serviceId}")
    public CommonResult<?> getCouponList(@PathVariable String serviceId, HttpServletRequest request,
            HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        return CommonResult.succeeded(iAppCouponService.getMyCoupon(unionid, serviceId));
    }

    @RequestMapping(value = "/checkUserIsNew")
    public CommonResult<?> checkUserIsNew(HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        return CommonResult.succeeded(new HashMap() {
            {
                put("isNew", iCouponRuleService.isNew(unionid));
            }
        });
    }

    @RequestMapping(value = "/getCoupon")
    public CommonResult<?> getCoupon(HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        String activityId = request.getParameter("activityId");
        if (StringUtils.isEmpty(activityId)) {
            activityId = "1";
        }
        return iAppActivityService.receiveCouponByActivity(Long.valueOf(activityId), unionid);
    }

    @RequestMapping(value = "/hasCoupon")
    public CommonResult<?> hasCoupon(HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        String couponId = request.getParameter("couponId");
        if (StringUtils.isEmpty(couponId)) {
            couponId = "NC202212050001";
        }
        return CommonResult.succeeded(iAppCouponCheckoutService.hasReceived(couponId, unionid));
    }

    @RequestMapping(value = "/getActivity/{storeId}/{activityId}")
    public CommonResult<?> getActivity(@PathVariable String storeId, @PathVariable String activityId,
            HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        // Get activity info
        AppActivity appActivity = iAppActivityService.getActivity(storeId, activityId);
        if (appActivity == null || appActivity.getStatus().byteValue() == 0
                || appActivity.getDeleted().byteValue() == 1) {
            return CommonResult.failed("活动已下线");
        } else if (appActivity.getStartTime().isAfter(LocalDateTime.now())) {
            return CommonResult.failed("活动暂未开始");
        } else if (appActivity.getEndTime().isBefore(LocalDateTime.now())) {
            return CommonResult.failed("活动已结束");
        }

        // Convert activity to ObjectNode
        ObjectNode obj = objectMapper.valueToTree(appActivity);

        // Get coupon requests
        List<AppActivityCoupon> activityCoupons = iCouponRuleService.getActivityCoupon(appActivity.getId());
        AppActivityCoupon appActivityCoupon = activityCoupons.get(0);
        AppCoupon couponById = iAppCouponService.getCouponById(appActivityCoupon.getCouponId());
        List<AppCouponCheckout> listByCouponId = iAppCouponCheckoutService.getListByCouponId(
                storeId,
                couponById.getCouponId(),
                appActivity.getStartTime(),
                appActivity.getEndTime());

        // Add received count
        obj.put("hasReceived", listByCouponId.isEmpty() ? 0 : listByCouponId.size());

        // Get QR code status
        List<AppActivityQrcode> qrcodeByStatus = iAppActivityQrcodeService.getQrcodeByStatus(
                storeId,
                appActivity.getActivityId(),
                (byte) 0);
        if (qrcodeByStatus.isEmpty()) {
            return CommonResult.failed("发放名额已满");
        }

        // Add QR code and return result
        obj.putPOJO("qrcode", qrcodeByStatus.get(0));
        return CommonResult.succeeded(obj);
    }

    @ApiOperation(value = "获取领券记录")
    @GetMapping(value = "/getReceiveRecord/{storeId}/{activityId}")
    public CommonResult<?> getReceiveRecord(@PathVariable String storeId, @PathVariable String activityId,
            HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        // 获取活动信息
        AppActivity appActivity = iAppActivityService.getActivity(storeId, activityId);

        // 获取领券请求
        List<AppActivityCoupon> activityCoupons = iCouponRuleService.getActivityCoupon(appActivity.getId());
        AppActivityCoupon appActivityCoupon = activityCoupons.get(0);
        AppCoupon couponById = iAppCouponService.getCouponById(appActivityCoupon.getCouponId());
        List<AppCouponCheckout> listByCouponId = iAppCouponCheckoutService.getListByCouponId(storeId,
                couponById.getCouponId(), appActivity.getStartTime(), appActivity.getEndTime());
        ArrayNode arrayNode = objectMapper.createArrayNode();

        for (AppCouponCheckout checkout : listByCouponId) {
            ObjectNode checkoutNode = objectMapper.valueToTree(checkout);

            // Get and add customer info
            AppCustomerInfo customer = iAppCustomerInfoService.selectByUnionid(checkout.getReceiveCustomerUnionid());
            checkoutNode.putPOJO("customer", customer);

            arrayNode.add(checkoutNode);
        }

        return CommonResult.succeeded(arrayNode);
    }

    @ApiOperation(value = "撤销领券记录")
    @PostMapping(value = "/cancelReceiveRecord/{storeId}/{activityId}/{checkoutId}")
    public CommonResult<?> cancelReceiveRecord(@PathVariable String storeId, @PathVariable String activityId,
            @PathVariable Long checkoutId, HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        // 获取活动信息
        AppActivity appActivity = iAppActivityService.getActivity(storeId, activityId);
        // 获取领券请求
        List<AppActivityCoupon> activityCoupons = iCouponRuleService.getActivityCoupon(appActivity.getId());
        AppActivityCoupon appActivityCoupon = activityCoupons.get(0);
        AppCoupon couponById = iAppCouponService.getCouponById(appActivityCoupon.getCouponId());
        AppCouponCheckout checkoutById = iAppCouponCheckoutService.getById(checkoutId);
        if (!checkoutById.getCouponId().equals(couponById.getCouponId())) {
            return CommonResult.failed("券与活动的信息不符");
        }
        if (!StringUtils.isEmpty(checkoutById.getUseCouponOrderid())) {
            return CommonResult.failed("该券已使用，无法操作");
        }
        iAppCouponCheckoutService.deleteCoupon(checkoutId);
        iAppActivityQrcodeService.updateQrcodeStatus(storeId, activityId, checkoutById.getReceiveCustomerUnionid(),
                (byte) 0);
        return CommonResult.succeeded("");
    }

    @ApiOperation(value = "获取领券活动列表")
    @GetMapping(value = "/getSpecificActivity/{storeId}/{activityCategory}")
    public CommonResult<?> getSpecificActivity(@PathVariable String storeId, @PathVariable Integer activityCategory,
            HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        // 获取活动信息
        List<AppActivity> activityListByCategory = iAppActivityService.getActivityListByCategory(storeId,
                activityCategory);

        return CommonResult.succeeded(activityListByCategory);
    }

    @RequestMapping(value = "/getQrcodeStatus/{storeId}/{activityId}/{qrcodeParam}")
    public CommonResult<?> getQrcodeStatus(@PathVariable String storeId, @PathVariable String activityId,
            @PathVariable String qrcodeParam, HttpServletRequest request, HttpServletResponse response) {
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");

        // 获取活动信息
        AppActivity appActivity = iAppActivityService.getActivity(storeId, activityId);
        if (appActivity == null || appActivity.getStatus().byteValue() == 0
                || appActivity.getDeleted().byteValue() == 1) {
            return CommonResult.failed("活动已下线");
        } else if (appActivity.getStartTime().isAfter(LocalDateTime.now())) {
            return CommonResult.failed("活动暂未开始");
        } else if (appActivity.getEndTime().isBefore(LocalDateTime.now())) {
            return CommonResult.failed("活动已结束");
        }
        AppActivityQrcode qrcodeByQrcodeParams = iAppActivityQrcodeService.getQrcodeByQrcodeParams(storeId, activityId,
                qrcodeParam);

        return CommonResult.succeeded(qrcodeByQrcodeParams);
    }

}
