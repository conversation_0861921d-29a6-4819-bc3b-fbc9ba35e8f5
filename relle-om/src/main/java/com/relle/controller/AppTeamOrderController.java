package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping(value = "/teamOrder")
public class AppTeamOrderController {
    private static final Logger logger = LoggerFactory.getLogger(AppTeamOrderController.class);

    @Autowired
    private IAppServiceTeamOrderService iAppServiceTeamOrderService;
    @Autowired
    private IAppCustomerInfoService iAppCustomerInfoService;
    @Autowired
    private ICouponRuleService iCouponRuleService;


    @RequestMapping(value = "/get/{teamNo}")
    public CommonResult<?> get(@PathVariable String teamNo, HttpServletRequest request, HttpServletResponse response) {
        //1、获取用户信息
        //2、判断用户是新用户还是老用户，老用户重新跳转发起分享页面
        //以下是新用户发起流程
        //3、获取团购订单的信息，供页面端显示
        String unionid = (String)request.getSession().getAttribute("unionid");

        return iAppServiceTeamOrderService.getOrderListByTeamNo(teamNo,unionid);
    }


}
