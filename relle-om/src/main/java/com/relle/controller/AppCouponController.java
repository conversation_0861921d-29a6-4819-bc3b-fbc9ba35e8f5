package com.relle.controller;

import com.alibaba.fastjson.JSONObject;
import com.relle.common.api.CommonResult;
import com.relle.dto.AppCouponDTO;
import com.relle.enums.CouponCheckoutSourceEnum;
import com.relle.enums.CouponStatusEnum;
import com.relle.mbg.model.*;
import com.relle.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "优惠券接口")
@RestController
@RequestMapping(value = "/coupon")
public class AppCouponController {
    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;
    @Resource
    private IAppCouponService iAppCouponService;

    @Resource
    private IAppActivityQrcodeService iAppActivityQrcodeService;
    @Resource
    private IAppActivityService iAppActivityService;
    @Resource
    private ICouponRuleService iCouponRuleService;

    @ApiOperation(value = "发放卡券")
    @PostMapping(value = "/distribute/{couponId}/{unionid}")
    public CommonResult<?> distribute(@PathVariable String couponId, @PathVariable String unionid,HttpServletRequest request, HttpServletResponse response) {
        //获取用户信息
        String sunionid = (String)request.getSession().getAttribute("unionid");
        String sopenid = (String)request.getSession().getAttribute("openid");
        String storeIds = request.getParameter("storeIds");

        //校验
        if(unionid.equals(sunionid)) {
            AppCouponCheckout checkout = iAppCouponCheckoutService.grant(storeIds,couponId, unionid,
                    1, CouponStatusEnum.NO_USE.getCode(),
                    CouponCheckoutSourceEnum.MT.getCode(), "");
            JSONObject ret = new JSONObject();
            ret.put("coupon",iAppCouponService.getCoupon(couponId));
            ret.put("couponCheckout",checkout);
            return CommonResult.succeeded(ret);
        }
        return CommonResult.failed(1000,"非法发券");
    }

    @ApiOperation(value = "发放卡券")
    @PostMapping(value = "/distribute/{storeId}/{activityId}/{qrcodeParam}")
    public CommonResult<?> distributeByActivityId(@PathVariable String storeId,@PathVariable String activityId,@PathVariable String qrcodeParam,HttpServletRequest request, HttpServletResponse response) {
        //获取用户信息
        String sunionid = (String)request.getSession().getAttribute("unionid");
        String sopenid = (String)request.getSession().getAttribute("openid");
        if(StringUtils.isEmpty(qrcodeParam)){
            return CommonResult.failed(1000,"非法领券");
        }
        //判断此二维码是否已使用
        AppActivityQrcode qrcodeByQrcodeParams = iAppActivityQrcodeService.getQrcodeByQrcodeParams(storeId,activityId, qrcodeParam);
        if(qrcodeByQrcodeParams==null){
            return CommonResult.failed(2000,"非法领券");
        }
        if(qrcodeByQrcodeParams.getQrcodeStatus().byteValue() == 1){
            return CommonResult.failed(3000,"此码已使用过了");
        }
        Calendar now = Calendar.getInstance();
        AppActivity appActivity = iAppActivityService.getActivity(storeId,activityId);
        if(appActivity == null || appActivity.getStatus().byteValue() == 0 || appActivity.getDeleted().byteValue() == 1){
            return CommonResult.failed("活动已下线");
        } else if(appActivity.getStartTime().isAfter(LocalDateTime.now())){
            return CommonResult.failed("活动暂未开始");
        } else if(appActivity.getEndTime().isBefore(LocalDateTime.now())){
            return CommonResult.failed("活动已结束");
        }
        List<AppActivityCoupon> activityCoupons = iCouponRuleService.getActivityCoupon(appActivity.getId());
        AppActivityCoupon appActivityCoupon = activityCoupons.get(0);
        AppCoupon couponById = iAppCouponService.getCouponById(appActivityCoupon.getCouponId());
        //校验
        AppCouponCheckout checkout = iAppCouponCheckoutService.grant(storeId,couponById.getCouponId(), sunionid,
                1, CouponStatusEnum.NO_USE.getCode(),
                CouponCheckoutSourceEnum.ACTIVITY_QRCODE.getCode(), "");

        qrcodeByQrcodeParams.setQrcodeStatus((byte)1);
        qrcodeByQrcodeParams.setUpdateTime(new Date());
        qrcodeByQrcodeParams.setUpdateBy(sunionid);
        iAppActivityQrcodeService.update(storeId,activityId,qrcodeByQrcodeParams);

        JSONObject ret = new JSONObject();
        ret.put("coupon",couponById);
        ret.put("couponCheckout",checkout);
        return CommonResult.succeeded(ret);

    }

    @ApiOperation(value = "获取优惠券详情（必须是个人已领，且有效）")
    @GetMapping(value = "/getCouponDetail/{couponId}")
    public CommonResult<?> getCouponDetail(@PathVariable String couponId,HttpServletRequest request, HttpServletResponse response) {
        //获取用户信息
        String sunionid = (String)request.getSession().getAttribute("unionid");
        String sopenid = (String)request.getSession().getAttribute("openid");

        List<AppCouponDTO> myCoupons = iAppCouponService.getMyCoupon(sunionid);
        List<AppCouponDTO> dtoCoupons = myCoupons.stream() .filter(a -> a.getCouponId().equals(couponId)) .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dtoCoupons)){
            return CommonResult.succeeded(null,"未找到符合要求的券");
        }
        return CommonResult.succeeded(dtoCoupons.get(0));
    }

    @ApiOperation(value = "获取优惠券详情（必须是个人已领，且有效）")
    @GetMapping(value = "/getCouponDetailById/{id}")
    public CommonResult<?> getCouponDetail(@PathVariable Long id,HttpServletRequest request, HttpServletResponse response) {
        //获取用户信息
        String sunionid = (String)request.getSession().getAttribute("unionid");
        String sopenid = (String)request.getSession().getAttribute("openid");

        List<AppCouponDTO> myCoupons = iAppCouponService.getMyCoupon(sunionid);
        List<AppCouponDTO> dtoCoupons = myCoupons.stream() .filter(a -> a.getId().longValue()==id.longValue()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dtoCoupons)){
            return CommonResult.succeeded(null,"未找到符合要求的券");
        }
        return CommonResult.succeeded(dtoCoupons.get(0));
    }
}
