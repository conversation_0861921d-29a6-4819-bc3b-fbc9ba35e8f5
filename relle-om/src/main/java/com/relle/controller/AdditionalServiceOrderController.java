package com.relle.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.common.api.CommonResult;
import com.relle.common.utils.NumberGenerator;
import com.relle.dto.AdditionalOrderConfirmRO;
import com.relle.dto.AdditionalOrderRO;
import com.relle.dto.AdditionalOrderVO;
import com.relle.dto.AdditionalSuborderDTO;
import com.relle.enums.ServiceOrderStatusEnum;
import com.relle.enums.ServiceItemMediaTypeEnum;
import com.relle.mbg.model.*;
import com.relle.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Random;

@Api(tags = "加单处理接口")
@RestController
@RequestMapping(value = "/additionalOrder")
public class AdditionalServiceOrderController {
    private static final Logger logger = LoggerFactory.getLogger(AdditionalServiceOrderController.class);
    @Resource
    private IAppServiceSuborderAdditionalService iAppServiceSuborderAdditionalService;
    @Resource
    private IWxPayService iWxPayService;
    @Resource
    private IAppServiceOrderService iAppServiceOrderService;
    @Resource
    private IAppServiceSuborderService iAppServiceSuborderService;
    @Resource
    private IWXInterfaceService iwxInterfaceService;
    @Resource
    private IAppOrderPayService iAppOrderPayService;
    @Resource
    private IAppServiceItemService iAppServiceItemService;
    @Resource
    private IAppCouponCheckoutService iAppCouponCheckoutService;
    @Resource
    private IAppCouponService iAppCouponService;
    @Resource
    private IAppStoreInfoService iAppStoreInfoService;
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;
    @Resource
    private IAppServiceOperationRecordService iAppServiceOperationRecordService;

    @Value("${relle.miniprogram.version}")
    private String miniprogramVersion;

    @ApiOperation(value = "建单")
    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody AdditionalOrderRO ro, HttpServletRequest request,
            HttpServletResponse response) {
        // 1、处理信息
        // 2、获取service
        // 3、校验保存
        // 4、调取支付
        // 获取用户信息
        String unionid = (String) request.getSession().getAttribute("unionid");
        // String openid = (String)request.getSession().getAttribute("openid");
        // 取出门店信息和服务产品信息，生成订单编号
        String storeId = ro.getStoreId();
        String serviceItemId = ro.getServiceItemId();
        Number serviceItemAmount = ro.getServiceItemAmount();
        Number serviceItemOriginAmount = ro.getServiceItemOriginAmount();
        Number serviceItemReductionAmount = ro.getServiceItemReductionAmount();
        String orderId = ro.getOrderId();

        AppServiceSuborder suborder = iAppServiceSuborderService.getSuborderBySuborderId(orderId);
        if (suborder == null) {
            return CommonResult.failed("请检查服务订单");
        }
        AppServiceOrder order = iAppServiceOrderService.getOrder(suborder.getOrderId());
        if (order == null) {
            return CommonResult.failed("请检查服务订单");
        }
        AppServiceSuborderAdditional suborderAdditional = new AppServiceSuborderAdditional();
        suborderAdditional.setOrderId(orderId);
        suborderAdditional.setSuborderId(orderId + getRandomCharStr(2));

        suborderAdditional.setServiceItemId(serviceItemId);
        suborderAdditional.setSuborderAmount(new BigDecimal(serviceItemAmount.doubleValue()));
        suborderAdditional.setSuborderOriginPrice(new BigDecimal(serviceItemOriginAmount.doubleValue()));
        suborderAdditional.setSuborderReductionAmount(new BigDecimal(serviceItemReductionAmount.doubleValue()));
        suborderAdditional.setSuborderStatus(ServiceOrderStatusEnum.WAIT_PAY.getCode());
        suborderAdditional.setCreateBy(unionid);
        suborderAdditional.setUpdateBy(unionid);
        suborderAdditional.setPayUrl(iwxInterfaceService.getURLLink(ro.getPathOfQrcode(), miniprogramVersion,
                "o=" + suborderAdditional.getSuborderId()));

        suborderAdditional.setBookTime(suborder.getBookTime());
        suborderAdditional.setRoomId(suborder.getRoomId());
        suborderAdditional.setUnionid(suborder.getUnionid());
        suborderAdditional.setStoreId(storeId);
        iAppServiceSuborderAdditionalService.save(suborderAdditional);
        AppCustomerInfo customerInfo = new AppCustomerInfo();
        customerInfo.setWechatNickname(order.getContactName());
        customerInfo.setWechatPhone(order.getContactPhone());

        AdditionalSuborderDTO dto1 = new AdditionalSuborderDTO();
        dto1.setAdditionalSuborder(suborderAdditional);
        dto1.setServiceItem(iAppServiceItemService.getItem(suborderAdditional.getServiceItemId()));
        dto1.setThumbnail(iAppServiceItemService.getOneServiceMediaRelation(suborderAdditional.getServiceItemId(),
                ServiceItemMediaTypeEnum.THUMBNAIL.getCode()));
        dto1.setCustomerInfo(customerInfo);

        return CommonResult.succeeded(dto1);
    }

    public static String getRandomCharStr(int n) {
        String codes = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0; i < n; i++) {
            randomStr.append(codes.charAt(random.nextInt(62)));
        }
        return randomStr.toString();
    }

    @PostMapping("/notifyUrl/{storeId}/{serviceItemCategory}")
    public String notifyUrl(@PathVariable String storeId, @PathVariable Byte serviceItemCategory,
            HttpServletRequest request) {
        logger.info("------------------------notifyUrl----------------------------");
        System.out.println("Wechatpay-Timestamp:" + request.getHeader("Wechatpay-Timestamp"));
        System.out.println("Wechatpay-Nonce:" + request.getHeader("Wechatpay-Nonce"));
        System.out.println("Wechatpay-Signature:" + request.getHeader("Wechatpay-Signature"));
        System.out.println("Wechatpay-Serial:" + request.getHeader("Wechatpay-Serial"));
        logger.info("Wechatpay-Serial:" + request.getHeader("Wechatpay-Serial"));

        JsonNode result = iWxPayService.decryptNotifyUrlBody(storeId, serviceItemCategory, request);
        System.out.println(result);
        // 解析验签失败，直接返回错误信息
        if ("FAIL".equals(result.get("code").asText())) {
            return result.toString();
        }
        JsonNode goodBody = result.get("goodBody");
        int paySuccess = iAppServiceSuborderAdditionalService.paySuccess(goodBody.get("out_trade_no").asText(),
                goodBody.get("success_time").asText(), goodBody.get("transaction_id").asText());
        logger.info("支付通知HTTP应答：" + result.toString());
        ((ObjectNode) result).remove("goodBody");
        return result.toString();
    }

    @ApiOperation(value = "取消订单，仅限未支付的订单")
    @PostMapping(value = "/cancelOrder/{suborderId}")
    public CommonResult<?> cancelOrder(@PathVariable String suborderId, HttpServletRequest request) {
        String unionid_from_session = (String) request.getSession().getAttribute("unionid");
        // 核实订单信息，确认是否确认是未支付订单
        AppServiceSuborderAdditional suborderAdditional = iAppServiceSuborderAdditionalService
                .getOneBySuborderId(suborderId);
        if (!iAppServiceSuborderAdditionalService.checkOrderCanCancel(suborderAdditional)) {
            return CommonResult.failed("仅支持未支付的订单");
        }
        suborderAdditional.setDeleted((byte) 1);
        suborderAdditional.setUpdateBy(unionid_from_session);
        suborderAdditional.setUpdateTime(LocalDateTime.now());
        return CommonResult.succeeded(iAppServiceSuborderAdditionalService.update(suborderAdditional));
    }

    @ApiOperation(value = "获取所有加单")
    @GetMapping(value = "/list/{orderId}")
    public CommonResult<?> list(@PathVariable String orderId, HttpServletRequest request) {
        // String unionid_from_session =
        // (String)request.getSession().getAttribute("unionid");
        return CommonResult.succeeded(iAppServiceSuborderAdditionalService.getListByOrderId(orderId));
    }

    @ApiOperation(value = "获取订单信息，用于用户扫码后，显示订单明细")
    @GetMapping(value = "/get/{suborderId}")
    public CommonResult<?> get(@PathVariable String suborderId, HttpServletRequest request) {
        AdditionalOrderVO vo = new AdditionalOrderVO();
        AppServiceSuborderAdditional suborderAdditional = iAppServiceSuborderAdditionalService
                .getOneBySuborderId(suborderId);
        vo.setSuborderAdditional(suborderAdditional);
        vo.setServiceItem(iAppServiceItemService.getItem(suborderAdditional.getServiceItemId()));
        vo.setThumbnail(iAppServiceItemService.getOneServiceMediaRelation(suborderAdditional.getServiceItemId(),
                ServiceItemMediaTypeEnum.THUMBNAIL.getCode()));
        vo.setStore(iAppStoreInfoService.getStoreInfoByStoreId(suborderAdditional.getStoreId()));

        AppServiceSuborder suborder = iAppServiceSuborderService
                .getSuborderBySuborderId(suborderAdditional.getOrderId());
        if (suborder == null) {
            return CommonResult.failed("请检查服务订单");
        }
        AppServiceOrder order = iAppServiceOrderService.getOrder(suborder.getOrderId());
        if (order == null) {
            return CommonResult.failed("请检查服务订单");
        }
        AppCustomerInfo customerInfo = new AppCustomerInfo();
        customerInfo.setWechatNickname(order.getContactName());
        customerInfo.setWechatPhone(order.getContactPhone());
        vo.setCustomerInfo(customerInfo);
        return CommonResult.succeeded(vo);
    }

    @ApiOperation(value = "确认支付")
    @PostMapping(value = "/confirm")
    @Transactional
    public CommonResult<?> confirm(@RequestBody AdditionalOrderConfirmRO ro, HttpServletRequest request) {

        String unionid = (String) request.getSession().getAttribute("unionid");
        String openid = (String) request.getSession().getAttribute("openid");

        AppServiceSuborderAdditional oneBySuborderId = iAppServiceSuborderAdditionalService
                .getOneBySuborderId(ro.getSuborderId());
        if (oneBySuborderId == null) {
            return CommonResult.failed("未找到您要支付的订单，请核实！");
        }
        if (oneBySuborderId.getSuborderStatus() != ServiceOrderStatusEnum.WAIT_PAY.getCode()) {
            return CommonResult.failed("请确认订单状态");
        }
        Number couponId = ro.getCouponId();
        if (couponId != null) {
            // 先确认优惠券状态
            AppCouponCheckout couponCheckout = iAppCouponCheckoutService.getById(couponId.longValue());
            if (!iAppCouponCheckoutService.checkCouponValid(couponCheckout)) { // 优惠券非正常状态
                return CommonResult.failed(1000, "优惠券已失效");
            }
            // 核实订单

            // 更新订单
            iAppCouponService.useCoupon(couponId.longValue(), oneBySuborderId.getSuborderId(), unionid);
            oneBySuborderId.setSuborderOriginPrice(new BigDecimal(ro.getServiceItemOriginAmount().doubleValue()));
            oneBySuborderId
                    .setSuborderReductionAmount(new BigDecimal(ro.getServiceItemReductionAmount().doubleValue()));
            oneBySuborderId.setSuborderAmount(new BigDecimal(ro.getServiceItemAmount().doubleValue()));
            iAppServiceSuborderAdditionalService.update(oneBySuborderId);
        }

        // 确认是否已经预下单过
        AppOrderPay pay = iAppOrderPayService.getPay(ro.getSuborderId());
        if (pay != null) {
            return CommonResult.succeeded(pay);
        }
        // 若没有，预下单,返回支付参数
        String storeId = oneBySuborderId.getStoreId();
        Byte serviceItemCategory = 1;
        String serviceItemId = oneBySuborderId.getServiceItemId();
        BigDecimal serviceItemAmount = oneBySuborderId.getSuborderAmount();
        AppOrderPay prepay = iWxPayService.createPrepay(storeId, serviceItemCategory, serviceItemId,
                (int) (serviceItemAmount.doubleValue() * 100), oneBySuborderId.getSuborderId(), openid);
        // Byte serviceItemCategory =
        // iWxPayService.toPay(oneBySuborderId.getStoreId(),serviceItemCategory)
        return CommonResult.succeeded(prepay);
    }

    /*
     * @ApiOperation(value = "退单（用户）")
     * 
     * @PostMapping(value = "/refundOrder/{orderId}")
     * public CommonResult<?> refundOrder(@PathVariable String orderId, @RequestBody
     * Map<String, Object> orderMap, HttpServletRequest request) {
     * String unionid_from_session =
     * (String)request.getSession().getAttribute("unionid");
     * Number refundAmount = (Number)orderMap.get("refundAmount");
     * String refundReason = (String)orderMap.get("refundReason");
     * System.out.println("退款金额*****************"+refundAmount);
     * 
     * String storeId = orderId.substring(0,6);
     * String serviceItemId =
     * iAppServiceSuborderAdditionalService.getServiceItemIdFromOrderId(orderId);
     * //获取产品信息
     * AppServiceItemVO itemVO =
     * iAppServiceItemService.getItemDetail(storeId,serviceItemId);
     * Byte serviceItemCategory = itemVO.getServiceItemCategory();
     * //根据产品获取不同的service
     * IOrderService service =
     * iAppServiceOrderServiceFactory.getService(serviceItemCategory);
     * 
     * if(service.checkOrder(orderId,unionid_from_session)){
     * boolean isClose = false;
     * CommonResult result = service.getOrderDetail(orderId);
     * 
     * Number orderAmount = 0;
     * if(result.getData() instanceof AppServiceOrderDTO ){
     * AppServiceOrderDTO dto = (AppServiceOrderDTO)result.getData();
     * AppServiceOrder order = dto.getOrder();
     * orderAmount = order.getOrderAmount();
     * } else if(result.getData() instanceof AppServiceBundleOrderDTO ){
     * AppServiceBundleOrderDTO dto = (AppServiceBundleOrderDTO)result.getData();
     * AppServiceBundleOrder order = dto.getOrder();
     * orderAmount = order.getOrderAmount();
     * } else if(result.getData() instanceof AppServiceTeamOrderDTO){
     * AppServiceTeamOrderDTO dto = (AppServiceTeamOrderDTO)result.getData();
     * AppServiceTeamOrder order = dto.getOrder();
     * orderAmount = order.getOrderAmount();
     * } else {
     * return CommonResult.failed(9000,"这类订单暂不支持退款");
     * }
     * String refundOrderId = NumberGenerator.getRefundOrderId(orderId, 0);
     * if(orderAmount.doubleValue()==0d){
     * isClose = true;
     * } else {
     * 
     * try {
     * String response = iWxPayService.createRefundOrder(
     * storeId,
     * serviceItemCategory,
     * null,
     * orderId,
     * refundOrderId,
     * (int) (orderAmount.doubleValue() * 100),
     * (int) (refundAmount.doubleValue() * 100));
     * System.out.println("退款结果***************" + response);
     * JSONObject responseObj = JSONObject.parseObject(response);
     * if ("PROCESSING".equalsIgnoreCase(responseObj.getString("status")) ||
     * "SUCCESS".equalsIgnoreCase(responseObj.getString("status"))) {
     * isClose = true;
     * }
     * } catch (Exception e) {
     * e.printStackTrace();
     * }
     * }
     * if(isClose){
     * return
     * service.refundOrder(orderId,unionid_from_session,refundOrderId,refundAmount.
     * doubleValue(),refundReason);
     * } else {
     * return CommonResult.failed(3001,"退款失败");
     * }
     * } else {
     * return CommonResult.failed(3000,"不支持退款");
     * }
     * }
     */

    @ApiOperation(value = "测试退款")
    @PostMapping(value = "/rrr/{orderId}")
    public CommonResult<?> rrr(@PathVariable String orderId, HttpServletRequest request) throws Exception {
        // String orderId = "SH0001231201091513845017y";
        String refundOrderId = NumberGenerator.getRefundOrderId(orderId, 0);
        String response = iWxPayService.createRefundOrder(
                "SH0001",
                (byte) 1,
                null,
                orderId,
                refundOrderId,
                1,
                1);
        System.out.println("退款结果***************" + response);

        return CommonResult.succeeded(response);
    }

    @ApiOperation(value = "开始护理")
    @PostMapping(value = "/startService/{additionalSuborderId}/{serviceOperatorId}")
    public CommonResult<?> startService(@PathVariable String additionalSuborderId,
            @PathVariable String serviceOperatorId, HttpServletRequest request, HttpServletResponse response) {
        String unionidFromSession = (String) request.getSession().getAttribute("unionid");
        AppServiceSuborderAdditional oneBySuborderId = iAppServiceSuborderAdditionalService
                .getOneBySuborderId(additionalSuborderId);
        // 确认订单，是否存在，状态是否符合，判断此订单是否是本人
        return CommonResult.succeeded(
                iAppServiceOperationRecordService.startService(unionidFromSession, oneBySuborderId, serviceOperatorId));
    }

    @ApiOperation(value = "结束护理")
    @PostMapping(value = "/endService/{operationRecordId}")
    public CommonResult<?> endService(@PathVariable Long operationRecordId, HttpServletRequest request,
            HttpServletResponse response) {
        String unionidFromSession = (String) request.getSession().getAttribute("unionid");
        return CommonResult.succeeded(iAppServiceOperationRecordService.endService(operationRecordId));

    }

    @ApiOperation(value = "获取服务操作对象")
    @GetMapping(value = "/getOperation/{additionalSuborderId}")
    public CommonResult<?> operation(@PathVariable String additionalSuborderId, HttpServletRequest request,
            HttpServletResponse response) {
        return CommonResult.succeeded(iAppServiceOperationRecordService.getOperationRecord(additionalSuborderId));
    }
}