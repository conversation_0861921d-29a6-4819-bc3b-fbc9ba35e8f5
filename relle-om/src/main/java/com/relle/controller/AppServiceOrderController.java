package com.relle.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.relle.common.api.CommonResult;
import com.relle.common.utils.NumberGenerator;
import com.relle.dto.AppServiceBundleOrderDTO;
import com.relle.dto.AppServiceItemVO;
import com.relle.dto.AppServiceOrderDTO;
import com.relle.dto.AppServiceSuborderDTO;
import com.relle.dto.AppServiceTeamOrderDTO;
import com.relle.dto.RefundOrderParamDTO;
import com.relle.enums.OderRefundStatusEnum;
import com.relle.enums.OrderCategoryEnum;
import com.relle.enums.ServiceItemMediaTypeEnum;
import com.relle.mbg.model.*;
import com.relle.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Api(tags = "订单处理接口")
@RestController
@RequestMapping(value = "/order")
public class AppServiceOrderController {
    private static final Logger logger = LoggerFactory.getLogger(AppServiceOrderController.class);

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private IAppServiceOrderService iAppServiceOrderService;

    @Resource
    private IAppServiceBundleOrderService iAppServiceBundleOrderService;

    @Resource
    private IAppServiceTeamOrderService iAppServiceTeamOrderService;

    @Resource
    private IWxPayService iWxPayService;

    // @Autowired
    // private IScheduleJobService iScheduleJobService;

    @Resource
    private IAppServiceItemService iAppServiceItemService;
    // @Autowired
    // private ISysTaskService iSysTaskService;
    @Resource
    private AppServiceOrderServiceFactory iAppServiceOrderServiceFactory;
    @Resource
    private IVSubordersService ivSubordersService;
    @Resource
    private IAppCustomerInfoService iAppCustomerInfoService;

    @PostMapping(value = "/createOrder")
    public CommonResult<?> createOrder(@RequestBody Map<String, Object> orderMap, HttpServletRequest request) {
        // 1、处理信息
        // 2、获取service
        // 3、校验保存
        // 4、调取支付
        // 获取用户信息

        String unionid = (String) request.getSession().getAttribute("unionid");
        String openid = (String) request.getSession().getAttribute("openid");
        // 取出门店信息和服务产品信息，生成订单编号
        String storeId = (String) orderMap.get("storeId");
        String serviceItemId = (String) orderMap.get("serviceItemId");
        Number serviceItemAmount = (Number) orderMap.get("serviceItemAmount");
        String orderId = NumberGenerator.getServiceOrderId(storeId);
        orderMap.put("unionid", unionid);
        orderMap.put("orderId", orderId);
        // 获取产品信息
        AppServiceItemVO itemVO = iAppServiceItemService.getItemDetail(storeId, serviceItemId);
        Byte serviceItemCategory = itemVO.getServiceItemCategory();
        // 根据产品获取不同的service
        IOrderService service = iAppServiceOrderServiceFactory.getService(serviceItemCategory);
        CommonResult r = service.createOrder(orderMap);
        if (!r.isStatus()) {
            return r;
        }
        try {
            Integer saveAsMvlnfo = (Integer) orderMap.get("saveAsMvlnfo");
            if (saveAsMvlnfo == 1) {
                AppCustomerInfo customerInfo = iAppCustomerInfoService.selectByUnionid(unionid);
                String contactName = (String) orderMap.get("contactName");
                String contactPhone = (String) orderMap.get("contactPhone");
                customerInfo.setWechatNickname(contactName);
                customerInfo.setWechatPhone(contactPhone);
                iAppCustomerInfoService.update(customerInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 进行微信支付预下单，成功后，进行数据保存
        String autoTask = iAppServiceOrderServiceFactory.getAutoTask(serviceItemCategory);
        Map map = iWxPayService.toPay(storeId, serviceItemCategory, serviceItemId,
                (int) (serviceItemAmount.doubleValue() * 100), orderId, openid, autoTask, service);

        r.setData(map);
        return r;
        /*
         * if( serviceItemAmount.doubleValue()>0) { //订单金额大于0，需要调取支付功能
         * try {
         * map = iWxPayService.createOrder(serviceItemId, (int)
         * (serviceItemAmount.doubleValue() * 100), orderId, openid);
         *
         * } catch (Exception e) {
         * e.printStackTrace();
         * return CommonResult.failed(2000, "下单失败");
         * }
         *
         * orderMap.put("unionid", unionid);
         * orderMap.putAll(map);
         *
         * //1.获取产品信息，确认产品类型
         * //2.根据产品类型，处理不同流程
         * //3.服务产品，先校验数据，然后进行保存
         * //4.代金券产品，先校验数据，然后进行保存
         *
         * CommonResult r = CommonResult.failed();
         * if(serviceItemCategory == AppServiceItemCategoryEnum.SERVICE.getCode()){
         * r = iAppServiceOrderService.createOrder(orderMap);
         * } else if(serviceItemCategory ==
         * AppServiceItemCategoryEnum.CASH_COUPON.getCode()){
         * r = iAppServiceBundleOrderService.createOrder(orderMap);
         * } else {
         * r = iAppServiceOrderService.createOrder(orderMap);
         * }
         * r.setData(map);
         * return r;
         * } else { //订单金额为0的，不需要调取支付功能，直接显示支付成功。
         * CommonResult r = CommonResult.failed();
         * orderMap.put("orderStatus", ServiceOrderStatusEnum.PAY_SUCCESS.getCode());
         * if(serviceItemCategory == AppServiceItemCategoryEnum.SERVICE.getCode()){
         * r = iAppServiceOrderService.createOrder(orderMap);
         * } else if(serviceItemCategory ==
         * AppServiceItemCategoryEnum.CASH_COUPON.getCode()){
         * orderMap.put("orderStatus", ServiceOrderStatusEnum.FINISHED.getCode());
         * r = iAppServiceBundleOrderService.createOrder(orderMap);
         * } else {
         * r = iAppServiceOrderService.createOrder(orderMap);
         * }
         * iSysTaskService.close(orderId);
         *
         * map.put("notLaunch",1);
         * r.setData(map);
         * return r;
         * }
         */
    }

    @RequestMapping("/notifyUrl/{storeId}/{serviceItemCategory}")
    public String notifyUrl(@PathVariable String storeId, @PathVariable Byte serviceItemCategory,
            HttpServletRequest request) {
        System.out.println("------------------------notifyUrl----------------------------");
        System.out.println("SH0001:" + storeId);
        System.out.println("Wechatpay-Timestamp:" + request.getHeader("Wechatpay-Timestamp"));
        System.out.println("Wechatpay-Nonce:" + request.getHeader("Wechatpay-Nonce"));
        System.out.println("Wechatpay-Signature:" + request.getHeader("Wechatpay-Signature"));
        System.out.println("Wechatpay-Serial:" + request.getHeader("Wechatpay-Serial"));

        // JSONObject result = new JSONObject();
        ObjectNode result = objectMapper.createObjectNode();
        result.put("code", "FAIL");
        try {
            BufferedReader br = request.getReader();
            String str = null;
            StringBuilder builder = new StringBuilder();
            while ((str = br.readLine()) != null) {
                builder.append(str);
            }
            System.out.println("notifyUrl builder：" + builder);

            // 验证签名
            // 参考：https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay4_1.shtml
            StringBuffer signStr = new StringBuffer();
            signStr.append(request.getHeader("Wechatpay-Timestamp")).append("\n");
            signStr.append(request.getHeader("Wechatpay-Nonce")).append("\n");
            signStr.append(builder.toString()).append("\n");
            if (!iWxPayService.signVerify(storeId, serviceItemCategory, request.getHeader("Wechatpay-Serial"),
                    signStr.toString(), request.getHeader("Wechatpay-Signature"))) {
                result.put("message", "sign error");
                return result.toString();
            }
            // 解密密文
            String goodBody = iWxPayService.decryptOrder(storeId, serviceItemCategory, builder.toString());
            System.out.println("notifyUrl goodBody：" + goodBody);
            // todo 验证订单（订单是否存在，订单是否重复支付，订单金额与支付金额是否一致），以及更新订单状态等……
            // todo
            // 需要注意的是，由于网络或其它原因，微信调用这个接口可能会在支付成功的很长时间之后，没准那个时候用户已经申请了退款。所以最好是在本接口中调用queryOrder()去微信查询该订单最新的状态。
            // goodBody =
            // "{\"mchid\":\"**********\",\"appid\":\"wx7868e71b9b547f08\",\"out_trade_no\":\"0540918f84d4b4c9a265a2b97e65e1e3\",\"transaction_id\":\"4200001620202211260175459288\",\"trade_type\":\"JSAPI\",\"trade_state\":\"SUCCESS\",\"trade_state_desc\":\"支付成功\",\"bank_type\":\"OTHERS\",\"attach\":\"\",\"success_time\":\"2022-11-26T22:21:55+08:00\",\"payer\":{\"openid\":\"o4a585ZnGo6BGCmliLlTNR3o4b_c\"},\"amount\":{\"total\":1,\"payer_total\":1,\"currency\":\"CNY\",\"payer_currency\":\"CNY\"}}";
            // JSONObject goodJson = JSON.parseObject(goodBody);
            JsonNode goodJson = objectMapper.readTree(goodBody);
            if ("SUCCESS".equalsIgnoreCase(goodJson.get("trade_state").asText())) {
                String serviceItemId = goodJson.get("attach").asText();

                System.out.println("*******************" + serviceItemId);

                // 获取产品信息

                // 根据产品获取不同的service
                IOrderService service = iAppServiceOrderServiceFactory.getService(serviceItemCategory);

                service.paySuccess(goodJson.get("out_trade_no").asText(), goodJson.get("success_time").asText(),
                        goodJson.get("transaction_id").asText());
            }
            // 一切正常后返回code=SUCCESS,
            result.put("code", "SUCCESS");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("message", "exception:" + e.getMessage());
        }
        logger.info("支付通知HTTP应答：" + result.toString());
        return result.toString();
    }

    @RequestMapping("/refundNotifyUrl/{storeId}/{serviceItemCategory}")
    @ResponseBody
    public String refundNotifyUrl(@PathVariable String storeId, @PathVariable Byte serviceItemCategory,
            HttpServletRequest request) {
        // String storeId = "SH0001";
        // JSONObject result = new JSONObject();
        ObjectNode result = objectMapper.createObjectNode();
        result.put("code", "FAIL");
        try {
            BufferedReader br = request.getReader();
            String str = null;
            StringBuilder builder = new StringBuilder();
            while ((str = br.readLine()) != null) {
                builder.append(str);
            }
            System.out.println("refundNotifyUrl builder：" + builder);

            //// TODO: 2022/11/27
            //// builder中存放着微信返回的数据，示例：{"id":"7baa0238-9522-5b2e-9d4a-6de8a5bcaf63","create_time":"2022-11-27T01:18:25+08:00","resource_type":"encrypt-resource","event_type":"REFUND.SUCCESS","summary":"退款成功","resource":{"original_type":"refund","algorithm":"AEAD_AES_256_GCM","ciphertext":"pg0aezxdJCBZdKF9IKoQpqZbxn4IhBkYyGsAppVliw3eKUxKRp5976rmK63MA4FbswLmU8p4UBVIZahVmHDl7vkswo5hlfmHILf5TbPSaOuM030JSD27qcrhNGNEhSeL0ud6sFnuXN4bHLo+blP6TbBOpyEMZmELhYIT7XeadxwLkBLP0UQbn4xHVEsB//pw85RailcyDNgqsbTN09hkeBsu1EOE83bbF3eK6trrwti6s3l/l6Kthy6xjPzWOID2RVpGNT2Wc1xSgHHgVhqzZyGm2GqzH/7Ikaf9S93WpntQ+E0IcImSGGsdl6QB7EqF8fD4V3OsOyxt2rrRCDdh82+AJxlo8PDJs2k1XK9yqdMOuE/gzxKc78fo0REEmY9hm3//7oOY5hVg1WbwkI55VzxFYsCQ/TFEKEDc4x4ytZkjhT07dVjsjCRK0QWoAjgHNUCv5oYxk5yhYQZ9lAx2BkukzgkGfaQy/v2sqc3wKcBbBDOMgmbSwdWsiDxG1jqmwezV8ZXdI7WqYpzGjr9yrrHi9sEwZgHSWi4=","associated_data":"refund","nonce":"benAkLM5rh6S"}}
            StringBuffer signStr = new StringBuffer();
            signStr.append(request.getHeader("Wechatpay-Timestamp")).append("\n");
            signStr.append(request.getHeader("Wechatpay-Nonce")).append("\n");
            signStr.append(builder.toString()).append("\n");
            if (!iWxPayService.signVerify(storeId, serviceItemCategory, request.getHeader("Wechatpay-Serial"),
                    signStr.toString(), request.getHeader("Wechatpay-Signature"))) {
                result.put("message", "sign error");
                return result.toString();
            }
            // 解密密文
            String goodBody = iWxPayService.decryptOrder(storeId, serviceItemCategory, builder.toString());

            /**
             * {"mchid":"**********","out_trade_no":"*********************",
             * "transaction_id":"4200001710202212034416400926","out_refund_no":"*********************r00",
             * "refund_id":"50302604122022120428033870571",
             * "refund_status":"SUCCESS",
             * "success_time":"2022-12-04T20:13:06+08:00",
             * "amount":{"total":1,"refund":1,"payer_total":1,
             * "payer_refund":1},"user_received_account":"支付用户零钱"}
             */
            // JSONObject goodJson = JSON.parseObject(goodBody);
            JsonNode goodJson = objectMapper.readTree(goodBody);

            logger.info("退款返回接口:" + goodBody);
            if ("SUCCESS".equalsIgnoreCase(goodJson.get("refund_status").asText())) {
                String orderId = goodJson.get("out_trade_no").asText();
                String refundOrderId = goodJson.get("out_refund_no").asText();
                String success_time = goodJson.get("success_time").asText();
                // 通过订单
                String serviceItemId = ivSubordersService.getServiceItemIdFromOrderId(orderId);
                System.out.println("退款返回获取产品*******************" + serviceItemId);

                // 获取产品信息
                /*
                 * AppServiceItemVO itemVO =
                 * iAppServiceItemService.getItemDetail(storeId,serviceItemId);
                 * Byte serviceItemCategory = itemVO.getServiceItemCategory();
                 */
                // 根据产品获取不同的service
                IOrderService service = iAppServiceOrderServiceFactory.getService(serviceItemCategory);
                // 更新订单
                service.refundResult(orderId, OderRefundStatusEnum.REFUND_FINISHED.getCode(), success_time,
                        refundOrderId);

            } else {
                String orderId = goodJson.get("out_trade_no").asText();
                String refundOrderId = goodJson.get("out_refund_no").asText();
                String success_time = goodJson.get("success_time").asText();
                iAppServiceOrderService.refundResult(orderId, OderRefundStatusEnum.REFUND_FAIL.getCode(), success_time,
                        refundOrderId);
            }
            result.put("code", "SUCCESS");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }

    @RequestMapping(value = "/getRefundAmount/{orderId}")
    public CommonResult<?> getRefundAmount(@PathVariable String orderId, HttpServletRequest request) {
        String unionid_from_session = (String) request.getSession().getAttribute("unionid");
        CommonResult result = iAppServiceOrderService.getOrderDetail(orderId);
        AppServiceOrderDTO dto = (AppServiceOrderDTO) result.getData();
        AppServiceOrder order = dto.getOrder();
        List<AppServiceSuborderDTO> appServiceSuborderDTOS = dto.getSuborders();
        AppServiceSuborderDTO appServiceSuborderDTO = appServiceSuborderDTOS.get(0);
        LocalDateTime bookTime = appServiceSuborderDTO.getSuborder().getBookTime();
        DecimalFormat decimalFormat = new DecimalFormat("0.00");

        /**
         * 1.非预约服务当日，可申请更改预约时间或取消预约并可全额退款；
         * 2.预约当天，距服务时间3小时以上，可更改预约时间，重新预约，不支持取消订单
         * 3.预约当天，距服务时间3小时以内，不支持更改服务时间和取消订单。
         */
        String bookTimeDay = LocalDate.parse(bookTime.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).toString();
        String nowDay = LocalDate.now().toString();
        if (bookTimeDay.compareTo(nowDay) <= 0) {
            return CommonResult.failed("预约服务当日不支持申请退款");
        } else {
            // JSONObject object = new JSONObject();
            ObjectNode returnObjectNode = objectMapper.createObjectNode();
            returnObjectNode.put("orderId", orderId);
            returnObjectNode.put("orderAmount", order.getOrderAmount());
            returnObjectNode.put("refundAmount",
                    Double.valueOf(decimalFormat.format(order.getOrderAmount().doubleValue())));
            return CommonResult.succeeded(returnObjectNode);
        }

    }

    @ApiOperation(value = "退单（用户）")
    @PostMapping(value = "/refundOrder/{orderId}")
    public CommonResult<?> refundOrder(@PathVariable String orderId, @RequestBody Map<String, Object> orderMap,
            HttpServletRequest request) {
        String unionid_from_session = (String) request.getSession().getAttribute("unionid");
        Number refundAmount = (Number) orderMap.get("refundAmount");
        String refundReason = (String) orderMap.get("refundReason");
        System.out.println("退款金额*****************" + refundAmount);

        String storeId = orderId.substring(0, 6);
        String serviceItemId = ivSubordersService.getServiceItemIdFromOrderId(orderId);
        // 获取产品信息
        AppServiceItemVO itemVO = iAppServiceItemService.getItemDetail(storeId, serviceItemId);
        Byte serviceItemCategory = itemVO.getServiceItemCategory();
        // 根据产品获取不同的service
        IOrderService service = iAppServiceOrderServiceFactory.getService(serviceItemCategory);

        if (service.checkOrder(orderId, unionid_from_session)) {
            boolean isClose = false;
            CommonResult result = service.getOrderDetail(orderId);

            Number orderAmount = 0;
            if (result.getData() instanceof AppServiceOrderDTO) {
                AppServiceOrderDTO dto = (AppServiceOrderDTO) result.getData();
                AppServiceOrder order = dto.getOrder();
                orderAmount = order.getOrderAmount();
            } else if (result.getData() instanceof AppServiceBundleOrderDTO) {
                AppServiceBundleOrderDTO dto = (AppServiceBundleOrderDTO) result.getData();
                AppServiceBundleOrder order = dto.getOrder();
                orderAmount = order.getOrderAmount();
            } else if (result.getData() instanceof AppServiceTeamOrderDTO) {
                AppServiceTeamOrderDTO dto = (AppServiceTeamOrderDTO) result.getData();
                AppServiceTeamOrder order = dto.getOrder();
                orderAmount = order.getOrderAmount();
            } else {
                return CommonResult.failed(9000, "这类订单暂不支持退款");
            }
            String refundOrderId = NumberGenerator.getRefundOrderId(orderId, 0);
            if (orderAmount.doubleValue() == 0d) {
                isClose = true;
            } else {

                try {
                    String response = iWxPayService.createRefundOrder(
                            storeId,
                            serviceItemCategory,
                            null,
                            orderId,
                            refundOrderId,
                            (int) (orderAmount.doubleValue() * 100),
                            (int) (refundAmount.doubleValue() * 100));
                    System.out.println("退款结果***************" + response);
                    // JSONObject responseObj = JSONObject.parseObject(response);
                    JsonNode responseObj = objectMapper.readTree(response);
                    if ("PROCESSING".equalsIgnoreCase(responseObj.get("status").asText())
                            || "SUCCESS".equalsIgnoreCase(responseObj.get("status").asText())) {
                        isClose = true;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (isClose) {
                return service.refundOrder(orderId, unionid_from_session, refundOrderId, refundAmount.doubleValue(),
                        refundReason);
            } else {
                return CommonResult.failed(3001, "退款失败");
            }
        } else {
            return CommonResult.failed(3000, "不支持退款");
        }
    }

    @ApiOperation(value = "退单（店长端）")
    @PostMapping(value = "/refundOrderAdmin/{orderId}")
    public CommonResult<?> refundOrderAdmin(@PathVariable String orderId, @RequestBody RefundOrderParamDTO paramDTO,
            HttpServletRequest request) {
        String unionid_from_session = (String) request.getSession().getAttribute("unionid");
        Number refundAmount = paramDTO.getRefundAmount();
        String refundReason = paramDTO.getRefundReason();

        String storeId = orderId.substring(0, 6);
        String serviceItemId = ivSubordersService.getServiceItemIdFromOrderId(orderId);
        // 获取产品信息
        AppServiceItemVO itemVO = iAppServiceItemService.getItemDetail(storeId, serviceItemId);
        Byte serviceItemCategory = itemVO.getServiceItemCategory();
        // 根据产品获取不同的service
        IOrderService service = iAppServiceOrderServiceFactory.getService(serviceItemCategory);

        if (service.checkOrderAdmin(orderId, unionid_from_session)) {
            boolean isClose = false;
            CommonResult result = service.getOrderDetail(orderId);

            Number orderAmount = 0;
            if (result.getData() instanceof AppServiceOrderDTO) {
                AppServiceOrderDTO dto = (AppServiceOrderDTO) result.getData();
                AppServiceOrder order = dto.getOrder();
                orderAmount = order.getOrderAmount();
            } else if (result.getData() instanceof AppServiceBundleOrderDTO) {
                AppServiceBundleOrderDTO dto = (AppServiceBundleOrderDTO) result.getData();
                AppServiceBundleOrder order = dto.getOrder();
                orderAmount = order.getOrderAmount();
            } else if (result.getData() instanceof AppServiceTeamOrderDTO) {
                AppServiceTeamOrderDTO dto = (AppServiceTeamOrderDTO) result.getData();
                AppServiceTeamOrder order = dto.getOrder();
                orderAmount = order.getOrderAmount();
            } else {
                return CommonResult.failed(9000, "这类订单暂不支持退款");
            }
            String refundOrderId = NumberGenerator.getRefundOrderId(orderId, 0);
            if (orderAmount.doubleValue() == 0d) {
                isClose = true;
            } else {

                try {
                    String response = iWxPayService.createRefundOrder(
                            storeId,
                            serviceItemCategory,
                            null,
                            orderId,
                            refundOrderId,
                            (int) (orderAmount.doubleValue() * 100),
                            (int) (refundAmount.doubleValue() * 100));
                    System.out.println("退款结果***************" + response);
                    // JSONObject responseObj = JSONObject.parseObject(response);
                    JsonNode responseObj = objectMapper.readTree(response);
                    if ("PROCESSING".equalsIgnoreCase(responseObj.get("status").asText())
                            || "SUCCESS".equalsIgnoreCase(responseObj.get("status").asText())) {
                        isClose = true;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (isClose) {
                return service.refundOrder(orderId, unionid_from_session, refundOrderId, refundAmount.doubleValue(),
                        refundReason);
            } else {
                return CommonResult.failed(3001, "退款失败");
            }
        } else {
            return CommonResult.failed(3000, "不支持退款");
        }
    }

    @ApiOperation(value = "取消支付")
    @PostMapping(value = "/cancelOrder/{orderId}")
    public CommonResult<?> cancelOrder(@PathVariable String orderId, HttpServletRequest request) {
        String unionid_from_session = (String) request.getSession().getAttribute("unionid");
        String storeId = orderId.substring(0, 6);
        String serviceItemId = ivSubordersService.getServiceItemIdFromOrderId(orderId);
        // 获取产品信息
        AppServiceItemVO itemVO = iAppServiceItemService.getItemDetail(storeId, serviceItemId);
        Byte serviceItemCategory = itemVO.getServiceItemCategory();
        // 根据产品获取不同的service
        IOrderService service = iAppServiceOrderServiceFactory.getService(serviceItemCategory);
        return service.cancelOrder(orderId, unionid_from_session);
    }

    @ApiOperation(value = "订单列表")
    @GetMapping(value = "/list/{unionid}")
    public CommonResult<?> list(@PathVariable String unionid, HttpServletRequest request) {
        String unionid_from_session = (String) request.getSession().getAttribute("unionid");
        System.out.println("***************" + unionid_from_session);
        if (unionid_from_session.equalsIgnoreCase(unionid)) {
            return iAppServiceOrderService.getOrderList(unionid);
        } else {
            return CommonResult.failed(550, "非法操作");
        }

    }

    @ApiOperation(value = "订单明细")
    @GetMapping(value = "/detail/{orderCategory}/{orderId}")
    public CommonResult<?> detail(@PathVariable Byte orderCategory, @PathVariable String orderId,
            HttpServletRequest request) {
        if (orderCategory.byteValue() == OrderCategoryEnum.SERVICE.getCode()) {
            return iAppServiceOrderService.getOrderDetail(orderId);
        } else if (orderCategory.byteValue() == OrderCategoryEnum.COUPON.getCode()) {
            return iAppServiceBundleOrderService.getOrderDetail(orderId);
        } else if (orderCategory.byteValue() == OrderCategoryEnum.TEAM.getCode()) {
            return iAppServiceTeamOrderService.getOrderDetail(orderId);
        } else {
            return CommonResult.failed("不支持的类型");
        }
    }

    @ApiOperation(value = "修改预约时间")
    @PostMapping(value = "/updateBookTime/{orderId}")
    public CommonResult<?> updateBookTime(@PathVariable String orderId, @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        String unionid_from_session = (String) request.getSession().getAttribute("unionid");
        String bookTime = (String) params.get("bookTime");
        LocalDateTime bookTimeDate = null;
        try {
            bookTimeDate = LocalDateTime.parse(bookTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
            return iAppServiceOrderService.updateBookTime(orderId, unionid_from_session, bookTimeDate);
        } catch (Exception e) {
            e.printStackTrace();
            return CommonResult.failed("预约时间格式不正确");
        }
    }
    /*
     * @RequestMapping(value = "/queryOrder/{storeId}/{orderId}")
     * public CommonResult<?> queryOrder(@PathVariable String storeId, @PathVariable
     * String orderId, HttpServletRequest request){
     * return CommonResult.succeeded(iWxPayService.queryOrder(storeId,orderId));
     * }
     */

}