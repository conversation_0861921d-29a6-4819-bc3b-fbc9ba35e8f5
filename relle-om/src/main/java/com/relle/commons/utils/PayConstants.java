package com.relle.commons.utils;

import com.relle.cache.mch.MchCache;
import com.relle.mbg.model.AppMchAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
/*
@PropertySource("classpath:/wxPayConfig_${spring.profiles.active}.properties")
*/
public class PayConstants {
    @Autowired
    private Environment env;
    @Resource
    private MchCache mchCache;

    /*@Value("${PAY_NOTIFY_URL}")
    private String payNotifyUrl = "https://relle.douwifi.cn/relle-om-0.1/order/notifyUrl";
    @Value("${REFUND_NOTIFY_URL}")
    private String refundNotifyUrl = "https://relle.douwifi.cn/relle-om-0.1/order/refundNotifyUrl";  //退款回调地址
    @Value("${MCH_ID}")
    private String mchId = "**********"; // 商户号
    @Value("${MCH_SERIAL_NO}")
    private String mchSerialNo = "7E14FD3DE0534E05D4A75DE6CDA6ED53A45B722B"; // 商户证书序列号
    @Value("${API_V3KEY}")
    private String apiV3key = "4265f5e0be618df09d0198a6386b307B"; // API V3密钥
    @Value("${APP_ID}")
    private String appId = "wx56b81e1a0a4cc40a";               //appid*/
//    String PACKAGE = "Sign=WXPay";      //签名固定字符串（微信要求的）
    // 你的商户私钥
    /*@Value("${PRIVATE_KEY_PATH}")
    private String privateKeyPath = "apiclient_key.pem";*/

    public String getPayNotifyUrl(String storeId,Byte serviceItemCategory) {
        return env.getProperty(storeId+"_PAY_NOTIFY_URL").trim()+storeId+"/"+serviceItemCategory;
    }

    public String getRefundNotifyUrl(String storeId,Byte serviceItemCategory) {
        return env.getProperty(storeId+"_REFUND_NOTIFY_URL").trim()+storeId+"/"+serviceItemCategory;
    }

    public String getMchId(String storeId,Byte serviceItemCategory) {
        AppMchAccount mchAccount = mchCache.getMchAccount(storeId, serviceItemCategory);
        return mchAccount.getMchId();
    }

    public String getMchSerialNo(String storeId,Byte serviceItemCategory) {
        AppMchAccount mchAccount = mchCache.getMchAccount(storeId, serviceItemCategory);
        return mchAccount.getCertificateSerialNumber();
    }

    public String getApiV3key(String storeId,Byte serviceItemCategory){
        AppMchAccount mchAccount = mchCache.getMchAccount(storeId, serviceItemCategory);
        return mchAccount.getApiV3key();
    }

    public String getAppId() {
        return env.getProperty("APP_ID").trim();
    }


    public String getPrivateKey(String storeId,Byte serviceItemCategory) {
        AppMchAccount mchAccount = mchCache.getMchAccount(storeId, serviceItemCategory);
        return mchAccount.getApiclientKey();
    }

    public String getOthers(String paramName) {
        return  env.getProperty(paramName);
    }

}