<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.MtCodeToCouponMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.MtCodeToCoupon">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="deal_id" jdbcType="VARCHAR" property="dealId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="service_item" jdbcType="VARCHAR" property="serviceItem" />
    <result column="checkout_id" jdbcType="BIGINT" property="checkoutId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, time, store_id, unionid, deal_id, code, coupon_id, service_item, checkout_id
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.MtCodeToCouponExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mt_code_to_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mt_code_to_coupon
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mt_code_to_coupon
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.MtCodeToCouponExample">
    delete from mt_code_to_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.MtCodeToCoupon">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_code_to_coupon (time, store_id, unionid, 
      deal_id, code, coupon_id, 
      service_item, checkout_id)
    values (#{time,jdbcType=TIMESTAMP}, #{storeId,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{dealId,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{couponId,jdbcType=VARCHAR}, 
      #{serviceItem,jdbcType=VARCHAR}, #{checkoutId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.MtCodeToCoupon">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_code_to_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="time != null">
        time,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="dealId != null">
        deal_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="serviceItem != null">
        service_item,
      </if>
      <if test="checkoutId != null">
        checkout_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="dealId != null">
        #{dealId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItem != null">
        #{serviceItem,jdbcType=VARCHAR},
      </if>
      <if test="checkoutId != null">
        #{checkoutId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.MtCodeToCouponExample" resultType="java.lang.Integer">
    select count(*) from mt_code_to_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mt_code_to_coupon
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.time != null">
        time = #{record.time,jdbcType=TIMESTAMP},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.dealId != null">
        deal_id = #{record.dealId,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItem != null">
        service_item = #{record.serviceItem,jdbcType=VARCHAR},
      </if>
      <if test="record.checkoutId != null">
        checkout_id = #{record.checkoutId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mt_code_to_coupon
    set id = #{record.id,jdbcType=BIGINT},
      time = #{record.time,jdbcType=TIMESTAMP},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      unionid = #{record.unionid,jdbcType=VARCHAR},
      deal_id = #{record.dealId,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      service_item = #{record.serviceItem,jdbcType=VARCHAR},
      checkout_id = #{record.checkoutId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.MtCodeToCoupon">
    update mt_code_to_coupon
    <set>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="dealId != null">
        deal_id = #{dealId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItem != null">
        service_item = #{serviceItem,jdbcType=VARCHAR},
      </if>
      <if test="checkoutId != null">
        checkout_id = #{checkoutId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.MtCodeToCoupon">
    update mt_code_to_coupon
    set time = #{time,jdbcType=TIMESTAMP},
      store_id = #{storeId,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      deal_id = #{dealId,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      service_item = #{serviceItem,jdbcType=VARCHAR},
      checkout_id = #{checkoutId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>