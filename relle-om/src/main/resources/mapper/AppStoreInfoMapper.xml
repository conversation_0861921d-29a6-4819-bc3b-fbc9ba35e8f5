<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppStoreInfoMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppStoreInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_address" jdbcType="VARCHAR" property="storeAddress" />
    <result column="store_phone" jdbcType="VARCHAR" property="storePhone" />
    <result column="store_conver_src" jdbcType="VARCHAR" property="storeConverSrc" />
    <result column="store_longitude" jdbcType="DECIMAL" property="storeLongitude" />
    <result column="store_latitude" jdbcType="DECIMAL" property="storeLatitude" />
    <result column="store_opening_date" jdbcType="DATE" property="storeOpeningDate" />
    <result column="store_detail_info" jdbcType="VARCHAR" property="storeDetailInfo" />
    <result column="store_status" jdbcType="TINYINT" property="storeStatus" />
    <result column="store_type" jdbcType="TINYINT" property="storeType" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, store_name, store_address, store_phone, store_conver_src, store_longitude, 
    store_latitude, store_opening_date, store_detail_info, store_status, store_type, 
    create_by, create_time, update_by, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppStoreInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_store_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_store_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_store_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppStoreInfoExample">
    delete from app_store_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppStoreInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_store_info (store_id, store_name, store_address, 
      store_phone, store_conver_src, store_longitude, 
      store_latitude, store_opening_date, store_detail_info, 
      store_status, store_type, create_by, 
      create_time, update_by, update_time, 
      deleted)
    values (#{storeId,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{storeAddress,jdbcType=VARCHAR}, 
      #{storePhone,jdbcType=VARCHAR}, #{storeConverSrc,jdbcType=VARCHAR}, #{storeLongitude,jdbcType=DECIMAL}, 
      #{storeLatitude,jdbcType=DECIMAL}, #{storeOpeningDate,jdbcType=DATE}, #{storeDetailInfo,jdbcType=VARCHAR}, 
      #{storeStatus,jdbcType=TINYINT}, #{storeType,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppStoreInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_store_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="storeAddress != null">
        store_address,
      </if>
      <if test="storePhone != null">
        store_phone,
      </if>
      <if test="storeConverSrc != null">
        store_conver_src,
      </if>
      <if test="storeLongitude != null">
        store_longitude,
      </if>
      <if test="storeLatitude != null">
        store_latitude,
      </if>
      <if test="storeOpeningDate != null">
        store_opening_date,
      </if>
      <if test="storeDetailInfo != null">
        store_detail_info,
      </if>
      <if test="storeStatus != null">
        store_status,
      </if>
      <if test="storeType != null">
        store_type,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeAddress != null">
        #{storeAddress,jdbcType=VARCHAR},
      </if>
      <if test="storePhone != null">
        #{storePhone,jdbcType=VARCHAR},
      </if>
      <if test="storeConverSrc != null">
        #{storeConverSrc,jdbcType=VARCHAR},
      </if>
      <if test="storeLongitude != null">
        #{storeLongitude,jdbcType=DECIMAL},
      </if>
      <if test="storeLatitude != null">
        #{storeLatitude,jdbcType=DECIMAL},
      </if>
      <if test="storeOpeningDate != null">
        #{storeOpeningDate,jdbcType=DATE},
      </if>
      <if test="storeDetailInfo != null">
        #{storeDetailInfo,jdbcType=VARCHAR},
      </if>
      <if test="storeStatus != null">
        #{storeStatus,jdbcType=TINYINT},
      </if>
      <if test="storeType != null">
        #{storeType,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppStoreInfoExample" resultType="java.lang.Integer">
    select count(*) from app_store_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_store_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeAddress != null">
        store_address = #{record.storeAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.storePhone != null">
        store_phone = #{record.storePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.storeConverSrc != null">
        store_conver_src = #{record.storeConverSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.storeLongitude != null">
        store_longitude = #{record.storeLongitude,jdbcType=DECIMAL},
      </if>
      <if test="record.storeLatitude != null">
        store_latitude = #{record.storeLatitude,jdbcType=DECIMAL},
      </if>
      <if test="record.storeOpeningDate != null">
        store_opening_date = #{record.storeOpeningDate,jdbcType=DATE},
      </if>
      <if test="record.storeDetailInfo != null">
        store_detail_info = #{record.storeDetailInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.storeStatus != null">
        store_status = #{record.storeStatus,jdbcType=TINYINT},
      </if>
      <if test="record.storeType != null">
        store_type = #{record.storeType,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_store_info
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      store_address = #{record.storeAddress,jdbcType=VARCHAR},
      store_phone = #{record.storePhone,jdbcType=VARCHAR},
      store_conver_src = #{record.storeConverSrc,jdbcType=VARCHAR},
      store_longitude = #{record.storeLongitude,jdbcType=DECIMAL},
      store_latitude = #{record.storeLatitude,jdbcType=DECIMAL},
      store_opening_date = #{record.storeOpeningDate,jdbcType=DATE},
      store_detail_info = #{record.storeDetailInfo,jdbcType=VARCHAR},
      store_status = #{record.storeStatus,jdbcType=TINYINT},
      store_type = #{record.storeType,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppStoreInfo">
    update app_store_info
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeAddress != null">
        store_address = #{storeAddress,jdbcType=VARCHAR},
      </if>
      <if test="storePhone != null">
        store_phone = #{storePhone,jdbcType=VARCHAR},
      </if>
      <if test="storeConverSrc != null">
        store_conver_src = #{storeConverSrc,jdbcType=VARCHAR},
      </if>
      <if test="storeLongitude != null">
        store_longitude = #{storeLongitude,jdbcType=DECIMAL},
      </if>
      <if test="storeLatitude != null">
        store_latitude = #{storeLatitude,jdbcType=DECIMAL},
      </if>
      <if test="storeOpeningDate != null">
        store_opening_date = #{storeOpeningDate,jdbcType=DATE},
      </if>
      <if test="storeDetailInfo != null">
        store_detail_info = #{storeDetailInfo,jdbcType=VARCHAR},
      </if>
      <if test="storeStatus != null">
        store_status = #{storeStatus,jdbcType=TINYINT},
      </if>
      <if test="storeType != null">
        store_type = #{storeType,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppStoreInfo">
    update app_store_info
    set store_id = #{storeId,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      store_address = #{storeAddress,jdbcType=VARCHAR},
      store_phone = #{storePhone,jdbcType=VARCHAR},
      store_conver_src = #{storeConverSrc,jdbcType=VARCHAR},
      store_longitude = #{storeLongitude,jdbcType=DECIMAL},
      store_latitude = #{storeLatitude,jdbcType=DECIMAL},
      store_opening_date = #{storeOpeningDate,jdbcType=DATE},
      store_detail_info = #{storeDetailInfo,jdbcType=VARCHAR},
      store_status = #{storeStatus,jdbcType=TINYINT},
      store_type = #{storeType,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>