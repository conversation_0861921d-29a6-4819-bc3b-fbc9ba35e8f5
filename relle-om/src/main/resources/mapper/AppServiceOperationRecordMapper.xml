<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppServiceOperationRecordMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppServiceOperationRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_operation_id" jdbcType="VARCHAR" property="serviceOperationId" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="suborder_id" jdbcType="VARCHAR" property="suborderId" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="service_operator_id" jdbcType="VARCHAR" property="serviceOperatorId" />
    <result column="service_starttime" jdbcType="TIMESTAMP" property="serviceStarttime" />
    <result column="service_endtime" jdbcType="TIMESTAMP" property="serviceEndtime" />
    <result column="nursing_starttime" jdbcType="TIMESTAMP" property="nursingStarttime" />
    <result column="nursing_endtime" jdbcType="TIMESTAMP" property="nursingEndtime" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="machine_id" jdbcType="VARCHAR" property="machineId" />
    <result column="service_operation_status" jdbcType="TINYINT" property="serviceOperationStatus" />
    <result column="customer_feedback" jdbcType="TINYINT" property="customerFeedback" />
    <result column="customer_feedback_time" jdbcType="TIMESTAMP" property="customerFeedbackTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, service_operation_id, unionid, suborder_id, store_id, service_operator_id, service_starttime, 
    service_endtime, nursing_starttime, nursing_endtime, room_id, machine_id, service_operation_status, 
    customer_feedback, customer_feedback_time, create_by, create_time, update_by, update_time, 
    deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppServiceOperationRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_service_operation_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_service_operation_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_service_operation_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppServiceOperationRecordExample">
    delete from app_service_operation_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppServiceOperationRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_operation_record (service_operation_id, unionid, suborder_id, 
      store_id, service_operator_id, service_starttime, 
      service_endtime, nursing_starttime, nursing_endtime, 
      room_id, machine_id, service_operation_status, 
      customer_feedback, customer_feedback_time, 
      create_by, create_time, update_by, 
      update_time, deleted)
    values (#{serviceOperationId,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{suborderId,jdbcType=VARCHAR}, 
      #{storeId,jdbcType=VARCHAR}, #{serviceOperatorId,jdbcType=VARCHAR}, #{serviceStarttime,jdbcType=TIMESTAMP}, 
      #{serviceEndtime,jdbcType=TIMESTAMP}, #{nursingStarttime,jdbcType=TIMESTAMP}, #{nursingEndtime,jdbcType=TIMESTAMP}, 
      #{roomId,jdbcType=VARCHAR}, #{machineId,jdbcType=VARCHAR}, #{serviceOperationStatus,jdbcType=TINYINT}, 
      #{customerFeedback,jdbcType=TINYINT}, #{customerFeedbackTime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppServiceOperationRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_operation_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serviceOperationId != null">
        service_operation_id,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="suborderId != null">
        suborder_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="serviceOperatorId != null">
        service_operator_id,
      </if>
      <if test="serviceStarttime != null">
        service_starttime,
      </if>
      <if test="serviceEndtime != null">
        service_endtime,
      </if>
      <if test="nursingStarttime != null">
        nursing_starttime,
      </if>
      <if test="nursingEndtime != null">
        nursing_endtime,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="machineId != null">
        machine_id,
      </if>
      <if test="serviceOperationStatus != null">
        service_operation_status,
      </if>
      <if test="customerFeedback != null">
        customer_feedback,
      </if>
      <if test="customerFeedbackTime != null">
        customer_feedback_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serviceOperationId != null">
        #{serviceOperationId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="suborderId != null">
        #{suborderId,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="serviceOperatorId != null">
        #{serviceOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="serviceStarttime != null">
        #{serviceStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceEndtime != null">
        #{serviceEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="nursingStarttime != null">
        #{nursingStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="nursingEndtime != null">
        #{nursingEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="machineId != null">
        #{machineId,jdbcType=VARCHAR},
      </if>
      <if test="serviceOperationStatus != null">
        #{serviceOperationStatus,jdbcType=TINYINT},
      </if>
      <if test="customerFeedback != null">
        #{customerFeedback,jdbcType=TINYINT},
      </if>
      <if test="customerFeedbackTime != null">
        #{customerFeedbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppServiceOperationRecordExample" resultType="java.lang.Integer">
    select count(*) from app_service_operation_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_service_operation_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.serviceOperationId != null">
        service_operation_id = #{record.serviceOperationId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.suborderId != null">
        suborder_id = #{record.suborderId,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceOperatorId != null">
        service_operator_id = #{record.serviceOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceStarttime != null">
        service_starttime = #{record.serviceStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.serviceEndtime != null">
        service_endtime = #{record.serviceEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.nursingStarttime != null">
        nursing_starttime = #{record.nursingStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.nursingEndtime != null">
        nursing_endtime = #{record.nursingEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=VARCHAR},
      </if>
      <if test="record.machineId != null">
        machine_id = #{record.machineId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceOperationStatus != null">
        service_operation_status = #{record.serviceOperationStatus,jdbcType=TINYINT},
      </if>
      <if test="record.customerFeedback != null">
        customer_feedback = #{record.customerFeedback,jdbcType=TINYINT},
      </if>
      <if test="record.customerFeedbackTime != null">
        customer_feedback_time = #{record.customerFeedbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_service_operation_record
    set id = #{record.id,jdbcType=BIGINT},
      service_operation_id = #{record.serviceOperationId,jdbcType=VARCHAR},
      unionid = #{record.unionid,jdbcType=VARCHAR},
      suborder_id = #{record.suborderId,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      service_operator_id = #{record.serviceOperatorId,jdbcType=VARCHAR},
      service_starttime = #{record.serviceStarttime,jdbcType=TIMESTAMP},
      service_endtime = #{record.serviceEndtime,jdbcType=TIMESTAMP},
      nursing_starttime = #{record.nursingStarttime,jdbcType=TIMESTAMP},
      nursing_endtime = #{record.nursingEndtime,jdbcType=TIMESTAMP},
      room_id = #{record.roomId,jdbcType=VARCHAR},
      machine_id = #{record.machineId,jdbcType=VARCHAR},
      service_operation_status = #{record.serviceOperationStatus,jdbcType=TINYINT},
      customer_feedback = #{record.customerFeedback,jdbcType=TINYINT},
      customer_feedback_time = #{record.customerFeedbackTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppServiceOperationRecord">
    update app_service_operation_record
    <set>
      <if test="serviceOperationId != null">
        service_operation_id = #{serviceOperationId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="suborderId != null">
        suborder_id = #{suborderId,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="serviceOperatorId != null">
        service_operator_id = #{serviceOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="serviceStarttime != null">
        service_starttime = #{serviceStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceEndtime != null">
        service_endtime = #{serviceEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="nursingStarttime != null">
        nursing_starttime = #{nursingStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="nursingEndtime != null">
        nursing_endtime = #{nursingEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="machineId != null">
        machine_id = #{machineId,jdbcType=VARCHAR},
      </if>
      <if test="serviceOperationStatus != null">
        service_operation_status = #{serviceOperationStatus,jdbcType=TINYINT},
      </if>
      <if test="customerFeedback != null">
        customer_feedback = #{customerFeedback,jdbcType=TINYINT},
      </if>
      <if test="customerFeedbackTime != null">
        customer_feedback_time = #{customerFeedbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppServiceOperationRecord">
    update app_service_operation_record
    set service_operation_id = #{serviceOperationId,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      suborder_id = #{suborderId,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=VARCHAR},
      service_operator_id = #{serviceOperatorId,jdbcType=VARCHAR},
      service_starttime = #{serviceStarttime,jdbcType=TIMESTAMP},
      service_endtime = #{serviceEndtime,jdbcType=TIMESTAMP},
      nursing_starttime = #{nursingStarttime,jdbcType=TIMESTAMP},
      nursing_endtime = #{nursingEndtime,jdbcType=TIMESTAMP},
      room_id = #{roomId,jdbcType=VARCHAR},
      machine_id = #{machineId,jdbcType=VARCHAR},
      service_operation_status = #{serviceOperationStatus,jdbcType=TINYINT},
      customer_feedback = #{customerFeedback,jdbcType=TINYINT},
      customer_feedback_time = #{customerFeedbackTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>