<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppServiceBundleSuborderMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppServiceBundleSuborder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="suborder_id" jdbcType="VARCHAR" property="suborderId" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="service_item_id" jdbcType="VARCHAR" property="serviceItemId" />
    <result column="suborder_origin_price" jdbcType="DECIMAL" property="suborderOriginPrice" />
    <result column="suborder_reduction_amount" jdbcType="DECIMAL" property="suborderReductionAmount" />
    <result column="suborder_amount" jdbcType="DECIMAL" property="suborderAmount" />
    <result column="suborder_status" jdbcType="SMALLINT" property="suborderStatus" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, suborder_id, unionid, store_id, service_item_id, suborder_origin_price, 
    suborder_reduction_amount, suborder_amount, suborder_status, create_by, create_time, 
    update_by, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppServiceBundleSuborderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_service_bundle_suborder
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_service_bundle_suborder
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_service_bundle_suborder
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppServiceBundleSuborderExample">
    delete from app_service_bundle_suborder
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppServiceBundleSuborder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_bundle_suborder (order_id, suborder_id, unionid, 
      store_id, service_item_id, suborder_origin_price, 
      suborder_reduction_amount, suborder_amount, 
      suborder_status, create_by, create_time, 
      update_by, update_time, deleted
      )
    values (#{orderId,jdbcType=VARCHAR}, #{suborderId,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{storeId,jdbcType=VARCHAR}, #{serviceItemId,jdbcType=VARCHAR}, #{suborderOriginPrice,jdbcType=DECIMAL}, 
      #{suborderReductionAmount,jdbcType=DECIMAL}, #{suborderAmount,jdbcType=DECIMAL}, 
      #{suborderStatus,jdbcType=SMALLINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppServiceBundleSuborder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_bundle_suborder
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="suborderId != null">
        suborder_id,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="serviceItemId != null">
        service_item_id,
      </if>
      <if test="suborderOriginPrice != null">
        suborder_origin_price,
      </if>
      <if test="suborderReductionAmount != null">
        suborder_reduction_amount,
      </if>
      <if test="suborderAmount != null">
        suborder_amount,
      </if>
      <if test="suborderStatus != null">
        suborder_status,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="suborderId != null">
        #{suborderId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemId != null">
        #{serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="suborderOriginPrice != null">
        #{suborderOriginPrice,jdbcType=DECIMAL},
      </if>
      <if test="suborderReductionAmount != null">
        #{suborderReductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="suborderAmount != null">
        #{suborderAmount,jdbcType=DECIMAL},
      </if>
      <if test="suborderStatus != null">
        #{suborderStatus,jdbcType=SMALLINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppServiceBundleSuborderExample" resultType="java.lang.Integer">
    select count(*) from app_service_bundle_suborder
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_service_bundle_suborder
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.suborderId != null">
        suborder_id = #{record.suborderId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItemId != null">
        service_item_id = #{record.serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="record.suborderOriginPrice != null">
        suborder_origin_price = #{record.suborderOriginPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.suborderReductionAmount != null">
        suborder_reduction_amount = #{record.suborderReductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.suborderAmount != null">
        suborder_amount = #{record.suborderAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.suborderStatus != null">
        suborder_status = #{record.suborderStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_service_bundle_suborder
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      suborder_id = #{record.suborderId,jdbcType=VARCHAR},
      unionid = #{record.unionid,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      service_item_id = #{record.serviceItemId,jdbcType=VARCHAR},
      suborder_origin_price = #{record.suborderOriginPrice,jdbcType=DECIMAL},
      suborder_reduction_amount = #{record.suborderReductionAmount,jdbcType=DECIMAL},
      suborder_amount = #{record.suborderAmount,jdbcType=DECIMAL},
      suborder_status = #{record.suborderStatus,jdbcType=SMALLINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppServiceBundleSuborder">
    update app_service_bundle_suborder
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="suborderId != null">
        suborder_id = #{suborderId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemId != null">
        service_item_id = #{serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="suborderOriginPrice != null">
        suborder_origin_price = #{suborderOriginPrice,jdbcType=DECIMAL},
      </if>
      <if test="suborderReductionAmount != null">
        suborder_reduction_amount = #{suborderReductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="suborderAmount != null">
        suborder_amount = #{suborderAmount,jdbcType=DECIMAL},
      </if>
      <if test="suborderStatus != null">
        suborder_status = #{suborderStatus,jdbcType=SMALLINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppServiceBundleSuborder">
    update app_service_bundle_suborder
    set order_id = #{orderId,jdbcType=VARCHAR},
      suborder_id = #{suborderId,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=VARCHAR},
      service_item_id = #{serviceItemId,jdbcType=VARCHAR},
      suborder_origin_price = #{suborderOriginPrice,jdbcType=DECIMAL},
      suborder_reduction_amount = #{suborderReductionAmount,jdbcType=DECIMAL},
      suborder_amount = #{suborderAmount,jdbcType=DECIMAL},
      suborder_status = #{suborderStatus,jdbcType=SMALLINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>