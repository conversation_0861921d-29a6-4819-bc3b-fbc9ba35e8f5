<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppServiceMediaRelationMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppServiceMediaRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_id" jdbcType="VARCHAR" property="serviceId" />
    <result column="media_no" jdbcType="BIGINT" property="mediaNo" />
    <result column="media_showtype" jdbcType="VARCHAR" property="mediaShowtype" />
    <result column="media_show_sort" jdbcType="TINYINT" property="mediaShowSort" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, service_id, media_no, media_showtype, media_show_sort, create_by, create_time, 
    update_by, update_time
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppServiceMediaRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_service_media_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_service_media_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_service_media_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppServiceMediaRelationExample">
    delete from app_service_media_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppServiceMediaRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_media_relation (service_id, media_no, media_showtype, 
      media_show_sort, create_by, create_time, 
      update_by, update_time)
    values (#{serviceId,jdbcType=VARCHAR}, #{mediaNo,jdbcType=BIGINT}, #{mediaShowtype,jdbcType=VARCHAR}, 
      #{mediaShowSort,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppServiceMediaRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_media_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="mediaNo != null">
        media_no,
      </if>
      <if test="mediaShowtype != null">
        media_showtype,
      </if>
      <if test="mediaShowSort != null">
        media_show_sort,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serviceId != null">
        #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="mediaNo != null">
        #{mediaNo,jdbcType=BIGINT},
      </if>
      <if test="mediaShowtype != null">
        #{mediaShowtype,jdbcType=VARCHAR},
      </if>
      <if test="mediaShowSort != null">
        #{mediaShowSort,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppServiceMediaRelationExample" resultType="java.lang.Integer">
    select count(*) from app_service_media_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_service_media_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=VARCHAR},
      </if>
      <if test="record.mediaNo != null">
        media_no = #{record.mediaNo,jdbcType=BIGINT},
      </if>
      <if test="record.mediaShowtype != null">
        media_showtype = #{record.mediaShowtype,jdbcType=VARCHAR},
      </if>
      <if test="record.mediaShowSort != null">
        media_show_sort = #{record.mediaShowSort,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_service_media_relation
    set id = #{record.id,jdbcType=BIGINT},
      service_id = #{record.serviceId,jdbcType=VARCHAR},
      media_no = #{record.mediaNo,jdbcType=BIGINT},
      media_showtype = #{record.mediaShowtype,jdbcType=VARCHAR},
      media_show_sort = #{record.mediaShowSort,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppServiceMediaRelation">
    update app_service_media_relation
    <set>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="mediaNo != null">
        media_no = #{mediaNo,jdbcType=BIGINT},
      </if>
      <if test="mediaShowtype != null">
        media_showtype = #{mediaShowtype,jdbcType=VARCHAR},
      </if>
      <if test="mediaShowSort != null">
        media_show_sort = #{mediaShowSort,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppServiceMediaRelation">
    update app_service_media_relation
    set service_id = #{serviceId,jdbcType=VARCHAR},
      media_no = #{mediaNo,jdbcType=BIGINT},
      media_showtype = #{mediaShowtype,jdbcType=VARCHAR},
      media_show_sort = #{mediaShowSort,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>