<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.MtLogMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.MtLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_time" jdbcType="TIMESTAMP" property="requestTime" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_body" jdbcType="VARCHAR" property="requestBody" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="result_resp" jdbcType="VARCHAR" property="resultResp" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, request_time, request_url, request_body, code, result_resp
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.MtLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mt_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mt_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mt_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.MtLogExample">
    delete from mt_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.MtLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_log (request_time, request_url, request_body, 
      code, result_resp)
    values (#{requestTime,jdbcType=TIMESTAMP}, #{requestUrl,jdbcType=VARCHAR}, #{requestBody,jdbcType=VARCHAR}, 
      #{code,jdbcType=VARCHAR}, #{resultResp,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.MtLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="requestTime != null">
        request_time,
      </if>
      <if test="requestUrl != null">
        request_url,
      </if>
      <if test="requestBody != null">
        request_body,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="resultResp != null">
        result_resp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="requestTime != null">
        #{requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestBody != null">
        #{requestBody,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="resultResp != null">
        #{resultResp,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.MtLogExample" resultType="java.lang.Integer">
    select count(*) from mt_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mt_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.requestTime != null">
        request_time = #{record.requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.requestUrl != null">
        request_url = #{record.requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.requestBody != null">
        request_body = #{record.requestBody,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.resultResp != null">
        result_resp = #{record.resultResp,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mt_log
    set id = #{record.id,jdbcType=BIGINT},
      request_time = #{record.requestTime,jdbcType=TIMESTAMP},
      request_url = #{record.requestUrl,jdbcType=VARCHAR},
      request_body = #{record.requestBody,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      result_resp = #{record.resultResp,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.MtLog">
    update mt_log
    <set>
      <if test="requestTime != null">
        request_time = #{requestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestUrl != null">
        request_url = #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestBody != null">
        request_body = #{requestBody,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="resultResp != null">
        result_resp = #{resultResp,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.MtLog">
    update mt_log
    set request_time = #{requestTime,jdbcType=TIMESTAMP},
      request_url = #{requestUrl,jdbcType=VARCHAR},
      request_body = #{requestBody,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      result_resp = #{resultResp,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>