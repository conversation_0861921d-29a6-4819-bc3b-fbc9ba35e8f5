<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppWhiteListMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppWhiteList">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="category" jdbcType="TINYINT" property="category" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="quota" jdbcType="INTEGER" property="quota" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unionid, category, name, phone, store_id, quota, remark, create_time, create_by, 
    update_time, update_by, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppWhiteListExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_white_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_white_list
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_white_list
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppWhiteListExample">
    delete from app_white_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppWhiteList">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_white_list (unionid, category, name, 
      phone, store_id, quota, 
      remark, create_time, create_by, 
      update_time, update_by, deleted
      )
    values (#{unionid,jdbcType=VARCHAR}, #{category,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{storeId,jdbcType=VARCHAR}, #{quota,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppWhiteList">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_white_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unionid != null">
        unionid,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="quota != null">
        quota,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="quota != null">
        #{quota,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppWhiteListExample" resultType="java.lang.Integer">
    select count(*) from app_white_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_white_list
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=TINYINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.quota != null">
        quota = #{record.quota,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_white_list
    set id = #{record.id,jdbcType=BIGINT},
      unionid = #{record.unionid,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=TINYINT},
      name = #{record.name,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      quota = #{record.quota,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppWhiteList">
    update app_white_list
    <set>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="quota != null">
        quota = #{quota,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppWhiteList">
    update app_white_list
    set unionid = #{unionid,jdbcType=VARCHAR},
      category = #{category,jdbcType=TINYINT},
      name = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=VARCHAR},
      quota = #{quota,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>