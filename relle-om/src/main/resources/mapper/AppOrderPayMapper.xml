<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppOrderPayMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppOrderPay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_source" jdbcType="VARCHAR" property="orderSource" />
    <result column="pay_system_orderid" jdbcType="VARCHAR" property="paySystemOrderid" />
    <result column="pay_end_time" jdbcType="TIMESTAMP" property="payEndTime" />
    <result column="pay_chanel_id" jdbcType="VARCHAR" property="payChanelId" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="pay_params_timeStamp" jdbcType="VARCHAR" property="payParamsTimestamp" />
    <result column="pay_params_package" jdbcType="VARCHAR" property="payParamsPackage" />
    <result column="pay_params_paySign" jdbcType="VARCHAR" property="payParamsPaysign" />
    <result column="pay_prams_appId" jdbcType="VARCHAR" property="payPramsAppid" />
    <result column="pay_params_signType" jdbcType="VARCHAR" property="payParamsSigntype" />
    <result column="pay_prarams_nonceStr" jdbcType="VARCHAR" property="payPraramsNoncestr" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_source, pay_system_orderid, pay_end_time, pay_chanel_id, pay_time, 
    pay_amount, pay_params_timeStamp, pay_params_package, pay_params_paySign, pay_prams_appId, 
    pay_params_signType, pay_prarams_nonceStr, pay_status, create_by, create_time, update_by, 
    update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppOrderPayExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_order_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_order_pay
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_order_pay
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppOrderPayExample">
    delete from app_order_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppOrderPay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_order_pay (order_id, order_source, pay_system_orderid, 
      pay_end_time, pay_chanel_id, pay_time, 
      pay_amount, pay_params_timeStamp, pay_params_package, 
      pay_params_paySign, pay_prams_appId, pay_params_signType, 
      pay_prarams_nonceStr, pay_status, create_by, 
      create_time, update_by, update_time, 
      deleted)
    values (#{orderId,jdbcType=VARCHAR}, #{orderSource,jdbcType=VARCHAR}, #{paySystemOrderid,jdbcType=VARCHAR}, 
      #{payEndTime,jdbcType=TIMESTAMP}, #{payChanelId,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP}, 
      #{payAmount,jdbcType=DECIMAL}, #{payParamsTimestamp,jdbcType=VARCHAR}, #{payParamsPackage,jdbcType=VARCHAR}, 
      #{payParamsPaysign,jdbcType=VARCHAR}, #{payPramsAppid,jdbcType=VARCHAR}, #{payParamsSigntype,jdbcType=VARCHAR}, 
      #{payPraramsNoncestr,jdbcType=VARCHAR}, #{payStatus,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppOrderPay">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_order_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="paySystemOrderid != null">
        pay_system_orderid,
      </if>
      <if test="payEndTime != null">
        pay_end_time,
      </if>
      <if test="payChanelId != null">
        pay_chanel_id,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="payParamsTimestamp != null">
        pay_params_timeStamp,
      </if>
      <if test="payParamsPackage != null">
        pay_params_package,
      </if>
      <if test="payParamsPaysign != null">
        pay_params_paySign,
      </if>
      <if test="payPramsAppid != null">
        pay_prams_appId,
      </if>
      <if test="payParamsSigntype != null">
        pay_params_signType,
      </if>
      <if test="payPraramsNoncestr != null">
        pay_prarams_nonceStr,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="paySystemOrderid != null">
        #{paySystemOrderid,jdbcType=VARCHAR},
      </if>
      <if test="payEndTime != null">
        #{payEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payChanelId != null">
        #{payChanelId,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="payParamsTimestamp != null">
        #{payParamsTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="payParamsPackage != null">
        #{payParamsPackage,jdbcType=VARCHAR},
      </if>
      <if test="payParamsPaysign != null">
        #{payParamsPaysign,jdbcType=VARCHAR},
      </if>
      <if test="payPramsAppid != null">
        #{payPramsAppid,jdbcType=VARCHAR},
      </if>
      <if test="payParamsSigntype != null">
        #{payParamsSigntype,jdbcType=VARCHAR},
      </if>
      <if test="payPraramsNoncestr != null">
        #{payPraramsNoncestr,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppOrderPayExample" resultType="java.lang.Integer">
    select count(*) from app_order_pay
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_order_pay
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=VARCHAR},
      </if>
      <if test="record.paySystemOrderid != null">
        pay_system_orderid = #{record.paySystemOrderid,jdbcType=VARCHAR},
      </if>
      <if test="record.payEndTime != null">
        pay_end_time = #{record.payEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payChanelId != null">
        pay_chanel_id = #{record.payChanelId,jdbcType=VARCHAR},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.payParamsTimestamp != null">
        pay_params_timeStamp = #{record.payParamsTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="record.payParamsPackage != null">
        pay_params_package = #{record.payParamsPackage,jdbcType=VARCHAR},
      </if>
      <if test="record.payParamsPaysign != null">
        pay_params_paySign = #{record.payParamsPaysign,jdbcType=VARCHAR},
      </if>
      <if test="record.payPramsAppid != null">
        pay_prams_appId = #{record.payPramsAppid,jdbcType=VARCHAR},
      </if>
      <if test="record.payParamsSigntype != null">
        pay_params_signType = #{record.payParamsSigntype,jdbcType=VARCHAR},
      </if>
      <if test="record.payPraramsNoncestr != null">
        pay_prarams_nonceStr = #{record.payPraramsNoncestr,jdbcType=VARCHAR},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_order_pay
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_source = #{record.orderSource,jdbcType=VARCHAR},
      pay_system_orderid = #{record.paySystemOrderid,jdbcType=VARCHAR},
      pay_end_time = #{record.payEndTime,jdbcType=TIMESTAMP},
      pay_chanel_id = #{record.payChanelId,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      pay_params_timeStamp = #{record.payParamsTimestamp,jdbcType=VARCHAR},
      pay_params_package = #{record.payParamsPackage,jdbcType=VARCHAR},
      pay_params_paySign = #{record.payParamsPaysign,jdbcType=VARCHAR},
      pay_prams_appId = #{record.payPramsAppid,jdbcType=VARCHAR},
      pay_params_signType = #{record.payParamsSigntype,jdbcType=VARCHAR},
      pay_prarams_nonceStr = #{record.payPraramsNoncestr,jdbcType=VARCHAR},
      pay_status = #{record.payStatus,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppOrderPay">
    update app_order_pay
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="paySystemOrderid != null">
        pay_system_orderid = #{paySystemOrderid,jdbcType=VARCHAR},
      </if>
      <if test="payEndTime != null">
        pay_end_time = #{payEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payChanelId != null">
        pay_chanel_id = #{payChanelId,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="payParamsTimestamp != null">
        pay_params_timeStamp = #{payParamsTimestamp,jdbcType=VARCHAR},
      </if>
      <if test="payParamsPackage != null">
        pay_params_package = #{payParamsPackage,jdbcType=VARCHAR},
      </if>
      <if test="payParamsPaysign != null">
        pay_params_paySign = #{payParamsPaysign,jdbcType=VARCHAR},
      </if>
      <if test="payPramsAppid != null">
        pay_prams_appId = #{payPramsAppid,jdbcType=VARCHAR},
      </if>
      <if test="payParamsSigntype != null">
        pay_params_signType = #{payParamsSigntype,jdbcType=VARCHAR},
      </if>
      <if test="payPraramsNoncestr != null">
        pay_prarams_nonceStr = #{payPraramsNoncestr,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppOrderPay">
    update app_order_pay
    set order_id = #{orderId,jdbcType=VARCHAR},
      order_source = #{orderSource,jdbcType=VARCHAR},
      pay_system_orderid = #{paySystemOrderid,jdbcType=VARCHAR},
      pay_end_time = #{payEndTime,jdbcType=TIMESTAMP},
      pay_chanel_id = #{payChanelId,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      pay_params_timeStamp = #{payParamsTimestamp,jdbcType=VARCHAR},
      pay_params_package = #{payParamsPackage,jdbcType=VARCHAR},
      pay_params_paySign = #{payParamsPaysign,jdbcType=VARCHAR},
      pay_prams_appId = #{payPramsAppid,jdbcType=VARCHAR},
      pay_params_signType = #{payParamsSigntype,jdbcType=VARCHAR},
      pay_prarams_nonceStr = #{payPraramsNoncestr,jdbcType=VARCHAR},
      pay_status = #{payStatus,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>