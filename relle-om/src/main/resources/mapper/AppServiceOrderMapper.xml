<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppServiceOrderMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppServiceOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="sotre_id" jdbcType="VARCHAR" property="sotreId" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="origin_price" jdbcType="DECIMAL" property="originPrice" />
    <result column="reduction_amount" jdbcType="DECIMAL" property="reductionAmount" />
    <result column="order_status" jdbcType="SMALLINT" property="orderStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="modify_num" jdbcType="TINYINT" property="modifyNum" />
    <result column="customer_source" jdbcType="VARCHAR" property="customerSource" />
    <result column="source_order_id" jdbcType="VARCHAR" property="sourceOrderId" />
    <result column="write_off" jdbcType="TINYINT" property="writeOff" />
    <result column="write_off_time" jdbcType="TIMESTAMP" property="writeOffTime" />
    <result column="write_off_user" jdbcType="VARCHAR" property="writeOffUser" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, unionid, sotre_id, room_id, order_amount, contact_name, contact_phone, 
    origin_price, reduction_amount, order_status, create_time, create_by, update_time, 
    update_by, deleted, modify_num, customer_source, source_order_id, write_off, write_off_time, 
    write_off_user
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppServiceOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_service_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_service_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_service_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppServiceOrderExample">
    delete from app_service_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppServiceOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_order (order_id, unionid, sotre_id, 
      room_id, order_amount, contact_name, 
      contact_phone, origin_price, reduction_amount, 
      order_status, create_time, create_by, 
      update_time, update_by, deleted, 
      modify_num, customer_source, source_order_id, 
      write_off, write_off_time, write_off_user
      )
    values (#{orderId,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, #{sotreId,jdbcType=VARCHAR}, 
      #{roomId,jdbcType=VARCHAR}, #{orderAmount,jdbcType=DECIMAL}, #{contactName,jdbcType=VARCHAR}, 
      #{contactPhone,jdbcType=VARCHAR}, #{originPrice,jdbcType=DECIMAL}, #{reductionAmount,jdbcType=DECIMAL}, 
      #{orderStatus,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{modifyNum,jdbcType=TINYINT}, #{customerSource,jdbcType=VARCHAR}, #{sourceOrderId,jdbcType=VARCHAR}, 
      #{writeOff,jdbcType=TINYINT}, #{writeOffTime,jdbcType=TIMESTAMP}, #{writeOffUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppServiceOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="sotreId != null">
        sotre_id,
      </if>
      <if test="roomId != null">
        room_id,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="originPrice != null">
        origin_price,
      </if>
      <if test="reductionAmount != null">
        reduction_amount,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="modifyNum != null">
        modify_num,
      </if>
      <if test="customerSource != null">
        customer_source,
      </if>
      <if test="sourceOrderId != null">
        source_order_id,
      </if>
      <if test="writeOff != null">
        write_off,
      </if>
      <if test="writeOffTime != null">
        write_off_time,
      </if>
      <if test="writeOffUser != null">
        write_off_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="sotreId != null">
        #{sotreId,jdbcType=VARCHAR},
      </if>
      <if test="roomId != null">
        #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="originPrice != null">
        #{originPrice,jdbcType=DECIMAL},
      </if>
      <if test="reductionAmount != null">
        #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="modifyNum != null">
        #{modifyNum,jdbcType=TINYINT},
      </if>
      <if test="customerSource != null">
        #{customerSource,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrderId != null">
        #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="writeOff != null">
        #{writeOff,jdbcType=TINYINT},
      </if>
      <if test="writeOffTime != null">
        #{writeOffTime,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUser != null">
        #{writeOffUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppServiceOrderExample" resultType="java.lang.Integer">
    select count(*) from app_service_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_service_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.sotreId != null">
        sotre_id = #{record.sotreId,jdbcType=VARCHAR},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAmount != null">
        order_amount = #{record.orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.originPrice != null">
        origin_price = #{record.originPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.reductionAmount != null">
        reduction_amount = #{record.reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.modifyNum != null">
        modify_num = #{record.modifyNum,jdbcType=TINYINT},
      </if>
      <if test="record.customerSource != null">
        customer_source = #{record.customerSource,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceOrderId != null">
        source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.writeOff != null">
        write_off = #{record.writeOff,jdbcType=TINYINT},
      </if>
      <if test="record.writeOffTime != null">
        write_off_time = #{record.writeOffTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.writeOffUser != null">
        write_off_user = #{record.writeOffUser,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_service_order
    set id = #{record.id,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      unionid = #{record.unionid,jdbcType=VARCHAR},
      sotre_id = #{record.sotreId,jdbcType=VARCHAR},
      room_id = #{record.roomId,jdbcType=VARCHAR},
      order_amount = #{record.orderAmount,jdbcType=DECIMAL},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      origin_price = #{record.originPrice,jdbcType=DECIMAL},
      reduction_amount = #{record.reductionAmount,jdbcType=DECIMAL},
      order_status = #{record.orderStatus,jdbcType=SMALLINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      modify_num = #{record.modifyNum,jdbcType=TINYINT},
      customer_source = #{record.customerSource,jdbcType=VARCHAR},
      source_order_id = #{record.sourceOrderId,jdbcType=VARCHAR},
      write_off = #{record.writeOff,jdbcType=TINYINT},
      write_off_time = #{record.writeOffTime,jdbcType=TIMESTAMP},
      write_off_user = #{record.writeOffUser,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppServiceOrder">
    update app_service_order
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="sotreId != null">
        sotre_id = #{sotreId,jdbcType=VARCHAR},
      </if>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="originPrice != null">
        origin_price = #{originPrice,jdbcType=DECIMAL},
      </if>
      <if test="reductionAmount != null">
        reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="modifyNum != null">
        modify_num = #{modifyNum,jdbcType=TINYINT},
      </if>
      <if test="customerSource != null">
        customer_source = #{customerSource,jdbcType=VARCHAR},
      </if>
      <if test="sourceOrderId != null">
        source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="writeOff != null">
        write_off = #{writeOff,jdbcType=TINYINT},
      </if>
      <if test="writeOffTime != null">
        write_off_time = #{writeOffTime,jdbcType=TIMESTAMP},
      </if>
      <if test="writeOffUser != null">
        write_off_user = #{writeOffUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppServiceOrder">
    update app_service_order
    set order_id = #{orderId,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      sotre_id = #{sotreId,jdbcType=VARCHAR},
      room_id = #{roomId,jdbcType=VARCHAR},
      order_amount = #{orderAmount,jdbcType=DECIMAL},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      origin_price = #{originPrice,jdbcType=DECIMAL},
      reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      order_status = #{orderStatus,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      modify_num = #{modifyNum,jdbcType=TINYINT},
      customer_source = #{customerSource,jdbcType=VARCHAR},
      source_order_id = #{sourceOrderId,jdbcType=VARCHAR},
      write_off = #{writeOff,jdbcType=TINYINT},
      write_off_time = #{writeOffTime,jdbcType=TIMESTAMP},
      write_off_user = #{writeOffUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>