<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.MtStoreMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.MtStore">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="open_shop_uuid" jdbcType="VARCHAR" property="openShopUuid" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="shop_address" jdbcType="VARCHAR" property="shopAddress" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, open_shop_uuid, shop_name, shop_address
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.MtStoreExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mt_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mt_store
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mt_store
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.MtStoreExample">
    delete from mt_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.MtStore">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_store (store_id, open_shop_uuid, shop_name, 
      shop_address)
    values (#{storeId,jdbcType=VARCHAR}, #{openShopUuid,jdbcType=VARCHAR}, #{shopName,jdbcType=VARCHAR}, 
      #{shopAddress,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.MtStore">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_store
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="openShopUuid != null">
        open_shop_uuid,
      </if>
      <if test="shopName != null">
        shop_name,
      </if>
      <if test="shopAddress != null">
        shop_address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="openShopUuid != null">
        #{openShopUuid,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="shopAddress != null">
        #{shopAddress,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.MtStoreExample" resultType="java.lang.Integer">
    select count(*) from mt_store
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mt_store
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.openShopUuid != null">
        open_shop_uuid = #{record.openShopUuid,jdbcType=VARCHAR},
      </if>
      <if test="record.shopName != null">
        shop_name = #{record.shopName,jdbcType=VARCHAR},
      </if>
      <if test="record.shopAddress != null">
        shop_address = #{record.shopAddress,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mt_store
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      open_shop_uuid = #{record.openShopUuid,jdbcType=VARCHAR},
      shop_name = #{record.shopName,jdbcType=VARCHAR},
      shop_address = #{record.shopAddress,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.MtStore">
    update mt_store
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="openShopUuid != null">
        open_shop_uuid = #{openShopUuid,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="shopAddress != null">
        shop_address = #{shopAddress,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.MtStore">
    update mt_store
    set store_id = #{storeId,jdbcType=VARCHAR},
      open_shop_uuid = #{openShopUuid,jdbcType=VARCHAR},
      shop_name = #{shopName,jdbcType=VARCHAR},
      shop_address = #{shopAddress,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>