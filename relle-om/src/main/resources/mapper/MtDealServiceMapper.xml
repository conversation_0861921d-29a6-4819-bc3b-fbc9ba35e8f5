<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.MtDealServiceMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.MtDealService">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="deal_id" jdbcType="BIGINT" property="dealId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="begin_date" jdbcType="TIMESTAMP" property="beginDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="service_item" jdbcType="VARCHAR" property="serviceItem" />
    <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, deal_id, title, begin_date, end_date, service_item, coupon_id, create_time, 
    create_by, update_time, update_by, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.MtDealServiceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mt_deal_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mt_deal_service
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mt_deal_service
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.MtDealServiceExample">
    delete from mt_deal_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.MtDealService">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_deal_service (store_id, deal_id, title, 
      begin_date, end_date, service_item, 
      coupon_id, create_time, create_by, 
      update_time, update_by, deleted
      )
    values (#{storeId,jdbcType=VARCHAR}, #{dealId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, 
      #{beginDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, #{serviceItem,jdbcType=VARCHAR}, 
      #{couponId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.MtDealService">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_deal_service
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="dealId != null">
        deal_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="beginDate != null">
        begin_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="serviceItem != null">
        service_item,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="dealId != null">
        #{dealId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="beginDate != null">
        #{beginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceItem != null">
        #{serviceItem,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.MtDealServiceExample" resultType="java.lang.Integer">
    select count(*) from mt_deal_service
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mt_deal_service
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.dealId != null">
        deal_id = #{record.dealId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.beginDate != null">
        begin_date = #{record.beginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.serviceItem != null">
        service_item = #{record.serviceItem,jdbcType=VARCHAR},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mt_deal_service
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      deal_id = #{record.dealId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      begin_date = #{record.beginDate,jdbcType=TIMESTAMP},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      service_item = #{record.serviceItem,jdbcType=VARCHAR},
      coupon_id = #{record.couponId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.MtDealService">
    update mt_deal_service
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="dealId != null">
        deal_id = #{dealId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="beginDate != null">
        begin_date = #{beginDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="serviceItem != null">
        service_item = #{serviceItem,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.MtDealService">
    update mt_deal_service
    set store_id = #{storeId,jdbcType=VARCHAR},
      deal_id = #{dealId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      begin_date = #{beginDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      service_item = #{serviceItem,jdbcType=VARCHAR},
      coupon_id = #{couponId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>