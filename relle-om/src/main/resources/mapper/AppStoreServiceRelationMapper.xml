<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppStoreServiceRelationMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppStoreServiceRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="service_item_id" jdbcType="VARCHAR" property="serviceItemId" />
    <result column="service_store_show_sort" jdbcType="TINYINT" property="serviceStoreShowSort" />
    <result column="service_item_category" jdbcType="TINYINT" property="serviceItemCategory" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, service_item_id, service_store_show_sort, service_item_category, create_by, 
    create_time, update_by, update_time
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppStoreServiceRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_store_service_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_store_service_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_store_service_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppStoreServiceRelationExample">
    delete from app_store_service_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppStoreServiceRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_store_service_relation (store_id, service_item_id, service_store_show_sort, 
      service_item_category, create_by, create_time, 
      update_by, update_time)
    values (#{storeId,jdbcType=VARCHAR}, #{serviceItemId,jdbcType=VARCHAR}, #{serviceStoreShowSort,jdbcType=TINYINT}, 
      #{serviceItemCategory,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppStoreServiceRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_store_service_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="serviceItemId != null">
        service_item_id,
      </if>
      <if test="serviceStoreShowSort != null">
        service_store_show_sort,
      </if>
      <if test="serviceItemCategory != null">
        service_item_category,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemId != null">
        #{serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="serviceStoreShowSort != null">
        #{serviceStoreShowSort,jdbcType=TINYINT},
      </if>
      <if test="serviceItemCategory != null">
        #{serviceItemCategory,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppStoreServiceRelationExample" resultType="java.lang.Integer">
    select count(*) from app_store_service_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_store_service_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceItemId != null">
        service_item_id = #{record.serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceStoreShowSort != null">
        service_store_show_sort = #{record.serviceStoreShowSort,jdbcType=TINYINT},
      </if>
      <if test="record.serviceItemCategory != null">
        service_item_category = #{record.serviceItemCategory,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_store_service_relation
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      service_item_id = #{record.serviceItemId,jdbcType=VARCHAR},
      service_store_show_sort = #{record.serviceStoreShowSort,jdbcType=TINYINT},
      service_item_category = #{record.serviceItemCategory,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppStoreServiceRelation">
    update app_store_service_relation
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemId != null">
        service_item_id = #{serviceItemId,jdbcType=VARCHAR},
      </if>
      <if test="serviceStoreShowSort != null">
        service_store_show_sort = #{serviceStoreShowSort,jdbcType=TINYINT},
      </if>
      <if test="serviceItemCategory != null">
        service_item_category = #{serviceItemCategory,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppStoreServiceRelation">
    update app_store_service_relation
    set store_id = #{storeId,jdbcType=VARCHAR},
      service_item_id = #{serviceItemId,jdbcType=VARCHAR},
      service_store_show_sort = #{serviceStoreShowSort,jdbcType=TINYINT},
      service_item_category = #{serviceItemCategory,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>