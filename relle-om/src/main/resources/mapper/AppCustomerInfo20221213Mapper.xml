<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppCustomerInfo20221213Mapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppCustomerInfo20221213">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="mini_openid" jdbcType="VARCHAR" property="miniOpenid" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="wechat_nickname" jdbcType="VARCHAR" property="wechatNickname" />
    <result column="wechat_phone" jdbcType="VARCHAR" property="wechatPhone" />
    <result column="wechat_avatar_src" jdbcType="VARCHAR" property="wechatAvatarSrc" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_gender" jdbcType="TINYINT" property="userGender" />
    <result column="user_birthdate" jdbcType="VARCHAR" property="userBirthdate" />
    <result column="user_avatar_src" jdbcType="VARCHAR" property="userAvatarSrc" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unionid, mini_openid, customer_id, wechat_nickname, wechat_phone, wechat_avatar_src, 
    user_name, user_gender, user_birthdate, user_avatar_src, create_by, create_time, 
    update_by, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppCustomerInfo20221213Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_customer_info_20221213
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_customer_info_20221213
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_customer_info_20221213
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppCustomerInfo20221213Example">
    delete from app_customer_info_20221213
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppCustomerInfo20221213">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_customer_info_20221213 (unionid, mini_openid, customer_id, 
      wechat_nickname, wechat_phone, wechat_avatar_src, 
      user_name, user_gender, user_birthdate, 
      user_avatar_src, create_by, create_time, 
      update_by, update_time, deleted
      )
    values (#{unionid,jdbcType=VARCHAR}, #{miniOpenid,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, 
      #{wechatNickname,jdbcType=VARCHAR}, #{wechatPhone,jdbcType=VARCHAR}, #{wechatAvatarSrc,jdbcType=VARCHAR}, 
      #{userName,jdbcType=VARCHAR}, #{userGender,jdbcType=TINYINT}, #{userBirthdate,jdbcType=VARCHAR}, 
      #{userAvatarSrc,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppCustomerInfo20221213">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_customer_info_20221213
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unionid != null">
        unionid,
      </if>
      <if test="miniOpenid != null">
        mini_openid,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="wechatNickname != null">
        wechat_nickname,
      </if>
      <if test="wechatPhone != null">
        wechat_phone,
      </if>
      <if test="wechatAvatarSrc != null">
        wechat_avatar_src,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userGender != null">
        user_gender,
      </if>
      <if test="userBirthdate != null">
        user_birthdate,
      </if>
      <if test="userAvatarSrc != null">
        user_avatar_src,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="miniOpenid != null">
        #{miniOpenid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="wechatNickname != null">
        #{wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="wechatPhone != null">
        #{wechatPhone,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatarSrc != null">
        #{wechatAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userGender != null">
        #{userGender,jdbcType=TINYINT},
      </if>
      <if test="userBirthdate != null">
        #{userBirthdate,jdbcType=VARCHAR},
      </if>
      <if test="userAvatarSrc != null">
        #{userAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppCustomerInfo20221213Example" resultType="java.lang.Integer">
    select count(*) from app_customer_info_20221213
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_customer_info_20221213
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.miniOpenid != null">
        mini_openid = #{record.miniOpenid,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.wechatNickname != null">
        wechat_nickname = #{record.wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="record.wechatPhone != null">
        wechat_phone = #{record.wechatPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.wechatAvatarSrc != null">
        wechat_avatar_src = #{record.wechatAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.userGender != null">
        user_gender = #{record.userGender,jdbcType=TINYINT},
      </if>
      <if test="record.userBirthdate != null">
        user_birthdate = #{record.userBirthdate,jdbcType=VARCHAR},
      </if>
      <if test="record.userAvatarSrc != null">
        user_avatar_src = #{record.userAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_customer_info_20221213
    set id = #{record.id,jdbcType=BIGINT},
      unionid = #{record.unionid,jdbcType=VARCHAR},
      mini_openid = #{record.miniOpenid,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      wechat_nickname = #{record.wechatNickname,jdbcType=VARCHAR},
      wechat_phone = #{record.wechatPhone,jdbcType=VARCHAR},
      wechat_avatar_src = #{record.wechatAvatarSrc,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      user_gender = #{record.userGender,jdbcType=TINYINT},
      user_birthdate = #{record.userBirthdate,jdbcType=VARCHAR},
      user_avatar_src = #{record.userAvatarSrc,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppCustomerInfo20221213">
    update app_customer_info_20221213
    <set>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="miniOpenid != null">
        mini_openid = #{miniOpenid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="wechatNickname != null">
        wechat_nickname = #{wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="wechatPhone != null">
        wechat_phone = #{wechatPhone,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatarSrc != null">
        wechat_avatar_src = #{wechatAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userGender != null">
        user_gender = #{userGender,jdbcType=TINYINT},
      </if>
      <if test="userBirthdate != null">
        user_birthdate = #{userBirthdate,jdbcType=VARCHAR},
      </if>
      <if test="userAvatarSrc != null">
        user_avatar_src = #{userAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppCustomerInfo20221213">
    update app_customer_info_20221213
    set unionid = #{unionid,jdbcType=VARCHAR},
      mini_openid = #{miniOpenid,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=VARCHAR},
      wechat_nickname = #{wechatNickname,jdbcType=VARCHAR},
      wechat_phone = #{wechatPhone,jdbcType=VARCHAR},
      wechat_avatar_src = #{wechatAvatarSrc,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_gender = #{userGender,jdbcType=TINYINT},
      user_birthdate = #{userBirthdate,jdbcType=VARCHAR},
      user_avatar_src = #{userAvatarSrc,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>