<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppTestMediaRelationMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppTestMediaRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="test_no" jdbcType="BIGINT" property="testNo" />
    <result column="media_no" jdbcType="BIGINT" property="mediaNo" />
    <result column="media_show_sort" jdbcType="TINYINT" property="mediaShowSort" />
    <result column="media_category" jdbcType="TINYINT" property="mediaCategory" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, test_no, media_no, media_show_sort, media_category, create_by, create_time, update_by, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppTestMediaRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_test_media_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_test_media_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_test_media_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppTestMediaRelationExample">
    delete from app_test_media_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppTestMediaRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_test_media_relation (test_no, media_no, media_show_sort, 
      media_category, create_by, create_time, 
      update_by, update_time)
    values (#{testNo,jdbcType=BIGINT}, #{mediaNo,jdbcType=BIGINT}, #{mediaShowSort,jdbcType=TINYINT}, 
      #{mediaCategory,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppTestMediaRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_test_media_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="testNo != null">
        test_no,
      </if>
      <if test="mediaNo != null">
        media_no,
      </if>
      <if test="mediaShowSort != null">
        media_show_sort,
      </if>
      <if test="mediaCategory != null">
        media_category,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="testNo != null">
        #{testNo,jdbcType=BIGINT},
      </if>
      <if test="mediaNo != null">
        #{mediaNo,jdbcType=BIGINT},
      </if>
      <if test="mediaShowSort != null">
        #{mediaShowSort,jdbcType=TINYINT},
      </if>
      <if test="mediaCategory != null">
        #{mediaCategory,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppTestMediaRelationExample" resultType="java.lang.Integer">
    select count(*) from app_test_media_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_test_media_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.testNo != null">
        test_no = #{record.testNo,jdbcType=BIGINT},
      </if>
      <if test="record.mediaNo != null">
        media_no = #{record.mediaNo,jdbcType=BIGINT},
      </if>
      <if test="record.mediaShowSort != null">
        media_show_sort = #{record.mediaShowSort,jdbcType=TINYINT},
      </if>
      <if test="record.mediaCategory != null">
        media_category = #{record.mediaCategory,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_test_media_relation
    set id = #{record.id,jdbcType=BIGINT},
      test_no = #{record.testNo,jdbcType=BIGINT},
      media_no = #{record.mediaNo,jdbcType=BIGINT},
      media_show_sort = #{record.mediaShowSort,jdbcType=TINYINT},
      media_category = #{record.mediaCategory,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppTestMediaRelation">
    update app_test_media_relation
    <set>
      <if test="testNo != null">
        test_no = #{testNo,jdbcType=BIGINT},
      </if>
      <if test="mediaNo != null">
        media_no = #{mediaNo,jdbcType=BIGINT},
      </if>
      <if test="mediaShowSort != null">
        media_show_sort = #{mediaShowSort,jdbcType=TINYINT},
      </if>
      <if test="mediaCategory != null">
        media_category = #{mediaCategory,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppTestMediaRelation">
    update app_test_media_relation
    set test_no = #{testNo,jdbcType=BIGINT},
      media_no = #{mediaNo,jdbcType=BIGINT},
      media_show_sort = #{mediaShowSort,jdbcType=TINYINT},
      media_category = #{mediaCategory,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>