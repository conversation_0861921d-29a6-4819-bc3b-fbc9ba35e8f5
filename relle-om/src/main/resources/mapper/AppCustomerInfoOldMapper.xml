<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppCustomerInfoOldMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppCustomerInfoOld">
    <id column="unionid" jdbcType="VARCHAR" property="unionid" />
    <id column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="mini_openid" jdbcType="VARCHAR" property="miniOpenid" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="wechat_nickname" jdbcType="VARCHAR" property="wechatNickname" />
    <result column="wechat_phone" jdbcType="VARCHAR" property="wechatPhone" />
    <result column="wechat_avatar_src" jdbcType="VARCHAR" property="wechatAvatarSrc" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_gender" jdbcType="TINYINT" property="userGender" />
    <result column="user_birthdate" jdbcType="VARCHAR" property="userBirthdate" />
    <result column="user_avatar_src" jdbcType="VARCHAR" property="userAvatarSrc" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    unionid, deleted, mini_openid, customer_id, wechat_nickname, wechat_phone, wechat_avatar_src, 
    user_name, user_gender, user_birthdate, user_avatar_src, create_by, create_time, 
    update_by, update_time
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppCustomerInfoOldExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_customer_info_old
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_customer_info_old
    where unionid = #{unionid,jdbcType=VARCHAR}
      and deleted = #{deleted,jdbcType=TINYINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from app_customer_info_old
    where unionid = #{unionid,jdbcType=VARCHAR}
      and deleted = #{deleted,jdbcType=TINYINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppCustomerInfoOldExample">
    delete from app_customer_info_old
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppCustomerInfoOld">
    insert into app_customer_info_old (unionid, deleted, mini_openid, 
      customer_id, wechat_nickname, wechat_phone, 
      wechat_avatar_src, user_name, user_gender, 
      user_birthdate, user_avatar_src, create_by, 
      create_time, update_by, update_time
      )
    values (#{unionid,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{miniOpenid,jdbcType=VARCHAR}, 
      #{customerId,jdbcType=VARCHAR}, #{wechatNickname,jdbcType=VARCHAR}, #{wechatPhone,jdbcType=VARCHAR}, 
      #{wechatAvatarSrc,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{userGender,jdbcType=TINYINT}, 
      #{userBirthdate,jdbcType=VARCHAR}, #{userAvatarSrc,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppCustomerInfoOld">
    insert into app_customer_info_old
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unionid != null">
        unionid,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="miniOpenid != null">
        mini_openid,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="wechatNickname != null">
        wechat_nickname,
      </if>
      <if test="wechatPhone != null">
        wechat_phone,
      </if>
      <if test="wechatAvatarSrc != null">
        wechat_avatar_src,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userGender != null">
        user_gender,
      </if>
      <if test="userBirthdate != null">
        user_birthdate,
      </if>
      <if test="userAvatarSrc != null">
        user_avatar_src,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="miniOpenid != null">
        #{miniOpenid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="wechatNickname != null">
        #{wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="wechatPhone != null">
        #{wechatPhone,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatarSrc != null">
        #{wechatAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userGender != null">
        #{userGender,jdbcType=TINYINT},
      </if>
      <if test="userBirthdate != null">
        #{userBirthdate,jdbcType=VARCHAR},
      </if>
      <if test="userAvatarSrc != null">
        #{userAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppCustomerInfoOldExample" resultType="java.lang.Integer">
    select count(*) from app_customer_info_old
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_customer_info_old
    <set>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.miniOpenid != null">
        mini_openid = #{record.miniOpenid,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.wechatNickname != null">
        wechat_nickname = #{record.wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="record.wechatPhone != null">
        wechat_phone = #{record.wechatPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.wechatAvatarSrc != null">
        wechat_avatar_src = #{record.wechatAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.userGender != null">
        user_gender = #{record.userGender,jdbcType=TINYINT},
      </if>
      <if test="record.userBirthdate != null">
        user_birthdate = #{record.userBirthdate,jdbcType=VARCHAR},
      </if>
      <if test="record.userAvatarSrc != null">
        user_avatar_src = #{record.userAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_customer_info_old
    set unionid = #{record.unionid,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=TINYINT},
      mini_openid = #{record.miniOpenid,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      wechat_nickname = #{record.wechatNickname,jdbcType=VARCHAR},
      wechat_phone = #{record.wechatPhone,jdbcType=VARCHAR},
      wechat_avatar_src = #{record.wechatAvatarSrc,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      user_gender = #{record.userGender,jdbcType=TINYINT},
      user_birthdate = #{record.userBirthdate,jdbcType=VARCHAR},
      user_avatar_src = #{record.userAvatarSrc,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppCustomerInfoOld">
    update app_customer_info_old
    <set>
      <if test="miniOpenid != null">
        mini_openid = #{miniOpenid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="wechatNickname != null">
        wechat_nickname = #{wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="wechatPhone != null">
        wechat_phone = #{wechatPhone,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatarSrc != null">
        wechat_avatar_src = #{wechatAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userGender != null">
        user_gender = #{userGender,jdbcType=TINYINT},
      </if>
      <if test="userBirthdate != null">
        user_birthdate = #{userBirthdate,jdbcType=VARCHAR},
      </if>
      <if test="userAvatarSrc != null">
        user_avatar_src = #{userAvatarSrc,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where unionid = #{unionid,jdbcType=VARCHAR}
      and deleted = #{deleted,jdbcType=TINYINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppCustomerInfoOld">
    update app_customer_info_old
    set mini_openid = #{miniOpenid,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=VARCHAR},
      wechat_nickname = #{wechatNickname,jdbcType=VARCHAR},
      wechat_phone = #{wechatPhone,jdbcType=VARCHAR},
      wechat_avatar_src = #{wechatAvatarSrc,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      user_gender = #{userGender,jdbcType=TINYINT},
      user_birthdate = #{userBirthdate,jdbcType=VARCHAR},
      user_avatar_src = #{userAvatarSrc,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where unionid = #{unionid,jdbcType=VARCHAR}
      and deleted = #{deleted,jdbcType=TINYINT}
  </update>
</mapper>