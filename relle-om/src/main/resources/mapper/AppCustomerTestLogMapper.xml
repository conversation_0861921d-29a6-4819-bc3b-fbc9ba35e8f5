<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppCustomerTestLogMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppCustomerTestLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_unionid" jdbcType="VARCHAR" property="customerUnionid" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="test_operator_id" jdbcType="VARCHAR" property="testOperatorId" />
    <result column="service_operation_record_id" jdbcType="VARCHAR" property="serviceOperationRecordId" />
    <result column="test_type" jdbcType="TINYINT" property="testType" />
    <result column="test_phase" jdbcType="TINYINT" property="testPhase" />
    <result column="test_starttime" jdbcType="TIMESTAMP" property="testStarttime" />
    <result column="test_endtime" jdbcType="TIMESTAMP" property="testEndtime" />
    <result column="report_src" jdbcType="VARCHAR" property="reportSrc" />
    <result column="report_remark" jdbcType="VARCHAR" property="reportRemark" />
    <result column="report_send" jdbcType="TINYINT" property="reportSend" />
    <result column="report_send_time" jdbcType="TIMESTAMP" property="reportSendTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="test_status" jdbcType="TINYINT" property="testStatus" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, customer_unionid, customer_id, test_operator_id, service_operation_record_id, 
    test_type, test_phase, test_starttime, test_endtime, report_src, report_remark, report_send, 
    report_send_time, create_by, create_time, update_by, update_time, test_status, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppCustomerTestLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_customer_test_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_customer_test_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_customer_test_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppCustomerTestLogExample">
    delete from app_customer_test_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppCustomerTestLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_customer_test_log (customer_unionid, customer_id, test_operator_id, 
      service_operation_record_id, test_type, test_phase, 
      test_starttime, test_endtime, report_src, 
      report_remark, report_send, report_send_time, 
      create_by, create_time, update_by, 
      update_time, test_status, deleted
      )
    values (#{customerUnionid,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, #{testOperatorId,jdbcType=VARCHAR}, 
      #{serviceOperationRecordId,jdbcType=VARCHAR}, #{testType,jdbcType=TINYINT}, #{testPhase,jdbcType=TINYINT}, 
      #{testStarttime,jdbcType=TIMESTAMP}, #{testEndtime,jdbcType=TIMESTAMP}, #{reportSrc,jdbcType=VARCHAR}, 
      #{reportRemark,jdbcType=VARCHAR}, #{reportSend,jdbcType=TINYINT}, #{reportSendTime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{testStatus,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppCustomerTestLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_customer_test_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerUnionid != null">
        customer_unionid,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="testOperatorId != null">
        test_operator_id,
      </if>
      <if test="serviceOperationRecordId != null">
        service_operation_record_id,
      </if>
      <if test="testType != null">
        test_type,
      </if>
      <if test="testPhase != null">
        test_phase,
      </if>
      <if test="testStarttime != null">
        test_starttime,
      </if>
      <if test="testEndtime != null">
        test_endtime,
      </if>
      <if test="reportSrc != null">
        report_src,
      </if>
      <if test="reportRemark != null">
        report_remark,
      </if>
      <if test="reportSend != null">
        report_send,
      </if>
      <if test="reportSendTime != null">
        report_send_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="testStatus != null">
        test_status,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerUnionid != null">
        #{customerUnionid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="testOperatorId != null">
        #{testOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="serviceOperationRecordId != null">
        #{serviceOperationRecordId,jdbcType=VARCHAR},
      </if>
      <if test="testType != null">
        #{testType,jdbcType=TINYINT},
      </if>
      <if test="testPhase != null">
        #{testPhase,jdbcType=TINYINT},
      </if>
      <if test="testStarttime != null">
        #{testStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="testEndtime != null">
        #{testEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportSrc != null">
        #{reportSrc,jdbcType=VARCHAR},
      </if>
      <if test="reportRemark != null">
        #{reportRemark,jdbcType=VARCHAR},
      </if>
      <if test="reportSend != null">
        #{reportSend,jdbcType=TINYINT},
      </if>
      <if test="reportSendTime != null">
        #{reportSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testStatus != null">
        #{testStatus,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppCustomerTestLogExample" resultType="java.lang.Integer">
    select count(*) from app_customer_test_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_customer_test_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.customerUnionid != null">
        customer_unionid = #{record.customerUnionid,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.testOperatorId != null">
        test_operator_id = #{record.testOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceOperationRecordId != null">
        service_operation_record_id = #{record.serviceOperationRecordId,jdbcType=VARCHAR},
      </if>
      <if test="record.testType != null">
        test_type = #{record.testType,jdbcType=TINYINT},
      </if>
      <if test="record.testPhase != null">
        test_phase = #{record.testPhase,jdbcType=TINYINT},
      </if>
      <if test="record.testStarttime != null">
        test_starttime = #{record.testStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.testEndtime != null">
        test_endtime = #{record.testEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reportSrc != null">
        report_src = #{record.reportSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.reportRemark != null">
        report_remark = #{record.reportRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.reportSend != null">
        report_send = #{record.reportSend,jdbcType=TINYINT},
      </if>
      <if test="record.reportSendTime != null">
        report_send_time = #{record.reportSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.testStatus != null">
        test_status = #{record.testStatus,jdbcType=TINYINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_customer_test_log
    set id = #{record.id,jdbcType=BIGINT},
      customer_unionid = #{record.customerUnionid,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      test_operator_id = #{record.testOperatorId,jdbcType=VARCHAR},
      service_operation_record_id = #{record.serviceOperationRecordId,jdbcType=VARCHAR},
      test_type = #{record.testType,jdbcType=TINYINT},
      test_phase = #{record.testPhase,jdbcType=TINYINT},
      test_starttime = #{record.testStarttime,jdbcType=TIMESTAMP},
      test_endtime = #{record.testEndtime,jdbcType=TIMESTAMP},
      report_src = #{record.reportSrc,jdbcType=VARCHAR},
      report_remark = #{record.reportRemark,jdbcType=VARCHAR},
      report_send = #{record.reportSend,jdbcType=TINYINT},
      report_send_time = #{record.reportSendTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      test_status = #{record.testStatus,jdbcType=TINYINT},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppCustomerTestLog">
    update app_customer_test_log
    <set>
      <if test="customerUnionid != null">
        customer_unionid = #{customerUnionid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="testOperatorId != null">
        test_operator_id = #{testOperatorId,jdbcType=VARCHAR},
      </if>
      <if test="serviceOperationRecordId != null">
        service_operation_record_id = #{serviceOperationRecordId,jdbcType=VARCHAR},
      </if>
      <if test="testType != null">
        test_type = #{testType,jdbcType=TINYINT},
      </if>
      <if test="testPhase != null">
        test_phase = #{testPhase,jdbcType=TINYINT},
      </if>
      <if test="testStarttime != null">
        test_starttime = #{testStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="testEndtime != null">
        test_endtime = #{testEndtime,jdbcType=TIMESTAMP},
      </if>
      <if test="reportSrc != null">
        report_src = #{reportSrc,jdbcType=VARCHAR},
      </if>
      <if test="reportRemark != null">
        report_remark = #{reportRemark,jdbcType=VARCHAR},
      </if>
      <if test="reportSend != null">
        report_send = #{reportSend,jdbcType=TINYINT},
      </if>
      <if test="reportSendTime != null">
        report_send_time = #{reportSendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testStatus != null">
        test_status = #{testStatus,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppCustomerTestLog">
    update app_customer_test_log
    set customer_unionid = #{customerUnionid,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=VARCHAR},
      test_operator_id = #{testOperatorId,jdbcType=VARCHAR},
      service_operation_record_id = #{serviceOperationRecordId,jdbcType=VARCHAR},
      test_type = #{testType,jdbcType=TINYINT},
      test_phase = #{testPhase,jdbcType=TINYINT},
      test_starttime = #{testStarttime,jdbcType=TIMESTAMP},
      test_endtime = #{testEndtime,jdbcType=TIMESTAMP},
      report_src = #{reportSrc,jdbcType=VARCHAR},
      report_remark = #{reportRemark,jdbcType=VARCHAR},
      report_send = #{reportSend,jdbcType=TINYINT},
      report_send_time = #{reportSendTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      test_status = #{testStatus,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>