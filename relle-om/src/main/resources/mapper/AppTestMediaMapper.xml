<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppTestMediaMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppTestMedia">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="media_name" jdbcType="VARCHAR" property="mediaName" />
    <result column="media_type" jdbcType="VARCHAR" property="mediaType" />
    <result column="media_size" jdbcType="BIGINT" property="mediaSize" />
    <result column="media_src" jdbcType="VARCHAR" property="mediaSrc" />
    <result column="thumbnail_src" jdbcType="VARCHAR" property="thumbnailSrc" />
    <result column="media_status" jdbcType="TINYINT" property="mediaStatus" />
    <result column="md5_sum" jdbcType="VARCHAR" property="md5Sum" />
    <result column="media_showname" jdbcType="VARCHAR" property="mediaShowname" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, media_name, media_type, media_size, media_src, thumbnail_src, media_status, md5_sum, 
    media_showname, create_by, create_time, update_by, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppTestMediaExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_test_media
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_test_media
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_test_media
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppTestMediaExample">
    delete from app_test_media
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppTestMedia">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_test_media (media_name, media_type, media_size, 
      media_src, thumbnail_src, media_status, 
      md5_sum, media_showname, create_by, 
      create_time, update_by, update_time, 
      deleted)
    values (#{mediaName,jdbcType=VARCHAR}, #{mediaType,jdbcType=VARCHAR}, #{mediaSize,jdbcType=BIGINT}, 
      #{mediaSrc,jdbcType=VARCHAR}, #{thumbnailSrc,jdbcType=VARCHAR}, #{mediaStatus,jdbcType=TINYINT}, 
      #{md5Sum,jdbcType=VARCHAR}, #{mediaShowname,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppTestMedia">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_test_media
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mediaName != null">
        media_name,
      </if>
      <if test="mediaType != null">
        media_type,
      </if>
      <if test="mediaSize != null">
        media_size,
      </if>
      <if test="mediaSrc != null">
        media_src,
      </if>
      <if test="thumbnailSrc != null">
        thumbnail_src,
      </if>
      <if test="mediaStatus != null">
        media_status,
      </if>
      <if test="md5Sum != null">
        md5_sum,
      </if>
      <if test="mediaShowname != null">
        media_showname,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mediaName != null">
        #{mediaName,jdbcType=VARCHAR},
      </if>
      <if test="mediaType != null">
        #{mediaType,jdbcType=VARCHAR},
      </if>
      <if test="mediaSize != null">
        #{mediaSize,jdbcType=BIGINT},
      </if>
      <if test="mediaSrc != null">
        #{mediaSrc,jdbcType=VARCHAR},
      </if>
      <if test="thumbnailSrc != null">
        #{thumbnailSrc,jdbcType=VARCHAR},
      </if>
      <if test="mediaStatus != null">
        #{mediaStatus,jdbcType=TINYINT},
      </if>
      <if test="md5Sum != null">
        #{md5Sum,jdbcType=VARCHAR},
      </if>
      <if test="mediaShowname != null">
        #{mediaShowname,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppTestMediaExample" resultType="java.lang.Integer">
    select count(*) from app_test_media
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_test_media
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mediaName != null">
        media_name = #{record.mediaName,jdbcType=VARCHAR},
      </if>
      <if test="record.mediaType != null">
        media_type = #{record.mediaType,jdbcType=VARCHAR},
      </if>
      <if test="record.mediaSize != null">
        media_size = #{record.mediaSize,jdbcType=BIGINT},
      </if>
      <if test="record.mediaSrc != null">
        media_src = #{record.mediaSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.thumbnailSrc != null">
        thumbnail_src = #{record.thumbnailSrc,jdbcType=VARCHAR},
      </if>
      <if test="record.mediaStatus != null">
        media_status = #{record.mediaStatus,jdbcType=TINYINT},
      </if>
      <if test="record.md5Sum != null">
        md5_sum = #{record.md5Sum,jdbcType=VARCHAR},
      </if>
      <if test="record.mediaShowname != null">
        media_showname = #{record.mediaShowname,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_test_media
    set id = #{record.id,jdbcType=BIGINT},
      media_name = #{record.mediaName,jdbcType=VARCHAR},
      media_type = #{record.mediaType,jdbcType=VARCHAR},
      media_size = #{record.mediaSize,jdbcType=BIGINT},
      media_src = #{record.mediaSrc,jdbcType=VARCHAR},
      thumbnail_src = #{record.thumbnailSrc,jdbcType=VARCHAR},
      media_status = #{record.mediaStatus,jdbcType=TINYINT},
      md5_sum = #{record.md5Sum,jdbcType=VARCHAR},
      media_showname = #{record.mediaShowname,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppTestMedia">
    update app_test_media
    <set>
      <if test="mediaName != null">
        media_name = #{mediaName,jdbcType=VARCHAR},
      </if>
      <if test="mediaType != null">
        media_type = #{mediaType,jdbcType=VARCHAR},
      </if>
      <if test="mediaSize != null">
        media_size = #{mediaSize,jdbcType=BIGINT},
      </if>
      <if test="mediaSrc != null">
        media_src = #{mediaSrc,jdbcType=VARCHAR},
      </if>
      <if test="thumbnailSrc != null">
        thumbnail_src = #{thumbnailSrc,jdbcType=VARCHAR},
      </if>
      <if test="mediaStatus != null">
        media_status = #{mediaStatus,jdbcType=TINYINT},
      </if>
      <if test="md5Sum != null">
        md5_sum = #{md5Sum,jdbcType=VARCHAR},
      </if>
      <if test="mediaShowname != null">
        media_showname = #{mediaShowname,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppTestMedia">
    update app_test_media
    set media_name = #{mediaName,jdbcType=VARCHAR},
      media_type = #{mediaType,jdbcType=VARCHAR},
      media_size = #{mediaSize,jdbcType=BIGINT},
      media_src = #{mediaSrc,jdbcType=VARCHAR},
      thumbnail_src = #{thumbnailSrc,jdbcType=VARCHAR},
      media_status = #{mediaStatus,jdbcType=TINYINT},
      md5_sum = #{md5Sum,jdbcType=VARCHAR},
      media_showname = #{mediaShowname,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>