<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.MtSessionCopyMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.MtSessionCopy">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="expires_in" jdbcType="TIMESTAMP" property="expiresIn" />
    <result column="tokenType" jdbcType="VARCHAR" property="tokentype" />
    <result column="scope" jdbcType="VARCHAR" property="scope" />
    <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken" />
    <result column="remain_refresh_count" jdbcType="INTEGER" property="remainRefreshCount" />
    <result column="bid" jdbcType="VARCHAR" property="bid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, access_token, expires_in, tokenType, scope, refresh_token, remain_refresh_count, 
    bid, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.MtSessionCopyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mt_session_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mt_session_copy
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mt_session_copy
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.MtSessionCopyExample">
    delete from mt_session_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.MtSessionCopy">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_session_copy (access_token, expires_in, tokenType, 
      scope, refresh_token, remain_refresh_count, 
      bid, create_time, update_time
      )
    values (#{accessToken,jdbcType=VARCHAR}, #{expiresIn,jdbcType=TIMESTAMP}, #{tokentype,jdbcType=VARCHAR}, 
      #{scope,jdbcType=VARCHAR}, #{refreshToken,jdbcType=VARCHAR}, #{remainRefreshCount,jdbcType=INTEGER}, 
      #{bid,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.MtSessionCopy">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mt_session_copy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accessToken != null">
        access_token,
      </if>
      <if test="expiresIn != null">
        expires_in,
      </if>
      <if test="tokentype != null">
        tokenType,
      </if>
      <if test="scope != null">
        scope,
      </if>
      <if test="refreshToken != null">
        refresh_token,
      </if>
      <if test="remainRefreshCount != null">
        remain_refresh_count,
      </if>
      <if test="bid != null">
        bid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accessToken != null">
        #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="expiresIn != null">
        #{expiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="tokentype != null">
        #{tokentype,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="remainRefreshCount != null">
        #{remainRefreshCount,jdbcType=INTEGER},
      </if>
      <if test="bid != null">
        #{bid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.MtSessionCopyExample" resultType="java.lang.Integer">
    select count(*) from mt_session_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mt_session_copy
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accessToken != null">
        access_token = #{record.accessToken,jdbcType=VARCHAR},
      </if>
      <if test="record.expiresIn != null">
        expires_in = #{record.expiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tokentype != null">
        tokenType = #{record.tokentype,jdbcType=VARCHAR},
      </if>
      <if test="record.scope != null">
        scope = #{record.scope,jdbcType=VARCHAR},
      </if>
      <if test="record.refreshToken != null">
        refresh_token = #{record.refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="record.remainRefreshCount != null">
        remain_refresh_count = #{record.remainRefreshCount,jdbcType=INTEGER},
      </if>
      <if test="record.bid != null">
        bid = #{record.bid,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mt_session_copy
    set id = #{record.id,jdbcType=INTEGER},
      access_token = #{record.accessToken,jdbcType=VARCHAR},
      expires_in = #{record.expiresIn,jdbcType=TIMESTAMP},
      tokenType = #{record.tokentype,jdbcType=VARCHAR},
      scope = #{record.scope,jdbcType=VARCHAR},
      refresh_token = #{record.refreshToken,jdbcType=VARCHAR},
      remain_refresh_count = #{record.remainRefreshCount,jdbcType=INTEGER},
      bid = #{record.bid,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.MtSessionCopy">
    update mt_session_copy
    <set>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="expiresIn != null">
        expires_in = #{expiresIn,jdbcType=TIMESTAMP},
      </if>
      <if test="tokentype != null">
        tokenType = #{tokentype,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        scope = #{scope,jdbcType=VARCHAR},
      </if>
      <if test="refreshToken != null">
        refresh_token = #{refreshToken,jdbcType=VARCHAR},
      </if>
      <if test="remainRefreshCount != null">
        remain_refresh_count = #{remainRefreshCount,jdbcType=INTEGER},
      </if>
      <if test="bid != null">
        bid = #{bid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.MtSessionCopy">
    update mt_session_copy
    set access_token = #{accessToken,jdbcType=VARCHAR},
      expires_in = #{expiresIn,jdbcType=TIMESTAMP},
      tokenType = #{tokentype,jdbcType=VARCHAR},
      scope = #{scope,jdbcType=VARCHAR},
      refresh_token = #{refreshToken,jdbcType=VARCHAR},
      remain_refresh_count = #{remainRefreshCount,jdbcType=INTEGER},
      bid = #{bid,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>