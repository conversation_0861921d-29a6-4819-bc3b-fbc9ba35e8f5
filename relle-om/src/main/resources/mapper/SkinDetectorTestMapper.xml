<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.SkinDetectorTestMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.SkinDetectorTest">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="report_id" jdbcType="INTEGER" property="reportId" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="skin_test_time" jdbcType="TIMESTAMP" property="skinTestTime" />
    <result column="pic_save_path" jdbcType="VARCHAR" property="picSavePath" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, report_id, store_id, uuid, unionid, customer_id, skin_test_time, pic_save_path, 
    create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.SkinDetectorTestExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from skin_detector_test
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from skin_detector_test
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from skin_detector_test
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.SkinDetectorTestExample">
    delete from skin_detector_test
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.SkinDetectorTest">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into skin_detector_test (report_id, store_id, uuid, 
      unionid, customer_id, skin_test_time, 
      pic_save_path, create_by, create_time, 
      update_by, update_time)
    values (#{reportId,jdbcType=INTEGER}, #{storeId,jdbcType=VARCHAR}, #{uuid,jdbcType=VARCHAR}, 
      #{unionid,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, #{skinTestTime,jdbcType=TIMESTAMP}, 
      #{picSavePath,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.SkinDetectorTest">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into skin_detector_test
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reportId != null">
        report_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="uuid != null">
        uuid,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="skinTestTime != null">
        skin_test_time,
      </if>
      <if test="picSavePath != null">
        pic_save_path,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reportId != null">
        #{reportId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="skinTestTime != null">
        #{skinTestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="picSavePath != null">
        #{picSavePath,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.SkinDetectorTestExample" resultType="java.lang.Integer">
    select count(*) from skin_detector_test
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update skin_detector_test
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.reportId != null">
        report_id = #{record.reportId,jdbcType=INTEGER},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.uuid != null">
        uuid = #{record.uuid,jdbcType=VARCHAR},
      </if>
      <if test="record.unionid != null">
        unionid = #{record.unionid,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.skinTestTime != null">
        skin_test_time = #{record.skinTestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.picSavePath != null">
        pic_save_path = #{record.picSavePath,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update skin_detector_test
    set id = #{record.id,jdbcType=BIGINT},
      report_id = #{record.reportId,jdbcType=INTEGER},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      uuid = #{record.uuid,jdbcType=VARCHAR},
      unionid = #{record.unionid,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      skin_test_time = #{record.skinTestTime,jdbcType=TIMESTAMP},
      pic_save_path = #{record.picSavePath,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.SkinDetectorTest">
    update skin_detector_test
    <set>
      <if test="reportId != null">
        report_id = #{reportId,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="skinTestTime != null">
        skin_test_time = #{skinTestTime,jdbcType=TIMESTAMP},
      </if>
      <if test="picSavePath != null">
        pic_save_path = #{picSavePath,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.SkinDetectorTest">
    update skin_detector_test
    set report_id = #{reportId,jdbcType=INTEGER},
      store_id = #{storeId,jdbcType=VARCHAR},
      uuid = #{uuid,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=VARCHAR},
      skin_test_time = #{skinTestTime,jdbcType=TIMESTAMP},
      pic_save_path = #{picSavePath,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>