<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppEmployeeMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppEmployee">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="employee_open_id" jdbcType="VARCHAR" property="employeeOpenId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_phone" jdbcType="VARCHAR" property="employeePhone" />
    <result column="employee_idcard" jdbcType="VARCHAR" property="employeeIdcard" />
    <result column="employee_note" jdbcType="VARCHAR" property="employeeNote" />
    <result column="employee_status" jdbcType="TINYINT" property="employeeStatus" />
    <result column="department_id" jdbcType="TINYINT" property="departmentId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, employee_id, employee_open_id, employee_name, employee_phone, employee_idcard, 
    employee_note, employee_status, department_id, create_by, create_time, update_by, 
    update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppEmployeeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_employee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_employee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppEmployeeExample">
    delete from app_employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppEmployee">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_employee (employee_id, employee_open_id, employee_name, 
      employee_phone, employee_idcard, employee_note, 
      employee_status, department_id, create_by, 
      create_time, update_by, update_time, 
      deleted)
    values (#{employeeId,jdbcType=VARCHAR}, #{employeeOpenId,jdbcType=VARCHAR}, #{employeeName,jdbcType=VARCHAR}, 
      #{employeePhone,jdbcType=VARCHAR}, #{employeeIdcard,jdbcType=VARCHAR}, #{employeeNote,jdbcType=VARCHAR}, 
      #{employeeStatus,jdbcType=TINYINT}, #{departmentId,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppEmployee">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_employee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeOpenId != null">
        employee_open_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="employeePhone != null">
        employee_phone,
      </if>
      <if test="employeeIdcard != null">
        employee_idcard,
      </if>
      <if test="employeeNote != null">
        employee_note,
      </if>
      <if test="employeeStatus != null">
        employee_status,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeOpenId != null">
        #{employeeOpenId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeIdcard != null">
        #{employeeIdcard,jdbcType=VARCHAR},
      </if>
      <if test="employeeNote != null">
        #{employeeNote,jdbcType=VARCHAR},
      </if>
      <if test="employeeStatus != null">
        #{employeeStatus,jdbcType=TINYINT},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppEmployeeExample" resultType="java.lang.Integer">
    select count(*) from app_employee
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_employee
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeOpenId != null">
        employee_open_id = #{record.employeeOpenId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeName != null">
        employee_name = #{record.employeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.employeePhone != null">
        employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeIdcard != null">
        employee_idcard = #{record.employeeIdcard,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeNote != null">
        employee_note = #{record.employeeNote,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeStatus != null">
        employee_status = #{record.employeeStatus,jdbcType=TINYINT},
      </if>
      <if test="record.departmentId != null">
        department_id = #{record.departmentId,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_employee
    set id = #{record.id,jdbcType=BIGINT},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      employee_open_id = #{record.employeeOpenId,jdbcType=VARCHAR},
      employee_name = #{record.employeeName,jdbcType=VARCHAR},
      employee_phone = #{record.employeePhone,jdbcType=VARCHAR},
      employee_idcard = #{record.employeeIdcard,jdbcType=VARCHAR},
      employee_note = #{record.employeeNote,jdbcType=VARCHAR},
      employee_status = #{record.employeeStatus,jdbcType=TINYINT},
      department_id = #{record.departmentId,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppEmployee">
    update app_employee
    <set>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="employeeOpenId != null">
        employee_open_id = #{employeeOpenId,jdbcType=VARCHAR},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="employeePhone != null">
        employee_phone = #{employeePhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeIdcard != null">
        employee_idcard = #{employeeIdcard,jdbcType=VARCHAR},
      </if>
      <if test="employeeNote != null">
        employee_note = #{employeeNote,jdbcType=VARCHAR},
      </if>
      <if test="employeeStatus != null">
        employee_status = #{employeeStatus,jdbcType=TINYINT},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppEmployee">
    update app_employee
    set employee_id = #{employeeId,jdbcType=VARCHAR},
      employee_open_id = #{employeeOpenId,jdbcType=VARCHAR},
      employee_name = #{employeeName,jdbcType=VARCHAR},
      employee_phone = #{employeePhone,jdbcType=VARCHAR},
      employee_idcard = #{employeeIdcard,jdbcType=VARCHAR},
      employee_note = #{employeeNote,jdbcType=VARCHAR},
      employee_status = #{employeeStatus,jdbcType=TINYINT},
      department_id = #{departmentId,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>