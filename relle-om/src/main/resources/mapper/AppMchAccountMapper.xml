<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppMchAccountMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppMchAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="mch_id" jdbcType="VARCHAR" property="mchId" />
    <result column="certificate_serial_number" jdbcType="VARCHAR" property="certificateSerialNumber" />
    <result column="api_v3key" jdbcType="VARCHAR" property="apiV3key" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.relle.mbg.model.AppMchAccount">
    <result column="apiclient_key" jdbcType="LONGVARCHAR" property="apiclientKey" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, mch_id, certificate_serial_number, api_v3key
  </sql>
  <sql id="Blob_Column_List">
    apiclient_key
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.relle.mbg.model.AppMchAccountExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from app_mch_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppMchAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_mch_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from app_mch_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_mch_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppMchAccountExample">
    delete from app_mch_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppMchAccount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_mch_account (store_id, mch_id, certificate_serial_number, 
      api_v3key, apiclient_key)
    values (#{storeId,jdbcType=VARCHAR}, #{mchId,jdbcType=VARCHAR}, #{certificateSerialNumber,jdbcType=VARCHAR}, 
      #{apiV3key,jdbcType=VARCHAR}, #{apiclientKey,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppMchAccount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_mch_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="mchId != null">
        mch_id,
      </if>
      <if test="certificateSerialNumber != null">
        certificate_serial_number,
      </if>
      <if test="apiV3key != null">
        api_v3key,
      </if>
      <if test="apiclientKey != null">
        apiclient_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="mchId != null">
        #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="certificateSerialNumber != null">
        #{certificateSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="apiV3key != null">
        #{apiV3key,jdbcType=VARCHAR},
      </if>
      <if test="apiclientKey != null">
        #{apiclientKey,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppMchAccountExample" resultType="java.lang.Integer">
    select count(*) from app_mch_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_mch_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=VARCHAR},
      </if>
      <if test="record.mchId != null">
        mch_id = #{record.mchId,jdbcType=VARCHAR},
      </if>
      <if test="record.certificateSerialNumber != null">
        certificate_serial_number = #{record.certificateSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.apiV3key != null">
        api_v3key = #{record.apiV3key,jdbcType=VARCHAR},
      </if>
      <if test="record.apiclientKey != null">
        apiclient_key = #{record.apiclientKey,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update app_mch_account
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      mch_id = #{record.mchId,jdbcType=VARCHAR},
      certificate_serial_number = #{record.certificateSerialNumber,jdbcType=VARCHAR},
      api_v3key = #{record.apiV3key,jdbcType=VARCHAR},
      apiclient_key = #{record.apiclientKey,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_mch_account
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=VARCHAR},
      mch_id = #{record.mchId,jdbcType=VARCHAR},
      certificate_serial_number = #{record.certificateSerialNumber,jdbcType=VARCHAR},
      api_v3key = #{record.apiV3key,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppMchAccount">
    update app_mch_account
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=VARCHAR},
      </if>
      <if test="mchId != null">
        mch_id = #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="certificateSerialNumber != null">
        certificate_serial_number = #{certificateSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="apiV3key != null">
        api_v3key = #{apiV3key,jdbcType=VARCHAR},
      </if>
      <if test="apiclientKey != null">
        apiclient_key = #{apiclientKey,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.relle.mbg.model.AppMchAccount">
    update app_mch_account
    set store_id = #{storeId,jdbcType=VARCHAR},
      mch_id = #{mchId,jdbcType=VARCHAR},
      certificate_serial_number = #{certificateSerialNumber,jdbcType=VARCHAR},
      api_v3key = #{apiV3key,jdbcType=VARCHAR},
      apiclient_key = #{apiclientKey,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppMchAccount">
    update app_mch_account
    set store_id = #{storeId,jdbcType=VARCHAR},
      mch_id = #{mchId,jdbcType=VARCHAR},
      certificate_serial_number = #{certificateSerialNumber,jdbcType=VARCHAR},
      api_v3key = #{apiV3key,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>