<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.AppServiceItemMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.AppServiceItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="service_id" jdbcType="VARCHAR" property="serviceId" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="service_details" jdbcType="VARCHAR" property="serviceDetails" />
    <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
    <result column="service_tagtree" jdbcType="VARCHAR" property="serviceTagtree" />
    <result column="service_content" jdbcType="VARCHAR" property="serviceContent" />
    <result column="service_duration_sec" jdbcType="INTEGER" property="serviceDurationSec" />
    <result column="service_preparation_sec" jdbcType="INTEGER" property="servicePreparationSec" />
    <result column="service_closing_sec" jdbcType="INTEGER" property="serviceClosingSec" />
    <result column="service_status" jdbcType="TINYINT" property="serviceStatus" />
    <result column="service_type" jdbcType="VARCHAR" property="serviceType" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="service_show_sort" jdbcType="TINYINT" property="serviceShowSort" />
    <result column="can_add_item" jdbcType="TINYINT" property="canAddItem" />
    <result column="restricted_role" jdbcType="TINYINT" property="restrictedRole" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, service_id, service_name, service_details, service_price, service_tagtree, service_content, 
    service_duration_sec, service_preparation_sec, service_closing_sec, service_status, 
    service_type, channel_type, service_show_sort, can_add_item, restricted_role, create_by, 
    create_time, update_by, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.AppServiceItemExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from app_service_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_service_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from app_service_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.AppServiceItemExample">
    delete from app_service_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.AppServiceItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_item (service_id, service_name, service_details, 
      service_price, service_tagtree, service_content, 
      service_duration_sec, service_preparation_sec, 
      service_closing_sec, service_status, service_type, 
      channel_type, service_show_sort, can_add_item, 
      restricted_role, create_by, create_time, 
      update_by, update_time, deleted
      )
    values (#{serviceId,jdbcType=VARCHAR}, #{serviceName,jdbcType=VARCHAR}, #{serviceDetails,jdbcType=VARCHAR}, 
      #{servicePrice,jdbcType=DECIMAL}, #{serviceTagtree,jdbcType=VARCHAR}, #{serviceContent,jdbcType=VARCHAR}, 
      #{serviceDurationSec,jdbcType=INTEGER}, #{servicePreparationSec,jdbcType=INTEGER}, 
      #{serviceClosingSec,jdbcType=INTEGER}, #{serviceStatus,jdbcType=TINYINT}, #{serviceType,jdbcType=VARCHAR}, 
      #{channelType,jdbcType=TINYINT}, #{serviceShowSort,jdbcType=TINYINT}, #{canAddItem,jdbcType=TINYINT}, 
      #{restrictedRole,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.AppServiceItem">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into app_service_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serviceId != null">
        service_id,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="serviceDetails != null">
        service_details,
      </if>
      <if test="servicePrice != null">
        service_price,
      </if>
      <if test="serviceTagtree != null">
        service_tagtree,
      </if>
      <if test="serviceContent != null">
        service_content,
      </if>
      <if test="serviceDurationSec != null">
        service_duration_sec,
      </if>
      <if test="servicePreparationSec != null">
        service_preparation_sec,
      </if>
      <if test="serviceClosingSec != null">
        service_closing_sec,
      </if>
      <if test="serviceStatus != null">
        service_status,
      </if>
      <if test="serviceType != null">
        service_type,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="serviceShowSort != null">
        service_show_sort,
      </if>
      <if test="canAddItem != null">
        can_add_item,
      </if>
      <if test="restrictedRole != null">
        restricted_role,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serviceId != null">
        #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="serviceDetails != null">
        #{serviceDetails,jdbcType=VARCHAR},
      </if>
      <if test="servicePrice != null">
        #{servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceTagtree != null">
        #{serviceTagtree,jdbcType=VARCHAR},
      </if>
      <if test="serviceContent != null">
        #{serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="serviceDurationSec != null">
        #{serviceDurationSec,jdbcType=INTEGER},
      </if>
      <if test="servicePreparationSec != null">
        #{servicePreparationSec,jdbcType=INTEGER},
      </if>
      <if test="serviceClosingSec != null">
        #{serviceClosingSec,jdbcType=INTEGER},
      </if>
      <if test="serviceStatus != null">
        #{serviceStatus,jdbcType=TINYINT},
      </if>
      <if test="serviceType != null">
        #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=TINYINT},
      </if>
      <if test="serviceShowSort != null">
        #{serviceShowSort,jdbcType=TINYINT},
      </if>
      <if test="canAddItem != null">
        #{canAddItem,jdbcType=TINYINT},
      </if>
      <if test="restrictedRole != null">
        #{restrictedRole,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.AppServiceItemExample" resultType="java.lang.Integer">
    select count(*) from app_service_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update app_service_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.serviceId != null">
        service_id = #{record.serviceId,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceName != null">
        service_name = #{record.serviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceDetails != null">
        service_details = #{record.serviceDetails,jdbcType=VARCHAR},
      </if>
      <if test="record.servicePrice != null">
        service_price = #{record.servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.serviceTagtree != null">
        service_tagtree = #{record.serviceTagtree,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceContent != null">
        service_content = #{record.serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceDurationSec != null">
        service_duration_sec = #{record.serviceDurationSec,jdbcType=INTEGER},
      </if>
      <if test="record.servicePreparationSec != null">
        service_preparation_sec = #{record.servicePreparationSec,jdbcType=INTEGER},
      </if>
      <if test="record.serviceClosingSec != null">
        service_closing_sec = #{record.serviceClosingSec,jdbcType=INTEGER},
      </if>
      <if test="record.serviceStatus != null">
        service_status = #{record.serviceStatus,jdbcType=TINYINT},
      </if>
      <if test="record.serviceType != null">
        service_type = #{record.serviceType,jdbcType=VARCHAR},
      </if>
      <if test="record.channelType != null">
        channel_type = #{record.channelType,jdbcType=TINYINT},
      </if>
      <if test="record.serviceShowSort != null">
        service_show_sort = #{record.serviceShowSort,jdbcType=TINYINT},
      </if>
      <if test="record.canAddItem != null">
        can_add_item = #{record.canAddItem,jdbcType=TINYINT},
      </if>
      <if test="record.restrictedRole != null">
        restricted_role = #{record.restrictedRole,jdbcType=TINYINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update app_service_item
    set id = #{record.id,jdbcType=BIGINT},
      service_id = #{record.serviceId,jdbcType=VARCHAR},
      service_name = #{record.serviceName,jdbcType=VARCHAR},
      service_details = #{record.serviceDetails,jdbcType=VARCHAR},
      service_price = #{record.servicePrice,jdbcType=DECIMAL},
      service_tagtree = #{record.serviceTagtree,jdbcType=VARCHAR},
      service_content = #{record.serviceContent,jdbcType=VARCHAR},
      service_duration_sec = #{record.serviceDurationSec,jdbcType=INTEGER},
      service_preparation_sec = #{record.servicePreparationSec,jdbcType=INTEGER},
      service_closing_sec = #{record.serviceClosingSec,jdbcType=INTEGER},
      service_status = #{record.serviceStatus,jdbcType=TINYINT},
      service_type = #{record.serviceType,jdbcType=VARCHAR},
      channel_type = #{record.channelType,jdbcType=TINYINT},
      service_show_sort = #{record.serviceShowSort,jdbcType=TINYINT},
      can_add_item = #{record.canAddItem,jdbcType=TINYINT},
      restricted_role = #{record.restrictedRole,jdbcType=TINYINT},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.AppServiceItem">
    update app_service_item
    <set>
      <if test="serviceId != null">
        service_id = #{serviceId,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="serviceDetails != null">
        service_details = #{serviceDetails,jdbcType=VARCHAR},
      </if>
      <if test="servicePrice != null">
        service_price = #{servicePrice,jdbcType=DECIMAL},
      </if>
      <if test="serviceTagtree != null">
        service_tagtree = #{serviceTagtree,jdbcType=VARCHAR},
      </if>
      <if test="serviceContent != null">
        service_content = #{serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="serviceDurationSec != null">
        service_duration_sec = #{serviceDurationSec,jdbcType=INTEGER},
      </if>
      <if test="servicePreparationSec != null">
        service_preparation_sec = #{servicePreparationSec,jdbcType=INTEGER},
      </if>
      <if test="serviceClosingSec != null">
        service_closing_sec = #{serviceClosingSec,jdbcType=INTEGER},
      </if>
      <if test="serviceStatus != null">
        service_status = #{serviceStatus,jdbcType=TINYINT},
      </if>
      <if test="serviceType != null">
        service_type = #{serviceType,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=TINYINT},
      </if>
      <if test="serviceShowSort != null">
        service_show_sort = #{serviceShowSort,jdbcType=TINYINT},
      </if>
      <if test="canAddItem != null">
        can_add_item = #{canAddItem,jdbcType=TINYINT},
      </if>
      <if test="restrictedRole != null">
        restricted_role = #{restrictedRole,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.AppServiceItem">
    update app_service_item
    set service_id = #{serviceId,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      service_details = #{serviceDetails,jdbcType=VARCHAR},
      service_price = #{servicePrice,jdbcType=DECIMAL},
      service_tagtree = #{serviceTagtree,jdbcType=VARCHAR},
      service_content = #{serviceContent,jdbcType=VARCHAR},
      service_duration_sec = #{serviceDurationSec,jdbcType=INTEGER},
      service_preparation_sec = #{servicePreparationSec,jdbcType=INTEGER},
      service_closing_sec = #{serviceClosingSec,jdbcType=INTEGER},
      service_status = #{serviceStatus,jdbcType=TINYINT},
      service_type = #{serviceType,jdbcType=VARCHAR},
      channel_type = #{channelType,jdbcType=TINYINT},
      service_show_sort = #{serviceShowSort,jdbcType=TINYINT},
      can_add_item = #{canAddItem,jdbcType=TINYINT},
      restricted_role = #{restrictedRole,jdbcType=TINYINT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>