<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.relle.mbg.mapper.SysTaskMapper">
  <resultMap id="BaseResultMap" type="com.relle.mbg.model.SysTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="cron_expression" jdbcType="VARCHAR" property="cronExpression" />
    <result column="bean_class" jdbcType="VARCHAR" property="beanClass" />
    <result column="job_status" jdbcType="VARCHAR" property="jobStatus" />
    <result column="job_group" jdbcType="VARCHAR" property="jobGroup" />
    <result column="job_params" jdbcType="VARCHAR" property="jobParams" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, job_name, description, cron_expression, bean_class, job_status, job_group, job_params, 
    create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByExample" parameterType="com.relle.mbg.model.SysTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sys_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sys_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.relle.mbg.model.SysTaskExample">
    delete from sys_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.relle.mbg.model.SysTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_task (job_name, description, cron_expression, 
      bean_class, job_status, job_group, 
      job_params, create_user, create_time, 
      update_user, update_time)
    values (#{jobName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{cronExpression,jdbcType=VARCHAR}, 
      #{beanClass,jdbcType=VARCHAR}, #{jobStatus,jdbcType=VARCHAR}, #{jobGroup,jdbcType=VARCHAR}, 
      #{jobParams,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.relle.mbg.model.SysTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jobName != null">
        job_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="cronExpression != null">
        cron_expression,
      </if>
      <if test="beanClass != null">
        bean_class,
      </if>
      <if test="jobStatus != null">
        job_status,
      </if>
      <if test="jobGroup != null">
        job_group,
      </if>
      <if test="jobParams != null">
        job_params,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jobName != null">
        #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="cronExpression != null">
        #{cronExpression,jdbcType=VARCHAR},
      </if>
      <if test="beanClass != null">
        #{beanClass,jdbcType=VARCHAR},
      </if>
      <if test="jobStatus != null">
        #{jobStatus,jdbcType=VARCHAR},
      </if>
      <if test="jobGroup != null">
        #{jobGroup,jdbcType=VARCHAR},
      </if>
      <if test="jobParams != null">
        #{jobParams,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.relle.mbg.model.SysTaskExample" resultType="java.lang.Integer">
    select count(*) from sys_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sys_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.jobName != null">
        job_name = #{record.jobName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.cronExpression != null">
        cron_expression = #{record.cronExpression,jdbcType=VARCHAR},
      </if>
      <if test="record.beanClass != null">
        bean_class = #{record.beanClass,jdbcType=VARCHAR},
      </if>
      <if test="record.jobStatus != null">
        job_status = #{record.jobStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.jobGroup != null">
        job_group = #{record.jobGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.jobParams != null">
        job_params = #{record.jobParams,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sys_task
    set id = #{record.id,jdbcType=BIGINT},
      job_name = #{record.jobName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      cron_expression = #{record.cronExpression,jdbcType=VARCHAR},
      bean_class = #{record.beanClass,jdbcType=VARCHAR},
      job_status = #{record.jobStatus,jdbcType=VARCHAR},
      job_group = #{record.jobGroup,jdbcType=VARCHAR},
      job_params = #{record.jobParams,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.relle.mbg.model.SysTask">
    update sys_task
    <set>
      <if test="jobName != null">
        job_name = #{jobName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="cronExpression != null">
        cron_expression = #{cronExpression,jdbcType=VARCHAR},
      </if>
      <if test="beanClass != null">
        bean_class = #{beanClass,jdbcType=VARCHAR},
      </if>
      <if test="jobStatus != null">
        job_status = #{jobStatus,jdbcType=VARCHAR},
      </if>
      <if test="jobGroup != null">
        job_group = #{jobGroup,jdbcType=VARCHAR},
      </if>
      <if test="jobParams != null">
        job_params = #{jobParams,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.relle.mbg.model.SysTask">
    update sys_task
    set job_name = #{jobName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      cron_expression = #{cronExpression,jdbcType=VARCHAR},
      bean_class = #{beanClass,jdbcType=VARCHAR},
      job_status = #{jobStatus,jdbcType=VARCHAR},
      job_group = #{jobGroup,jdbcType=VARCHAR},
      job_params = #{jobParams,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>