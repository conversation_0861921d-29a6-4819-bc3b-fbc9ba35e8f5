<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>relle-api</artifactId>
  <packaging>war</packaging>
  <name>Relle API Module</name>
  <description>API module for Relle Application</description>

  <parent>
    <groupId>com.relle</groupId>
    <artifactId>relle-parent</artifactId>
    <version>0.1</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <properties>
    <!-- JaCoCo thresholds. Increase gradually as you add tests. -->
    <jacoco.unit-tests.limit.instruction-ratio>0%</jacoco.unit-tests.limit.instruction-ratio>
    <jacoco.unit-tests.limit.branch-ratio>0%</jacoco.unit-tests.limit.branch-ratio>
    <jacoco.unit-tests.limit.class-complexity>20</jacoco.unit-tests.limit.class-complexity>
    <jacoco.unit-tests.limit.method-complexity>5</jacoco.unit-tests.limit.method-complexity>
  </properties>

  <dependencies>
    <!-- ========== Internal Module Dependencies ========== -->
    <dependency>
      <groupId>com.relle</groupId>
      <artifactId>relle-common</artifactId>
    </dependency>

    <!-- Spring Boot Starter -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <!-- Exclude slf4j-simple -->
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-simple</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- Add explicit Logback dependency -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>

    <!-- Optional: Add SLF4J dependencies if needed -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>

    <!-- ========== Spring Boot Dependencies ========== -->
    <!-- NOTE: Parent POM already includes spring-boot-starter-web, spring-boot-starter-aop -->
    <!-- These starters transitively include ALL Spring dependencies we need: -->
    <!-- - spring-boot, spring-boot-autoconfigure -->
    <!-- - spring-web, spring-webmvc, spring-context, spring-beans, spring-core -->
    <!-- - aspectjweaver (from spring-boot-starter-aop) -->
    <!-- - jackson-databind, jackson-annotations (from spring-boot-starter-web) -->
    <!-- - javax.annotation-api, servlet-api (from spring-boot-starter-web) -->
    <!-- NO NEED TO DECLARE INDIVIDUAL SPRING DEPENDENCIES! -->

    <!-- ========== Database Dependencies ========== -->
    <!-- MyBatis dependencies for mapper scanning and database operations -->
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis-spring</artifactId>
    </dependency>

    <!-- ========== JSON Dependencies ========== -->
    <!-- Jackson JSR310 - used in ContactAppConfig for LocalDateTime serialization -->
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>

    <!-- ========== Security Dependencies ========== -->
    <!-- JWT - used in TokenInterceptor for authentication -->
    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
    </dependency>

    <!-- ========== Documentation Dependencies ========== -->
    <!-- Swagger - used in controllers and configuration -->
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- ========== Build-time Dependencies ========== -->
    <!-- MyBatis Generator - only needed for code generation, not runtime -->
    <dependency>
      <groupId>org.mybatis.generator</groupId>
      <artifactId>mybatis-generator-core</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- ========== Logging Override ========== -->
    <!-- Override slf4j-simple to prevent conflicts with Logback -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}</finalName>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*</include>
        </includes>
      </resource>
    </resources>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <dependencies>
          <dependency>
            <groupId>com.github.ngeor</groupId>
            <artifactId>checkstyle-rules</artifactId>
            <version>4.9.3</version>
          </dependency>
        </dependencies>
        <configuration>
          <configLocation>com/github/ngeor/checkstyle.xml</configLocation>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <skip>${skipTests}</skip>
        </configuration>
        <executions>
          <execution>
            <?m2e ignore?>
            <id>checkstyle</id>
            <phase>deploy</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>