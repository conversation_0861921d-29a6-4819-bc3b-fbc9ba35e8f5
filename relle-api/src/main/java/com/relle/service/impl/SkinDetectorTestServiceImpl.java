package com.relle.service.impl;

import com.relle.mbg.mapper.SkinDetectorTestMapper;
import com.relle.mbg.model.SkinDetectorTest;
import com.relle.mbg.model.SkinDetectorTestExample;
import com.relle.service.ISkinDetectorTestService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class SkinDetectorTestServiceImpl implements ISkinDetectorTestService {
    @Resource
    private SkinDetectorTestMapper skinDetectorTestMapper;
    @Override
    public SkinDetectorTest saveFromClient(SkinDetectorTest test) {
        SkinDetectorTest detectorTest = getTest(test);
        if(detectorTest!=null){
            return detectorTest;
        }
        Date now = new Date();
        test.setCreateBy("SkinDetectorTestServiceImpl");
        test.setUpdateBy("SkinDetectorTestServiceImpl");
        test.setCreateTime(now);
        test.setUpdateTime(now);
        skinDetectorTestMapper.insertSelective(test);
        return test;
    }

    public SkinDetectorTest getTest(SkinDetectorTest test){
        SkinDetectorTestExample example = new SkinDetectorTestExample();
        example.createCriteria()
                .andReportIdEqualTo(test.getReportId())
                .andStoreIdEqualTo(test.getStoreId());

        List<SkinDetectorTest> skinDetectorTests = skinDetectorTestMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(skinDetectorTests)){
            return null;
        }
        return skinDetectorTests.get(0);
    }
}
