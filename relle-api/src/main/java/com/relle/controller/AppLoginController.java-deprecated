package com.relle.controller;

import com.relle.common.api.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;

@RestController
@RequestMapping(value = "/login")
public class AppLoginController {
    private static final Logger logger = LoggerFactory.getLogger(AppLoginController.class);


    @RequestMapping(value="/oauth_token")
    public CommonResult<?> oauth_token  (String auth_code, HttpServletRequest req, HttpServletResponse res) throws UnsupportedEncodingException {

        logger.info("mt entry into oauth_token "+auth_code);
        String basePath =  "https://" + req.getServerName()
                + req.getContextPath() + "/login/oauth_token";


        return null;
    }


}
