package com.relle.controller;

import com.relle.common.api.CommonResult;
import com.relle.dto.SkinDetectorTestRequest;
import com.relle.mbg.model.SkinDetectorTest;
import com.relle.service.ISkinDetectorTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "检测仪接口")
@RestController
@RequestMapping(value = "/detector")
public class SkinDetectorTestController {
    @Resource
    private ISkinDetectorTestService iSkinDetectorTestService;

    @ApiOperation(value = "批量接收检测记录")
    @PostMapping(value = "/batchReceive")
    public CommonResult<?> batchReceive(@RequestBody List<SkinDetectorTestRequest> params, HttpServletRequest req, HttpServletResponse res) {
        List<SkinDetectorTest> resultList = new ArrayList<>();
        for (int i = 0; i < params.size(); i++) {
            try {
                SkinDetectorTest test = new SkinDetectorTest();
                BeanUtils.copyProperties(params.get(i), test);
                Integer customerIdInt = null;
                try {
                     customerIdInt = Integer.parseInt(test.getCustomerId().trim());
                } catch (Exception e) {
                    return CommonResult.failed("客户编号必须为数字");
                }
                
                // String customerId = StrUtil.fill(test.getCustomerId().trim(),'0', 8, true);
                String customerIdStr = String.format("%12d",customerIdInt);

                test.setCustomerId(customerIdStr);
                SkinDetectorTest detectorTest = iSkinDetectorTestService.saveFromClient(test);
                resultList.add(detectorTest);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return CommonResult.succeeded(resultList);
    }

    @ApiOperation(value = "单个接收检测记录")
    @PostMapping(value = "/receive")
    public CommonResult<?> receive(@RequestBody SkinDetectorTestRequest params, HttpServletRequest req, HttpServletResponse res) {
        SkinDetectorTest test = new SkinDetectorTest();
        BeanUtils.copyProperties(params, test);
        iSkinDetectorTestService.saveFromClient(test);
        return CommonResult.succeeded(test);
    }

}