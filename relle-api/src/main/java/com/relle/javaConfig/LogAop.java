package com.relle.javaConfig;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
@Aspect
@Component
public class LogAop {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogAop.class);

    @Pointcut("execution(public * com.relle.service.*.*(..))")
    public void webLog() {
    }


    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) {
        // 接收到请求，记录请求内容

        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() + ":{}";
        String methodParams = Arrays.toString(joinPoint.getArgs());

        LOGGER.info("params:" + methodName, methodParams);
    }


    @AfterReturning(returning = "data", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object data) {
        String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName() + ":{}";
        LOGGER.info("return:" + methodName, data);
    }

}