package com.relle;

// import com.alibaba.fastjson.JSON;
// import com.alibaba.fastjson.JSONObject;
// import org.apache.http.HttpEntity;
// import org.apache.http.client.config.RequestConfig;
// import org.apache.http.client.methods.CloseableHttpResponse;
// import org.apache.http.client.methods.HttpPost;
// import org.apache.http.entity.AbstractHttpEntity;
// import org.apache.http.entity.StringEntity;
// import org.apache.http.impl.client.CloseableHttpClient;
// import org.apache.http.impl.client.HttpClients;
// import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.*;
import java.util.*;

import javax.annotation.Resource;


/**
 * 生成带参数的小程序码
 * 参考 https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getUnlimitedQRCode.html
 */
public class MiniprogramQrcode {
    @Resource ObjectMapper objectMapper;
    @Resource RestTemplate restTemplate;

    // public static void main(String...args) throws Exception {
    //     MiniprogramQrcode instance = new MiniprogramQrcode();
    //     instance.getWxQrcode();
    // }

    public void getWxQrcode(){
        //智能导览小程序
//        String appid = "wxaa334f53f55e6c1f";
//        String secret = "32151349574f0d49fad8b9ec9e2dfea9";
        //购票小程序
//        String appid = "wx8d6613e78ed10219";
//        String secret = "cac4906ca84ea8429bd2e3de1c160240";
        //规划馆公众号
//        String appid = "wx3dc907d3b28b5bc5";
//        String secret = "947db1feb4d97cd2a002e4f4b98629f0";
//        String js_code = "8737d665ed181170b14fd39e0a641709";
        //relle小程序
        String appid = "wx56b81e1a0a4cc40a";
        String secret = "c9d39576c9e3aa6500db449ff7929a3f";
        //spup小程序
//        String appid = "wx85365381338b9a5e";
//        String secret = "b8e269e63f0d29ab1af196cfbfecec9b";

//        System.out.println( HttpUtils.getRequest( "https://api.weixin.qq.com/sns/jscode2session?appid="+appid+"&secret="+secret+"&js_code="+js_code+"&grant_type=authorization_code"));



//        MpClient client2 = new MpClient("wx3dc907d3b28b5bc5","8737d665ed181170b14fd39e0a641709");
//
//        UserInfoRequest userInfoRequest = new UserInfoRequest( "oyhocuJPE3hBL3Qa0kZG8T7w66VY");
//        UserInfoResponse userInfoResponse = client.send(userInfoRequest);
//        System.out.println( "userInfoResponse: " + JSON.toJSONString( userInfoResponse));
//        userInfoRequest = new UserInfoRequest( "oyhocuJPE3hBL3Qa0kZG8T7w66VY");
//        UserInfoResponse userInfoResponse2 = client2.send(userInfoRequest);
//        System.out.println( "userInfoResponse2: " + JSON.toJSONString( userInfoResponse2));

        //74_w3fNtEqqdQGnxWobNqXeJMtV7KSxUt2zeGmNuuw4e6k0il_Xl4WlVkHbDqsFnIvAhH7TuOeS2Al3XmZdHySW6kAsDqEslEIy84UEob866bZf0rv8xPf3jrqpE9ATUPfAFAZAV
        //74_vOb_a6HcPig4pD0fRySDEv2686_3Q94rrPIkad8sGYxS2V_HqCK2UK_wB6cW_6Khh8BQsDODGCDT9SfQ5lBMNsuPEN8o3VeK9HDDzsHyhISIMDuyPdVRW6XoTs8YGGfAHABJJ
        String accessToken = this.getAccessToken(appid, secret);
        System.out.println(accessToken);

        Map<String,String> params =  new HashMap<>();
//        params.put("access_token", accessToken);
//        params.put("page", "pages/index/index");
//        params.put("page", "pages/index/index");
//        params.put("scene", "line01");

        // JSONObject obj = new JSONObject();
        //小程序二维码：
//        obj.put("path", "pages/index/index?w=666");
//        obj.put("width", 1280);
//        不限个数的小程序码
        /*String[] idArr = {"SH0001009","SH0002003", "SH0002004"};
        String[] nameArr = {"余羽林", "陈岚", "齐瑞涵"};
        String envVersion = "release";
        for(int i=0; i<idArr.length; i++){
            String eId = idArr[i];
            String eName = nameArr[i];
            obj.put("page", "pages/index/index");
            obj.put("scene", "e="+eId+"&s=SH0002");
            obj.put("check_path", true);
            obj.put("env_version", envVersion);
            System.out.println( postRequest(eName, envVersion, "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+accessToken, obj.toString()));
        }
*/
        ObjectNode jsonNode = objectMapper.createObjectNode();
        String storeId = "SH0003";
        String activityId = "37";
        List<String> stringList = new ArrayList<>();
        for(int i=0; i<100; i++){
            String randCode= getRandomCharStr(4);
            String envVersion = "release";  //要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
            jsonNode.put("page", "subpages/my/coupon");
            jsonNode.put("scene", "s="+storeId+"&a="+activityId+"&n="+randCode);
            jsonNode.put("check_path", false);
            jsonNode.put("env_version", envVersion);
            stringList.add( postRequest(randCode, envVersion, "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+accessToken, jsonNode.toString(),activityId,storeId));
        }
        for (int i = 0; i < stringList.size(); i++) {
            System.out.println(stringList.get(i));
        }

        /**
         * 生成n位随机数字和字母
         * @param n 随机字符的位数
         * @return n位数字和字母组成的随机字符串
         */


//
//        String eId = "SH0001004";
//        String eName = "张瑞";
//        String envVersion = "release";
//        obj.put("page", "pages/index/index");
//        obj.put("scene", "e="+eId);
//        obj.put("check_path", true);
//        obj.put("env_version", envVersion);
//
//        System.out.println( postRequest(eName, envVersion, "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+accessToken, obj.toString()));
//        System.out.println( HttpUtils.postRequest( "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+accessToken, obj.toString()));
//        System.out.println( postRequest( "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token="+accessToken, obj.toString()));

//        System.out.println( HttpUtils.getRequest( "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wx6db8d7f1fbd3cd19&secret=947db1feb4d97cd2a002e4f4b98629f0"));
    }
    public static   String getRandomCharStr(int n) {
        String codes = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0; i < n; i++) {
            randomStr.append(codes.charAt(random.nextInt(62)));
        }
        return randomStr.toString();
    }
    public String getAccessToken(String appid, String secret) {
        // CloseableHttpClient httpClient = null;
        // CloseableHttpResponse httpResponse = null;
        // HttpEntity entity = null;
        // OutputStream os = null;
        try {
            // httpClient = HttpClients.createDefault();
            // HttpPost httpPost = new HttpPost();
            // RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( 60000 * 5).setConnectTimeout( 60000 * 5).build();//设置请求和传输超时时间
            // httpPost.setConfig(requestConfig);

            String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + secret;
            // httpPost.addHeader("Content-Type", "application/json;charset=utf-8");

//            JSONObject obj = new JSONObject();
//            obj.put("appid", appid);
//            obj.put("secret", secret);
//            obj.put("grant_type", "client_credential");
//
//            AbstractHttpEntity postEntity = new StringEntity( obj.toString(), "UTF-8");
//            httpPost.setEntity( postEntity);
            // httpResponse = httpClient.execute( httpPost);
            // String result = "";
            // entity = httpResponse.getEntity();
            String result = restTemplate.getForObject(url, String.class);

            // if (entity != null) {
            //     BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), "UTF-8"));
            //     String text;
            //     while ((text = bufferedReader.readLine()) != null) {
            //         result += text;
            //     }
            // }
            JsonNode json = objectMapper.readTree(result);
            return json.get("access_token").asText();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException( e);
        }
    }

    public static String postRequest(String eName, String envVersion, String url, String json,String activityId,String storeId) {
        if (!StringUtils.hasLength( url)) {
            throw new IllegalArgumentException( "url cannot be null or empty");
        }
        if (json == null) {
            json = "";
        }

        CloseableHttpClient httpClient = null;
        CloseableHttpResponse httpResponse = null;
        HttpEntity entity = null;
        OutputStream os = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost( url);
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout( 60000 * 5).setConnectTimeout( 60000 * 5).build();//设置请求和传输超时时间
            httpPost.setConfig(requestConfig);

            httpPost.addHeader("Content-Type", "application/json;charset=utf-8");

            AbstractHttpEntity postEntity = new StringEntity( json, "UTF-8");
            httpPost.setEntity( postEntity);
            httpResponse = httpClient.execute( httpPost);
            String result = "";
            entity = httpResponse.getEntity();

            // 图片保存的地址
            String path = "D:\\qrcode\\"+activityId+"\\"+storeId;
            String envVersionPath = "D:\\qrcode\\"+activityId+"\\"+storeId;
            // 输出的文件流保存到本地文件
            File envVersionDirectory = new File(envVersionPath);
            if (!envVersionDirectory.exists()) {
                envVersionDirectory.mkdirs();
            }

            if (entity != null) {
                // 1K的数据缓冲
                byte[] bs = new byte[1024];
                // 读取到的数据长度
                int len;
                // 保存图片
//                os = new FileOutputStream(tempFile.getPath() + File.separator + eName + "_" + envVersion + ".jpg");
                os = new FileOutputStream(path + File.separator + eName + ".jpg");
                // 开始读取
                while ((len = entity.getContent().read(bs)) != -1) {
                    os.write(bs, 0, len);
                }
            }

            return  "/relle-media/activity/"+activityId+"/"+storeId+"/"+eName + ".jpg,"+eName;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException( e);
        } finally {
            close(httpClient, httpResponse, entity);
            try {
                if(os!=null){
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }

}