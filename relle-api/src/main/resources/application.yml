server:
  port: 8088

jwt:
  sign:
    string: com.relle-mall.JWT

spring:
  profiles:
    active: pro
  jmx:
    enabled: false

mybatis: 
  mapper-locations: 
    - classpath:mapper/*.xml
    - classpath:com/**/mapper/*.xml

logging:
  config: classpath:logback-spring.xml
  file:
    path: logs
  # level:
  #   root: INFO
  #   com.relle: DEBUG
  #   org.springframework: INFO
  #   org.mybatis: INFO