package com.relle.common.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * Date and Time Utility Class
 * Provides conversion methods between Date and LocalDateTime/LocalDate
 */
public class DateTimeUtil {
    
    /**
     * Convert Date to LocalDateTime
     * @param date Date to convert
     * @return LocalDateTime or null if input is null
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
    
    /**
     * Convert LocalDateTime to Date
     * @param localDateTime LocalDateTime to convert
     * @return Date or null if input is null
     */
    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * Convert Date to LocalDate
     * @param date Date to convert
     * @return LocalDate or null if input is null
     */
    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
    
    /**
     * Convert LocalDate to Date
     * @param localDate LocalDate to convert
     * @return Date or null if input is null
     */
    public static Date toDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * Get current Date
     * @return current Date
     */
    public static Date now() {
        return new Date();
    }
    
    /**
     * Get current LocalDateTime
     * @return current LocalDateTime
     */
    public static LocalDateTime nowLocal() {
        return LocalDateTime.now();
    }
}
