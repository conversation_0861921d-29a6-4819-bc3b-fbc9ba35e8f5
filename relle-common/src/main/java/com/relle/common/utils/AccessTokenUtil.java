package com.relle.common.utils;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Utility class for handling WeChat access tokens
 */
public class AccessTokenUtil {
    private static final Logger logger = LoggerFactory.getLogger(AccessTokenUtil.class);
    @Resource
    static RestTemplate restTemplate;
    @Resource
    static ObjectMapper objectMapper;

    public static String getAccessToken(String appid, String secret) {
        try {
            String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + secret;
            String result = restTemplate.getForObject(url, String.class);
            JsonNode jsonObject = objectMapper.readTree(result);
            
            if (jsonObject.has("access_token")) {
                return jsonObject.get("access_token").asText();
            } else {
                logger.error("Failed to get access token: {}", result);
                return null;
            }
        } catch (Exception e) {
            logger.error("Error getting access token", e);
            return null;
        }
    }
}
