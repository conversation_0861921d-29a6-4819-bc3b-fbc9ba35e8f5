package com.relle.common.utils;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * HTTP request utility functions
 */
public class HttpRequestUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpRequestUtil.class);

    /**
     * Send HTTP POST request with parameters and optional token
     * @param url Target URL
     * @param params Request parameters
     * @param token Optional authorization token
     * @return Response string
     */
    public static String httpPost(String url, Map<String, String> params, String token) {
        String strResult = null;
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost method = new HttpPost(url);

            // Add token to header if provided
            if (token != null && !token.isEmpty()) {
                method.addHeader("Authorization", "Bearer " + token);
            }

            // Add parameters to request
            if (params != null && !params.isEmpty()) {
                List<NameValuePair> nameValuePairList = new ArrayList<>();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    nameValuePairList.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(nameValuePairList, "utf-8");
                method.setEntity(entity);
            }

            logger.info("POST URL: {}", url);
            HttpResponse result = httpClient.execute(method);
            url = URLDecoder.decode(url, "UTF-8");

            // Process response
            logger.info("Status code: {}", result.getStatusLine().getStatusCode());
            if (result.getStatusLine().getStatusCode() == 200) {
                try {
                    strResult = EntityUtils.toString(result.getEntity());
                    logger.info("Response: {}", strResult);
                } catch (Exception e) {
                    logger.error("POST request failed: {}", url, e);
                }
            }
        } catch (IOException e) {
            logger.error("POST request failed: {}", url, e);
        }
        return strResult;
    }

    /**
     * Send HTTP POST request with parameters
     * @param url Target URL
     * @param params Request parameters
     * @return Response string
     */
    public static String httpPost(String url, Map<String, String> params) {
        return httpPost(url, params, null);
    }

    /**
     * Send HTTP GET request
     * @param url Target URL
     * @return Response string
     */
    public static String httpGet(String url) {
        String strResult = null;
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            HttpResponse response = client.execute(request);

            // Process response
            if (response.getStatusLine().getStatusCode() == 200) {
                strResult = EntityUtils.toString(response.getEntity());
                logger.info("GET URL: {}, Response: {}", url, strResult);
            } else {
                logger.error("GET request failed: {}", url);
            }
        } catch (IOException e) {
            logger.error("GET request failed: {}", url, e);
        }
        return strResult;
    }

    /**
     * Send HTTP POST request with raw data
     * @param urlStr Target URL
     * @param data Raw data to send
     * @return Response string
     */
    public static String httpPostRaw(String urlStr, String data) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(urlStr);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestMethod("POST");
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.connect();

            // Send request data
            try (OutputStream os = connection.getOutputStream()) {
                os.write(data.getBytes("UTF-8"));
                os.flush();
            }

            // Read response
            StringBuilder sb = new StringBuilder();
            try (InputStream is = connection.getInputStream()) {
                byte[] b = new byte[1024];
                int len;
                while ((len = is.read(b)) != -1) {
                    sb.append(new String(b, 0, len));
                }
            }
            return sb.toString();
        } catch (Exception e) {
            logger.error("POST raw request failed: {}", urlStr, e);
            return null;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}
