package com.relle.common.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT token utility functions
 */
@Component
public class JWTUtil {
    private static final Logger logger = LoggerFactory.getLogger(JWTUtil.class);

    // Secret key for JWT token generation and verification
    private static final String SECRET = "com.relle-mall.JWT";
    // Default token expiration time (30 minutes)
    private static final long EXPIRE_TIME = 30 * 60 * 1000;

    @Value("${jwt.sign.string}")
    private String signStr;

    /**
     * Generate JWT token
     * @param username Username
     * @param secret Secret key
     * @return JWT token
     */
    public static String sign(String username, String secret) {
        return sign(username, secret, EXPIRE_TIME);
    }

    /**
     * Generate JWT token with custom expiration time
     * @param username Username
     * @param secret Secret key
     * @param expireTime Expiration time in milliseconds
     * @return JWT token
     */
    public static String sign(String username, String secret, long expireTime) {
        try {
            Date date = new Date(System.currentTimeMillis() + expireTime);
            Algorithm algorithm = Algorithm.HMAC256(secret);

            // Set JWT header
            Map<String, Object> header = new HashMap<>(2);
            header.put("typ", "JWT");
            header.put("alg", "HS256");

            // Build and sign JWT
            return JWT.create()
                    .withHeader(header)
                    .withClaim("username", username)
                    .withExpiresAt(date)
                    .sign(algorithm);
        } catch (Exception e) {
            logger.error("JWT token generation failed", e);
            return null;
        }
    }

    /**
     * Verify JWT token
     * @param token JWT token
     * @param username Expected username
     * @param secret Secret key
     * @return true if token is valid, false otherwise
     */
    public static boolean verify(String token, String username, String secret) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withClaim("username", username)
                    .build();
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            logger.error("JWT token verification failed", e);
            return false;
        }
    }

    /**
     * Get username from JWT token
     * @param token JWT token
     * @return Username
     */
    public static String getUsername(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim("username").asString();
        } catch (Exception e) {
            logger.error("Failed to get username from JWT token", e);
            return null;
        }
    }

    /**
     * Check if JWT token is expired
     * @param token JWT token
     * @return true if token is expired, false otherwise
     */
    public static boolean isTokenExpired(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt().before(new Date());
        } catch (Exception e) {
            logger.error("Failed to check JWT token expiration", e);
            return true;
        }
    }

    /**
     * Generate JWT token
     * @param unionid User unionid
     * @param openid User openid
     * @return JWT token
     */
    public String generateToken(String unionid, String openid) {
        try {
            // Set token expiration time
            Date expireDate = new Date(System.currentTimeMillis() + EXPIRE_TIME);

            // Set JWT header
            Map<String, Object> header = new HashMap<>();
            header.put("alg", "HS384");
            header.put("typ", "JWT");

            // Generate token
            return JWT.create()
                    .withHeader(header)
                    .withClaim("unionid", unionid)
                    .withClaim("openid", openid)
                    .withExpiresAt(expireDate)
                    .withIssuedAt(new Date())
                    .sign(Algorithm.HMAC384(SECRET));
        } catch (Exception e) {
            logger.error("Failed to generate JWT token", e);
            return null;
        }
    }

    /**
     * Verify and decode JWT token
     * @param token JWT token
     * @return Decoded JWT
     */
    public DecodedJWT decodeToken(String token) {
        try {
            JWTVerifier verifier = JWT.require(Algorithm.HMAC384(SECRET)).build();
            return verifier.verify(token);
        } catch (Exception e) {
            logger.error("Failed to decode JWT token", e);
            throw new RuntimeException("Failed to decode JWT token", e);
        }
    }


    public String getToken(String unionid,String openid) throws UnsupportedEncodingException {
        return  getToken(unionid,openid,120);
    }

    public String getToken(String unionid,String openid,int validMinute) throws UnsupportedEncodingException {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, validMinute);

        String token = JWT.create()
                .withHeader(new HashMap<>())  // Header
                .withClaim("unionid", unionid)  // Payload
                .withClaim("openid", openid)
                .withExpiresAt(calendar.getTime())  // 过期时间
                .sign(Algorithm.HMAC384(signStr));  // 签名用的secret
        return token;
    }
}
