package com.relle.common.utils;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ID and number generation utilities
 */
public class NumberGenerator {

    /**
     * Generate store ID
     * @return Store ID
     */
    public static String getStoreId() {
        // Implementation to be added based on business requirements
        return null;
    }

    /**
     * Generate service item ID
     * @return Service item ID
     */
    public static String getServiceItemId() {
        // Implementation to be added based on business requirements
        return null;
    }

    /**
     * Generate service order ID
     * @param storeId Store ID
     * @return Service order ID
     */
    public static String getServiceOrderId(String storeId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        // Order ID format: storeId + timestamp
        return storeId + timestamp;
    }

    /**
     * Generate refund order ID
     * @param orderId Original order ID
     * @param index Refund index
     * @return Refund order ID
     */
    public static String getRefundOrderId(String orderId, int index) {
        DecimalFormat decimalFormat = new DecimalFormat("00");
        // Refund order ID format: orderId + "r" + formatted index
        return orderId + "r" + decimalFormat.format(index);
    }

    /**
     * Generate customer ID
     * @return Customer ID
     */
    public static String getCustomerId() {
        // Customer ID based on current timestamp
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * Generate operation ID
     * @param prefix Prefix for the operation ID
     * @param index Operation index
     * @return Operation ID
     */
    public static String getOperationId(String prefix, int index) {
        DecimalFormat decimalFormat = new DecimalFormat("000");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss"));
        // Operation ID format: prefix + timestamp + formatted index
        return prefix + timestamp + decimalFormat.format(index);
    }
}
