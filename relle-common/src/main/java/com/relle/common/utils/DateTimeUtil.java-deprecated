package com.relle.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Date and time utility functions
 */
public class DateTimeUtil {
    /*
     * yyyy-MM-dd HH:mm:ss
     */
    public static final String PATTERN_0 = "yyyy-MM-dd HH:mm:ss";
    public static final SimpleDateFormat FORMATTER_0 = new SimpleDateFormat(PATTERN_0);

    /*
     * yyyy-MM-dd
     */
    public static final String PATTERN_1 = "yyyy-MM-dd";
    public static final SimpleDateFormat FORMATTER_1 = new SimpleDateFormat(PATTERN_1);

    /*
     * yyyy/MM/dd
     */
    public static final String PATTERN_2 = "yyyy/MM/dd";
    public static final SimpleDateFormat FORMATTER_2 = new SimpleDateFormat(PATTERN_2);

    /*
     * HH:mm:ss
     */
    public static final String PATTERN_3 = "HH:mm:ss";
    public static final SimpleDateFormat FORMATTER_3 = new SimpleDateFormat(PATTERN_3);

    /*
     * yyyy年MM月dd日
     */
    public static final String PATTERN_4 = "yyyy年MM月dd日";
    public static final SimpleDateFormat FORMATTER_4 = new SimpleDateFormat(PATTERN_4);

    /*
     * yyyy-MM-dd HH:mm
     */
    public static final String PATTERN_5 = "yyyy-MM-dd HH:mm";
    public static final SimpleDateFormat FORMATTER_5 = new SimpleDateFormat(PATTERN_5);

    /*
     * yyyy年MM月dd日 HH时mm分
     */
    public static final String PATTERN_6 = "yyyy年MM月dd日 HH时mm分";
    public static final SimpleDateFormat FORMATTER_6 = new SimpleDateFormat(PATTERN_6);

    /**
     * yyyyMMdd
     */
    public static final String PATTERN_7 = "yyyyMMdd";
    public static final SimpleDateFormat FORMATTER_7 = new SimpleDateFormat(PATTERN_7);

    public static final String PATTERN_8 = "yyyyMMddHHmm";
    public static final SimpleDateFormat FORMATTER_8 = new SimpleDateFormat(PATTERN_8);

    public static final String PATTERN_9 = "HHmm";
    public static final SimpleDateFormat FORMATTER_9 = new SimpleDateFormat(PATTERN_9);

    /**
     * Get current system time in yyyyMMddHHmmss format
     * @return Current time string
     */
    public static String getSysTime() {
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }

    /**
     * Get current system time in specified format
     * @param pattern Date format pattern
     * @return Current time string
     */
    public static String getSysTime(String pattern) {
        return new SimpleDateFormat(pattern).format(new Date());
    }

    /**
     * Format date to string using default pattern (yyyy-MM-dd HH:mm:ss)
     * @param date Date to format
     * @return Formatted date string
     */
    public static String getDateTimeString(Date date) {
        return FORMATTER_0.format(date);
    }

    /**
     * Format date to string using specified pattern
     * @param date Date to format
     * @param pattern Date format pattern
     * @return Formatted date string
     */
    public static String getDateTimeString(Date date, String pattern) {
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * Format calendar to string using specified pattern
     * @param pattern Date format pattern
     * @param calendar Calendar to format
     * @return Formatted date string
     */
    public static String getTime(String pattern, Calendar calendar) {
        return new SimpleDateFormat(pattern).format(calendar.getTime());
    }

    /**
     * Convert string from one date format to another
     * @param timeStr Original date string
     * @param srcPattern Source format pattern
     * @param destPattern Destination format pattern
     * @return Reformatted date string
     */
    public static String reFormatTime(String timeStr, String srcPattern, String destPattern) {
        try {
            Date date = new SimpleDateFormat(srcPattern).parse(timeStr);
            return new SimpleDateFormat(destPattern).format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Parse date string to Date object
     * @param dateStr Date string
     * @return Date object
     */
    public static Date parseDate(String dateStr) {
        try {
            return FORMATTER_0.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Parse date string to Date object using specified pattern
     * @param dateStr Date string
     * @param pattern Date format pattern
     * @return Date object
     */
    public static Date parseDate(String dateStr, String pattern) {
        try {
            return new SimpleDateFormat(pattern).parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Parse date string to Calendar object
     * @param dateStr Date string
     * @return Calendar object
     */
    public static Calendar getDateTime(String dateStr) {
        try {
            Date date = FORMATTER_0.parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            return calendar;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Format time duration in milliseconds to human-readable string
     * @param timeMillis Time duration in milliseconds
     * @return Human-readable time string
     */
    public static String getTimeStr(long timeMillis) {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);

        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day).append("天");
        }
        if (hour > 0) {
            sb.append(hour).append("小时");
        }
        if (min > 0) {
            sb.append(min).append("分");
        }
        if (s > 0) {
            sb.append(s).append("秒");
        }

        return sb.toString();
    }

    /**
     * Format date to string using specified pattern
     * @param date Date to format
     * @param pattern Format pattern
     * @return Formatted date string
     */
    public static String formatDate(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * Parse date string to Calendar object using specified pattern
     * @param dateStr Date string
     * @param pattern Date format pattern
     * @return Calendar object
     * @throws ParseException If the date string cannot be parsed
     */
    public static Calendar getDateTime(String dateStr, String pattern) throws ParseException {
        Date date = new SimpleDateFormat(pattern).parse(dateStr);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }
}
