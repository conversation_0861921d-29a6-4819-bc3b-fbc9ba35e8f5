package com.relle.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;

/**
 * Utility for sending emails with attachments
 */
@Component
public class SendComplexEmail {
    private static final Logger logger = LoggerFactory.getLogger(SendComplexEmail.class);

    @Resource
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    /**
     * Send email with attachment asynchronously
     * @param to Recipient email address
     * @param subject Email subject
     * @param content Email content
     * @param filePath Path to attachment file
     * @throws MessagingException If there's an error sending the email
     */
    @Async
    public void send(String to, String subject, String content, String filePath) throws MessagingException {
        send(to, subject, content, new File(filePath));
    }

    /**
     * Send email with attachment asynchronously
     * @param to Recipient email address
     * @param subject Email subject
     * @param content Email content
     * @param file Attachment file
     * @throws MessagingException If there's an error sending the email
     */
    @Async
    public void send(String to, String subject, String content, File file) throws MessagingException {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            
            if (file != null && file.exists()) {
                helper.addAttachment(file.getName(), file);
            } else {
                logger.warn("Attachment file does not exist: {}", file);
            }
            
            mailSender.send(message);
            logger.info("Email sent successfully to: {}", to);
        } catch (MessagingException e) {
            logger.error("Failed to send email to: {}", to, e);
            throw e;
        }
    }
}
