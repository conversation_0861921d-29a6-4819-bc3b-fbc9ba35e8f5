package com.relle.common.controller;

import com.relle.common.api.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * Example controller to demonstrate SpringDoc OpenAPI annotations
 * This controller can be used as a reference for migrating from Swagger to SpringDoc
 */
@Tag(name = "示例接口", description = "用于演示SpringDoc OpenAPI注解的示例接口")
@RestController
@RequestMapping("/example")
public class ExampleController {

    /**
     * Example GET endpoint
     * @param id ID parameter
     * @return CommonResult with a message
     */
    @Operation(summary = "获取示例数据", description = "根据ID获取示例数据")
    @GetMapping("/{id}")
    public CommonResult<?> getExample(
            @Parameter(description = "示例ID", required = true) @PathVariable String id) {
        return CommonResult.succeeded("Example data for ID: " + id);
    }

    /**
     * Example POST endpoint
     * @param data Request body data
     * @return CommonResult with a message
     */
    @Operation(summary = "创建示例数据", description = "创建新的示例数据")
    @PostMapping
    public CommonResult<?> createExample(
            @Parameter(description = "示例数据", required = true) @RequestBody String data) {
        return CommonResult.succeeded("Created example data: " + data);
    }

    /**
     * Example PUT endpoint
     * @param id ID parameter
     * @param data Request body data
     * @return CommonResult with a message
     */
    @Operation(summary = "更新示例数据", description = "根据ID更新示例数据")
    @PutMapping("/{id}")
    public CommonResult<?> updateExample(
            @Parameter(description = "示例ID", required = true) @PathVariable String id,
            @Parameter(description = "示例数据", required = true) @RequestBody String data) {
        return CommonResult.succeeded("Updated example data for ID: " + id + " with data: " + data);
    }

    /**
     * Example DELETE endpoint
     * @param id ID parameter
     * @return CommonResult with a message
     */
    @Operation(summary = "删除示例数据", description = "根据ID删除示例数据")
    @DeleteMapping("/{id}")
    public CommonResult<?> deleteExample(
            @Parameter(description = "示例ID", required = true) @PathVariable String id) {
        return CommonResult.succeeded("Deleted example data for ID: " + id);
    }
}
