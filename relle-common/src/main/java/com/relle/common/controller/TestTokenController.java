package com.relle.common.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.relle.common.api.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for generating test tokens for Swagger UI testing
 * This controller is excluded from token validation
 */
@Tag(name = "Test Utilities", description = "Utilities for testing the API")
@RestController
@RequestMapping("/api/test")
public class TestTokenController {

    @Value("${jwt.sign.string}")
    private String jwtSignString;

    /**
     * Generate a test JWT token for Swagger UI testing
     * This endpoint is excluded from token validation
     * @return Map containing the token and token with Bearer prefix
     */
    @Operation(
        summary = "Generate test token", 
        description = "Generates a test JWT token for Swagger UI testing. Copy the 'tokenWithBearer' value into the Authorization field."
    )
    @GetMapping("/token")
    public CommonResult<Map<String, String>> generateTestToken() {
        try {
            // Create a test token with sample claims
            String token = JWT.create()
                .withClaim("unionid", "test-unionid")
                .withClaim("openid", "test-openid")
                .sign(Algorithm.HMAC256(jwtSignString));
            
            // Return both the raw token and the token with Bearer prefix
            Map<String, String> result = new HashMap<>();
            result.put("token", token);
            result.put("tokenWithBearer", "Bearer " + token);
            result.put("usage", "Copy the 'tokenWithBearer' value into the Authorization field in Swagger UI");
            
            return CommonResult.succeeded(result);
        } catch (Exception e) {
            return CommonResult.failed("Failed to generate token: " + e.getMessage());
        }
    }
}
