# Swagger to SpringDoc OpenAPI Migration Guide

This document provides guidance on migrating from Swagger (SpringFox) to SpringDoc OpenAPI in the Relle application.

## Annotation Mapping

| Swagger (SpringFox) | SpringDoc OpenAPI | Description |
|---------------------|-------------------|-------------|
| `@Api(tags = "...")` | `@Tag(name = "...")` | Controller-level annotation to group API operations |
| `@ApiOperation(value = "...")` | `@Operation(summary = "...")` | Method-level annotation to describe an API operation |
| `@ApiParam(value = "...")` | `@Parameter(description = "...")` | Parameter-level annotation to describe an API parameter |
| `@ApiModel` | `@Schema` | Model-level annotation to describe a model |
| `@ApiModelProperty` | `@Schema` | Property-level annotation to describe a model property |
| `@ApiResponse` | `@ApiResponse` | Method-level annotation to describe an API response |
| `@ApiResponses` | `@ApiResponses` | Method-level annotation to describe multiple API responses |
| `@ApiIgnore` | `@Hidden` | Annotation to hide an API operation or parameter |

## Configuration Changes

### 1. Dependencies

Replace Swagger dependencies with SpringDoc dependencies in your pom.xml:

```xml
<!-- Remove these dependencies -->
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-swagger2</artifactId>
    <version>2.9.2</version>
</dependency>
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-swagger-ui</artifactId>
    <version>2.9.2</version>
</dependency>

<!-- Add these dependencies -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-ui</artifactId>
    <version>1.6.14</version>
</dependency>
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-webmvc-core</artifactId>
    <version>1.6.14</version>
</dependency>
```

### 2. Configuration Class

Replace Swagger2Config with OpenAPIConfig:

```java
@Configuration
public class OpenAPIConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Relle API")
                        .description("Relle API Documentation")
                        .version("1.0")
                        .license(new License().name("Relle License")))
                .components(new Components()
                        .addSecuritySchemes("Authorization", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .in(SecurityScheme.In.HEADER)
                                .name("Authorization")))
                .addSecurityItem(new SecurityRequirement().addList("Authorization"));
    }
}
```

### 3. Application Properties

Update your application.yml or application.properties:

```yaml
# SpringDoc OpenAPI Configuration
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    disable-swagger-default-url: true
    display-request-duration: true
    operations-sorter: alpha
    tags-sorter: alpha
  packages-to-scan: com.relle
  paths-to-match: /**
```

## Example Controller

Here's an example of a controller using SpringDoc OpenAPI annotations:

```java
@Tag(name = "示例接口", description = "用于演示SpringDoc OpenAPI注解的示例接口")
@RestController
@RequestMapping("/example")
public class ExampleController {

    @Operation(summary = "获取示例数据", description = "根据ID获取示例数据")
    @GetMapping("/{id}")
    public CommonResult<?> getExample(
            @Parameter(description = "示例ID", required = true) @PathVariable String id) {
        return CommonResult.succeeded("Example data for ID: " + id);
    }

    @Operation(summary = "创建示例数据", description = "创建新的示例数据")
    @PostMapping
    public CommonResult<?> createExample(
            @Parameter(description = "示例数据", required = true) @RequestBody String data) {
        return CommonResult.succeeded("Created example data: " + data);
    }
}
```

## Migration Steps

1. Add SpringDoc dependencies to your pom.xml
2. Create a new OpenAPIConfig class
3. Update your application.yml or application.properties
4. Migrate your controllers from Swagger to SpringDoc annotations
5. Migrate your models from Swagger to SpringDoc annotations
6. Test your API documentation at /swagger-ui.html

## Accessing the API Documentation

After migrating to SpringDoc OpenAPI, you can access the API documentation at:

- API Documentation: http://localhost:8080/v3/api-docs
- Swagger UI: http://localhost:8080/swagger-ui.html

## References

- [SpringDoc OpenAPI Documentation](https://springdoc.org/)
- [OpenAPI 3.0 Specification](https://spec.openapis.org/oas/v3.0.3)
- [Swagger to OpenAPI Migration Guide](https://swagger.io/blog/api-development/swagger-to-openapi-migration/)
