package com.relle.common.api;

/**
 * Standard API result codes
 */
public enum ResultCodeEnum implements IErrorCode {
    SUCCEEDED(true, 0, "Operation SUCCEEDED"),
    FAILED(false, 500, "Operation FAILED"),
    VALIDATION_FAILED(false, 404, "Validation FAILED"),
    AUTHORIZED_FAILED(false, 401, "Unauthorized or token EXPIRED"),
    REQUEST_TOKEN_EMPTY(false, 600, "Request token is EMPTY"),
    GET_TOKEN_KEY_FAILED(false, 601, "Getting token key FAILED"),
    GEN_PUBLIC_KEY_FAILED(false, 602, "Generating public key FAILED"),
    JWT_TOKEN_EXPIRE(false, 603, "Token checking FAILED"),
    TOO_MANY_REQUEST_CONTROLLED(false, 429, "Flow control on server"),
    SERVICE_DEGRADED(false, 604,"Service degraded"),
    BAD_GATEWAY(false, 502,"Bad gateway"),
    PRIVILE<PERSON><PERSON>_FORBIDDEN(false, 403, "Priviledge forbidden"),
    ALG<PERSON><PERSON><PERSON><PERSON>_INCONSISTENCY(false, 603, "Algorithm inconsistency"),
    JWT_TOKEN_FAILED(false, 604, "Token checking FAILED"),
    ; // Semicolon prevents compilation errors when adding new enum values
    
    private final boolean status;
    private final long code;
    private final String message;

    ResultCodeEnum(boolean status, long code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

    @Override
    public boolean getStatus() {
        return status;
    }

    @Override
    public long getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
