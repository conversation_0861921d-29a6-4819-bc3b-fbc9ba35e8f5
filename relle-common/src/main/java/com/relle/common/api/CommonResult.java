package com.relle.common.api;


/**
 * Common API response wrapper
 * @param <T> Type of data returned
 */
public class CommonResult<T> {
    private boolean status;
    private long code;
    private String message;
    private T data;

    protected CommonResult() {
    }

    protected CommonResult(boolean status, long code, String message, T data) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * Return successful result with data
     * @param data Result data
     * @param <T> Type of data
     * @return CommonResult with success status
     */
    public static <T> CommonResult<T> succeeded(T data) {
        return new CommonResult<T>(
            ResultCodeEnum.SUCCEEDED.getStatus(), 
            ResultCodeEnum.SUCCEEDED.getCode(),
            ResultCodeEnum.SUCCEEDED.getMessage(),
            data
        );
    }
    
    /**
     * Return successful result with data and custom message
     * @param data Result data
     * @param message Custom message
     * @param <T> Type of data
     * @return CommonResult with success status
     */
    public static <T> CommonResult<T> succeeded(T data, String message) {
        return new CommonResult<T>(
                ResultCodeEnum.SUCCEEDED.getStatus(),
                ResultCodeEnum.SUCCEEDED.getCode(),
                message,
                data
        );
    }

    public static <T> CommonResult<T> succeeded(T data,long code,String message) {
        return new CommonResult<T>(
                ResultCodeEnum.SUCCEEDED.getStatus(),
                code,
                message,
                data
        );
    }

    /**
     * Return failed result with error code
     * @param errCode Error code
     * @param <T> Type of data
     * @return CommonResult with failure status
     */
    public static <T> CommonResult<T> failed(IErrorCode errCode) {
        return new CommonResult<T>(
            errCode.getStatus(), 
            errCode.getCode(), 
            errCode.getMessage(), 
            null
        );
    }

    /**
     * Return failed result with custom message
     * @param message Custom error message
     * @param <T> Type of data
     * @return CommonResult with failure status
     */
    public static <T> CommonResult<T> failed(String message) {
        return new CommonResult<T>(
            ResultCodeEnum.FAILED.getStatus(), 
            ResultCodeEnum.FAILED.getCode(), 
            message, 
            null
        );
    }

    /**
     * Return failed result with custom code and message
     * @param code Custom error code
     * @param message Custom error message
     * @param <T> Type of data
     * @return CommonResult with failure status
     */
    public static <T> CommonResult<T> failed(long code, String message) {
        return new CommonResult<T>(
            ResultCodeEnum.FAILED.getStatus(), 
            code, 
            message, 
            null
        );
    }

    /**
     * Return failed result with data and custom message
     * @param data Result data
     * @param message Custom error message
     * @param <T> Type of data
     * @return CommonResult with failure status
     */
    public static <T> CommonResult<T> failed(T data, String message) {
        return new CommonResult<T>(
            ResultCodeEnum.FAILED.getStatus(),
            ResultCodeEnum.FAILED.getCode(),
            message,
            data
        );
    }

    /**
     * Return failed result with default error
     * @param <T> Type of data
     * @return CommonResult with failure status
     */
    public static <T> CommonResult<T> failed() {
        return failed(ResultCodeEnum.FAILED);
    }

    /**
     * Return validation failed result
     * @param <T> Type of data
     * @return CommonResult with validation failure status
     */
    public static <T> CommonResult<T> validateFailed() {
        return failed(ResultCodeEnum.VALIDATION_FAILED);
    }

    /**
     * Return validation failed result with custom message
     * @param message Custom validation error message
     * @param <T> Type of data
     * @return CommonResult with validation failure status
     */
    public static <T> CommonResult<T> validateFailed(String message) {
        return new CommonResult<T>(
            ResultCodeEnum.VALIDATION_FAILED.getStatus(),
            ResultCodeEnum.VALIDATION_FAILED.getCode(),
            message,
            null
        );
    }

    /**
     * Return unauthorized result
     * @param <T> Type of data
     * @return CommonResult with unauthorized status
     */
    public static <T> CommonResult<T> unauthorized() {
        return failed(ResultCodeEnum.AUTHORIZED_FAILED);
    }

    /**
     * Return unauthorized result with custom message
     * @param message Custom unauthorized message
     * @param <T> Type of data
     * @return CommonResult with unauthorized status
     */
    public static <T> CommonResult<T> unauthorized(String message) {
        return new CommonResult<T>(
            ResultCodeEnum.AUTHORIZED_FAILED.getStatus(),
            ResultCodeEnum.AUTHORIZED_FAILED.getCode(),
            message,
            null
        );
    }

    /**
     * Return forbidden result
     * @param <T> Type of data
     * @return CommonResult with forbidden status
     */
    public static <T> CommonResult<T> forbidden() {
        return failed(ResultCodeEnum.PRIVILEDGE_FORBIDDEN);
    }

    /**
     * Return forbidden result with custom message
     * @param message Custom forbidden message
     * @param <T> Type of data
     * @return CommonResult with forbidden status
     */
    public static <T> CommonResult<T> forbidden(String message) {
        return new CommonResult<T>(
            ResultCodeEnum.PRIVILEDGE_FORBIDDEN.getStatus(),
            ResultCodeEnum.PRIVILEDGE_FORBIDDEN.getCode(),
            message,
            null
        );
    }

    // Getters and setters
    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public long getCode() {
        return code;
    }

    public void setCode(long code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
