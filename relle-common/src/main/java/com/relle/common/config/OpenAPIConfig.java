package com.relle.common.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * OpenAPI configuration for API documentation
 * This is a common configuration that can be customized by overriding methods
 */
@Profile({"dev"})
@Configuration
public class OpenAPIConfig {

    /**
     * API title - should be overridden by subclasses
     * @return API title
     */
    protected String getApiTitle() {
        return "Relle API";
    }

    /**
     * API description - can be overridden by subclasses
     * @return API description
     */
    protected String getApiDescription() {
        return "接口文档";
    }

    /**
     * API version - can be overridden by subclasses
     * @return API version
     */
    protected String getApiVersion() {
        return "1.0";
    }

    /**
     * Default token value - can be overridden by subclasses
     * @return default token value
     */
    protected String getDefaultToken() {
        return "";
    }

    /**
     * Create a GroupedOpenApi bean for all API endpoints
     * @return GroupedOpenApi instance
     */
    @Bean
    public GroupedOpenApi allApi() {
        return GroupedOpenApi.builder()
                .group("all")
                .pathsToMatch("/**")
                .packagesToScan("com.relle.controller", "com.relle.common.controller")
                .build();
    }

    /**
     * Create a GroupedOpenApi bean for account API endpoints
     * @return GroupedOpenApi instance
     */
    @Bean
    public GroupedOpenApi accountApi() {
        return GroupedOpenApi.builder()
                .group("account")
                .pathsToMatch("/account/**")
                .packagesToScan("com.relle.controller")
                .build();
    }

    /**
     * Create a GroupedOpenApi bean for management API endpoints
     * @return GroupedOpenApi instance
     */
    @Bean
    public GroupedOpenApi managementApi() {
        return GroupedOpenApi.builder()
                .group("management")
                .pathsToMatch("/manage/**")
                .packagesToScan("com.relle.controller")
                .build();
    }

    /**
     * Create a GroupedOpenApi bean for service API endpoints
     * @return GroupedOpenApi instance
     */
    @Bean
    public GroupedOpenApi serviceApi() {
        return GroupedOpenApi.builder()
                .group("service")
                .pathsToMatch("/service/**")
                .packagesToScan("com.relle.controller")
                .build();
    }

    /**
     * Create a custom OpenAPI configuration
     * @return OpenAPI instance
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(getApiTitle())
                        .description(getApiDescription())
                        .version(getApiVersion())
                        .license(new License().name("Relle License")))
                .components(new Components()
                        .addSecuritySchemes("Authorization", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .in(SecurityScheme.In.HEADER)
                                .name("Authorization")))
                .addSecurityItem(new SecurityRequirement().addList("Authorization"));
    }
}
