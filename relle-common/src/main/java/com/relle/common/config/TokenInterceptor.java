package com.relle.common.config;

import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.relle.common.api.CommonResult;
import com.relle.common.api.ResultCodeEnum;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Component
public class TokenInterceptor implements HandlerInterceptor {
    @Resource ObjectMapper objectMapper;

    private static final Logger logger = LoggerFactory.getLogger(TokenInterceptor.class);
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();

        // Skip token validation for OPTIONS requests (pre-flight CORS requests)
        if(request.getMethod().equals(RequestMethod.OPTIONS.name())){
            logger.debug("Skipping token validation for OPTIONS request: {}", requestURI);
            return true;
        }

        // Skip token validation for API documentation paths
        if (isApiDocumentationPath(requestURI)) {
            logger.debug("Skipping token validation for API documentation path: {}", requestURI);
            return true;
        }

        // Get the Authorization header
        String token = request.getHeader("Authorization");

        // Check if token is null or empty
        if (token == null || token.isEmpty()) {
            logger.warn("Missing Authorization token for path: {}", requestURI);
            CommonResult<?> result = CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
            returnJson(response, objectMapper.writeValueAsString(result));
            return false;
        }

        CommonResult<?> commonResult = null;
        try {
            // 1.校验JWT字符串
            // Using JWT.decode directly since our common JWTUtil doesn't have decodeToken method
            DecodedJWT decodedJWT = com.auth0.jwt.JWT.decode(token);
            // 2.取出JWT字符串载荷中的随机token，从Redis中获取用户信息

            Claim unionid = decodedJWT.getClaim("unionid");
            Claim openid = decodedJWT.getClaim("openid");
            logger.debug("TokenInterceptor processing token for unionid: {}", unionid.asString());
            request.getSession().setAttribute("unionid", unionid.asString());
            request.getSession().setAttribute("openid", openid.asString());

            return true;
        }catch (SignatureVerificationException e){
            logger.warn("Invalid token signature", e);
            commonResult = CommonResult.failed(ResultCodeEnum.GET_TOKEN_KEY_FAILED);
        }catch (TokenExpiredException e){
            logger.warn("Token has expired", e);
            commonResult = CommonResult.failed(ResultCodeEnum.AUTHORIZED_FAILED);
        }catch (AlgorithmMismatchException e){
            logger.warn("Algorithm mismatch in token", e);
            commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        }catch (Exception e){
            logger.warn("Invalid token", e);
            commonResult = CommonResult.failed(ResultCodeEnum.JWT_TOKEN_EXPIRE);
        }
        returnJson(response, objectMapper.writeValueAsString(commonResult));
        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }

    /**
     * Check if the given path is an API documentation path or test endpoint that should be excluded from token validation
     * @param path The request path
     * @return true if the path is an API documentation path or test endpoint, false otherwise
     */
    private boolean isApiDocumentationPath(String path) {
        return path != null && (
            // API documentation paths
            path.startsWith("/v2/api-docs") ||
            path.startsWith("/v3/api-docs") ||
            path.startsWith("/swagger-ui") ||
            path.startsWith("/swagger-resources") ||
            path.startsWith("/webjars/swagger-ui") ||
            path.equals("/swagger-ui.html") ||

            // Test endpoints
            path.startsWith("/api/test/")
        );
    }

    /**
     * Write JSON response to the client
     * @param response The HTTP response
     * @param json The JSON string to write
     * @throws Exception If an error occurs
     */
    private void returnJson(HttpServletResponse response, String json) throws Exception {
        PrintWriter writer = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try {
            writer = response.getWriter();
            writer.print(json);
        } catch (IOException e) {
            logger.error("Error writing JSON response", e);
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }
}
