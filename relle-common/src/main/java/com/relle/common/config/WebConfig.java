package com.relle.common.config;

import javax.annotation.Resource;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Common Web configuration for all modules
 * This class provides base configuration for web resources, CORS, and interceptors
 * Modules can extend this class and override methods as needed
 *
 * Use @ConditionalOnMissingBean to allow modules to override this configuration
 */
@Configuration
@ConditionalOnMissingBean(name = "webConfig")
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private TokenInterceptor tokenInterceptor;

    /**
     * Get the paths to exclude from interceptors
     * @return array of paths to exclude
     */
    protected String[] getExcludePaths() {
        return new String[]{
            // Authentication paths
            "/login/**",

            // Test endpoints (for Swagger UI testing)
            "/api/test/**",
            "/test/**",

            // Static resources
            "/*.txt",
            "/html/**",
            "*.html",

            // Swagger 2 (legacy)
            "/v2/api-docs",
            "/v2/api-docs/**",
            "/swagger-resources",
            "/swagger-resources/**",
            "/configuration/ui",
            "/configuration/security",

            // SpringDoc OpenAPI 3
            "/v3/api-docs",
            "/v3/api-docs/**",
            "/v3/api-docs.yaml",
            "/swagger-ui",
            "/swagger-ui.html",
            "/swagger-ui/**",

            // Common resources
            "/webjars/**"
        };
    }

    /**
     * Add interceptors to the registry
     * This method should be overridden by subclasses to add specific interceptors
     * @param registry the interceptor registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // Log the excluded paths for debugging
        String[] excludePaths = getExcludePaths();
        if (excludePaths != null && excludePaths.length > 0) {
            System.out.println("Excluding the following paths from token validation:");
            for (String path : excludePaths) {
                System.out.println("  - " + path);
            }
        }

        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(excludePaths);
        // Subclasses should override this method to add specific interceptors
    }

    /**
     * Add resource handlers for static resources and API documentation
     * @param registry the resource handler registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Resolve static resources
        registry.addResourceHandler("/html/**")
                .addResourceLocations("classpath:/html/");

        // Resolve Swagger UI (legacy)
        registry.addResourceHandler("/swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/configuration/ui")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/configuration/security")
                .addResourceLocations("classpath:/META-INF/resources/");

        // Resolve SpringDoc Swagger UI
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/");

        // Resolve Swagger and SpringDoc JS files
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

        // Resolve Swagger resources
        registry.addResourceHandler("/swagger-resources/**")
                .addResourceLocations("classpath:/META-INF/resources/swagger-resources/");

        // Resolve API docs
        registry.addResourceHandler("/v2/api-docs/**")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/v3/api-docs/**")
                .addResourceLocations("classpath:/META-INF/resources/");
    }

    /**
     * Configure CORS mappings
     * @param registry the CORS registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowCredentials(true)
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .maxAge(3600 * 24);
    }
}
