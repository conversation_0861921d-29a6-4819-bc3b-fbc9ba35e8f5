package com.relle.common.config;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * Utility class to help with the migration from Swagger to SpringDoc OpenAPI
 * This class provides methods to print migration guides for Swagger annotations
 */
public class SwaggerToOpenAPIConverter {

    /**
     * Print migration guide for Swagger @Api annotation to SpringDoc @Tag annotation
     * @param api Swagger @Api annotation
     * @return Migration guide string
     */
    public static String getApiToTagMigrationGuide(Api api) {
        if (api == null) {
            return "";
        }

        String tagName = api.tags().length > 0 ? api.tags()[0] : "";
        String description = api.value();

        return "@Api(tags = \"" + tagName + "\", value = \"" + description + "\")\n" +
               "should be replaced with:\n" +
               "@Tag(name = \"" + tagName + "\", description = \"" + description + "\")";
    }

    /**
     * Print migration guide for Swagger @ApiOperation annotation to SpringDoc @Operation annotation
     * @param apiOperation Swagger @ApiOperation annotation
     * @return Migration guide string
     */
    public static String getApiOperationToOperationMigrationGuide(ApiOperation apiOperation) {
        if (apiOperation == null) {
            return "";
        }

        String summary = apiOperation.value();
        String description = apiOperation.notes();

        return "@ApiOperation(value = \"" + summary + "\", notes = \"" + description + "\")\n" +
               "should be replaced with:\n" +
               "@Operation(summary = \"" + summary + "\", description = \"" + description + "\")";
    }

    /**
     * Print migration guide for Swagger @ApiParam annotation to SpringDoc @Parameter annotation
     * @param apiParam Swagger @ApiParam annotation
     * @return Migration guide string
     */
    public static String getApiParamToParameterMigrationGuide(ApiParam apiParam) {
        if (apiParam == null) {
            return "";
        }

        String name = apiParam.name();
        String description = apiParam.value();
        boolean required = apiParam.required();

        return "@ApiParam(name = \"" + name + "\", value = \"" + description + "\", required = " + required + ")\n" +
               "should be replaced with:\n" +
               "@Parameter(name = \"" + name + "\", description = \"" + description + "\", required = " + required + ")";
    }

    /**
     * Print migration guide for all controllers in the application
     * @param requestMappingHandlerMapping Spring's RequestMappingHandlerMapping
     */
    public static void printMigrationGuide(RequestMappingHandlerMapping requestMappingHandlerMapping) {
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();

        System.out.println("=== Swagger to SpringDoc OpenAPI Migration Guide ===");

        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
            HandlerMethod handlerMethod = entry.getValue();
            Class<?> controllerClass = handlerMethod.getBeanType();
            Method method = handlerMethod.getMethod();

            Api apiAnnotation = AnnotationUtils.findAnnotation(controllerClass, Api.class);
            if (apiAnnotation != null) {
                System.out.println("Controller: " + controllerClass.getName());
                System.out.println(getApiToTagMigrationGuide(apiAnnotation));
                System.out.println();
            }

            ApiOperation apiOperationAnnotation = AnnotationUtils.findAnnotation(method, ApiOperation.class);
            if (apiOperationAnnotation != null) {
                System.out.println("Method: " + controllerClass.getName() + "." + method.getName());
                System.out.println(getApiOperationToOperationMigrationGuide(apiOperationAnnotation));
                System.out.println();
            }
        }

        System.out.println("=== End of Migration Guide ===");
    }
}
