package com.relle.common.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * MyBatis configuration for all modules
 * This class provides common MyBatis configuration
 * Modules can extend this class and add their own MapperScan annotations
 *
 * This configuration is disabled in the 'all' profile to avoid conflicts
 * with the uber JAR configuration.
 */
@Configuration
@MapperScan("com.relle.mbg.mapper")
@Profile("!all") // Disable this configuration in the 'all' profile
public class MybatisConfig {
    // This class can be extended by module-specific configurations
    // to add additional MapperScan annotations
}
